{"private": true, "scripts": {"dev": "cross-env NODE_ENV=development webpack --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "cross-env NODE_ENV=development webpack --watch --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js"}, "devDependencies": {"axios": "^0.19.2", "cross-env": "^6.0.3", "deepmerge": "^4.2.2", "laravel-mix": "^5.0.0", "sass": "^1.30.0", "sass-loader": "^10.1.0", "vue": "^2.6.10", "vue-loader": "^15.7.2", "vue-template-compiler": "^2.6.10", "vuetify-loader": "^1.6.0", "webpack": "^4.41.2", "webpack-cli": "^3.3.10"}, "dependencies": {"@linways/table-to-excel": "^1.0.4", "@mdi/font": "^5.8.55", "apexcharts": "^3.22.3", "babel-loader": "^8.2.2", "table-to-excel": "^1.0.6", "tiptap": "^1.32.1", "tiptap-extensions": "^1.35.1", "vue-apexcharts": "^1.6.0", "vue-jstree": "^2.1.6", "vue-multiselect": "^2.1.6", "vue-router": "^3.4.9", "vue-select": "^3.10.8", "vuedraggable": "^2.24.3", "vuetify": "^2.4.0", "vuetifyjs-mix-extension": "0.0.17", "vuex": "^3.5.1"}}