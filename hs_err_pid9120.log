#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 469762048 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3829), pid=9120, tid=34792
#
# JRE version:  (********+7) (build )
# Java VM: OpenJDK 64-Bit Server VM (********+7-b1000.32, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://github.com': 

Host: AMD Ryzen 7 5700G with Radeon Graphics         , 16 cores, 27G,  Windows 10 , 64 bit Build 19041 (10.0.19041.3570)
Time: Wed Dec 25 05:44:30 2024  Windows 10 , 64 bit Build 19041 (10.0.19041.3570) elapsed time: 0.019466 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x0000016eccd3c570):  JavaThread "Unknown thread" [_thread_in_vm, id=34792, stack(0x0000004319d00000,0x0000004319e00000)]

Stack: [0x0000004319d00000,0x0000004319e00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6852da]
V  [jvm.dll+0x844c84]
V  [jvm.dll+0x84659e]
V  [jvm.dll+0x846c03]
V  [jvm.dll+0x24b7cf]
V  [jvm.dll+0x682049]
V  [jvm.dll+0x67671a]
V  [jvm.dll+0x30be2b]
V  [jvm.dll+0x3132d6]
V  [jvm.dll+0x36302e]
V  [jvm.dll+0x36325f]
V  [jvm.dll+0x2e1f48]
V  [jvm.dll+0x2e2eb4]
V  [jvm.dll+0x815c11]
V  [jvm.dll+0x370df1]
V  [jvm.dll+0x7f4fdc]
V  [jvm.dll+0x3f3f1f]
V  [jvm.dll+0x3f5b31]
C  [jli.dll+0x528f]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17344]
C  [ntdll.dll+0x526b1]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffbb78e00d8, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x0000016eccdd9d50 GCTaskThread "GC Thread#0" [stack: 0x0000004319e00000,0x0000004319f00000] [id=39372]
  0x0000016eccde8bf0 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000004319f00000,0x000000431a000000] [id=24100]
  0x0000016eccde9da0 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000431a000000,0x000000431a100000] [id=24096]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffbb7092ab7]

VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x0000016eccd36e00] Heap_lock - owner thread: 0x0000016eccd3c570

Heap address: 0x0000000641400000, size: 7148 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
<Empty>

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000641400000, 0x0000000800000000)
  region size 4096K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)

Card table byte_map: [0x0000016ee0b40000,0x0000016ee1940000] _byte_map_base: 0x0000016edd936000

Marking Bits (Prev, Next): (CMBitMap*) 0x0000016eccdda370, (CMBitMap*) 0x0000016eccdda3b0
 Prev Bits: [0x0000016ee2740000, 0x0000016ee96f0000)
 Next Bits: [0x0000016ee96f0000, 0x0000016ef06a0000)

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.005 Loaded shared library C:\Program Files\JetBrains\PhpStorm 2023.2.3\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

VM Operations (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff61c950000 - 0x00007ff61c95a000 	C:\Program Files\JetBrains\PhpStorm 2023.2.3\jbr\bin\java.exe
0x00007ffc00ad0000 - 0x00007ffc00cc7000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffbfec10000 - 0x00007ffbfeccd000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffbfe780000 - 0x00007ffbfea76000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffbfe380000 - 0x00007ffbfe480000 	C:\Windows\System32\ucrtbase.dll
0x00007ffbe8c20000 - 0x00007ffbe8c37000 	C:\Program Files\JetBrains\PhpStorm 2023.2.3\jbr\bin\jli.dll
0x00007ffbe9240000 - 0x00007ffbe925b000 	C:\Program Files\JetBrains\PhpStorm 2023.2.3\jbr\bin\VCRUNTIME140.dll
0x00007ffbea4e0000 - 0x00007ffbea77a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.3570_none_60bb2a3971f3e41a\COMCTL32.dll
0x00007ffbffab0000 - 0x00007ffbffc4e000 	C:\Windows\System32\USER32.dll
0x00007ffc00940000 - 0x00007ffc009de000 	C:\Windows\System32\msvcrt.dll
0x00007ffbfe700000 - 0x00007ffbfe722000 	C:\Windows\System32\win32u.dll
0x00007ffc008f0000 - 0x00007ffc0091c000 	C:\Windows\System32\GDI32.dll
0x00007ffbfe480000 - 0x00007ffbfe59a000 	C:\Windows\System32\gdi32full.dll
0x00007ffbfe660000 - 0x00007ffbfe6fd000 	C:\Windows\System32\msvcp_win.dll
0x00007ffbfebe0000 - 0x00007ffbfec10000 	C:\Windows\System32\IMM32.DLL
0x00007ffbed3f0000 - 0x00007ffbed3fc000 	C:\Program Files\JetBrains\PhpStorm 2023.2.3\jbr\bin\vcruntime140_1.dll
0x00007ffbdf770000 - 0x00007ffbdf7fd000 	C:\Program Files\JetBrains\PhpStorm 2023.2.3\jbr\bin\msvcp140.dll
0x00007ffbb6da0000 - 0x00007ffbb7a23000 	C:\Program Files\JetBrains\PhpStorm 2023.2.3\jbr\bin\server\jvm.dll
0x00007ffbfeb30000 - 0x00007ffbfebde000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffbfede0000 - 0x00007ffbfee7c000 	C:\Windows\System32\sechost.dll
0x00007ffc002b0000 - 0x00007ffc003d6000 	C:\Windows\System32\RPCRT4.dll
0x00007ffbea4b0000 - 0x00007ffbea4b9000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ffbf3650000 - 0x00007ffbf3677000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffbf52a0000 - 0x00007ffbf52aa000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffbffc50000 - 0x00007ffbffcbb000 	C:\Windows\System32\WS2_32.dll
0x00007ffbfe010000 - 0x00007ffbfe05b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffbfdff0000 - 0x00007ffbfe002000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffbfbf50000 - 0x00007ffbfbf62000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffbed3d0000 - 0x00007ffbed3da000 	C:\Program Files\JetBrains\PhpStorm 2023.2.3\jbr\bin\jimage.dll
0x00007ffbf66e0000 - 0x00007ffbf68c4000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffbe4a90000 - 0x00007ffbe4ac4000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffbfe5d0000 - 0x00007ffbfe652000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffbe54e0000 - 0x00007ffbe5505000 	C:\Program Files\JetBrains\PhpStorm 2023.2.3\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\JetBrains\PhpStorm 2023.2.3\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.3570_none_60bb2a3971f3e41a;C:\Program Files\JetBrains\PhpStorm 2023.2.3\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://github.com': 
java_class_path (initial): C:/Program Files/JetBrains/PhpStorm 2023.2.3/plugins/vcs-git/lib/git4idea-rt.jar;C:/Program Files/JetBrains/PhpStorm 2023.2.3/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 469762048                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 7495221248                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 7495221248                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
PATH=C:/Program Files/Git/mingw64/libexec/git-core;C:/Program Files/Git/mingw64/libexec/git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files (x86)\VMware\VMware Player\bin\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\WinSCP\;C:\Program Files\Git\cmd;C:\Program Files\PuTTY\;C:\Program Files\dotnet\;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PhpStorm 2023.2.3\bin;C:\Program Files\JetBrains\PyCharm 2023.2.1\bin;C:\Users\<USER>\AppData\Local\gitkraken\bin
USERNAME=user
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 25 Model 80 Stepping 0, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp


JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

OOME stack traces (most recent first):
Classloader memory used:


---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.3570)
OS uptime: 45 days 7:29 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (16 cores per cpu, 2 threads per core) family 25 model 80 stepping 0 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, hv
Processor Information for all 16 processors :
  Max Mhz: 3800, Current Mhz: 3800, Mhz Limit: 3800

Memory: 4k page, system-wide physical 28583M (1292M free)
TotalPageFile size 59073M (AvailPageFile size 65M)
current process WorkingSet (physical memory assigned to process): 10M, peak: 10M
current process commit charge ("private bytes"): 70M, peak: 518M

vm_info: OpenJDK 64-Bit Server VM (********+7-b1000.32) for windows-amd64 JRE (********+7-b1000.32), built on 2023-09-15 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
