<?php

namespace tests\integration;

use common\chip\event\DispatchEventJob;
use common\chip\event\EventHandler;
use common\chip\event\project\ProjectCreatedEvent;
use Yii;
use yii\queue\file\Queue;

/**
 * Тесты для проверки механизма повторных попыток
 */
class RetryMechanismTest extends NotificationSystemTest
{
    /**
     * @var Queue
     */
    protected $testQueue;
    
    /**
     * @var array
     */
    protected $pushedJobs = [];
    
    /**
     * @var int
     */
    protected $attemptCount = 0;
    
    /**
     * Настройка перед каждым тестом
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Создаем тестовую очередь
        $this->testQueue = $this->getMockBuilder(Queue::class)
            ->getMock();
        
        // Очищаем список отправленных задач
        $this->pushedJobs = [];
        
        // Сбрасываем счетчик попыток
        $this->attemptCount = 0;
        
        // Устанавливаем функцию для отправки в очередь
        $this->eventDispatcher->setQueuePusher(function ($job) {
            $this->pushedJobs[] = $job;
            return count($this->pushedJobs);
        });
    }
    
    /**
     * Тест механизма повторных попыток при ошибке
     */
    public function testRetryOnError()
    {
        // Создаем обработчик, который выбрасывает исключение
        $handler = $this->getMockBuilder(EventHandler::class)
            ->getMock();
        
        $handler->method('canHandle')
            ->willReturn(true);
        
        // Обработчик будет выбрасывать исключение при первых двух вызовах
        // и успешно выполняться при третьем вызове
        $handler->method('handle')
            ->will($this->returnCallback(function () {
                $this->attemptCount++;
                if ($this->attemptCount < 3) {
                    throw new \Exception("Test exception, attempt {$this->attemptCount}");
                }
                return null;
            }));
        
        // Регистрируем обработчик в контейнере
        Yii::$container->set(EventHandler::class, function () use ($handler) {
            return $handler;
        });
        
        // Регистрируем диспетчер в контейнере
        Yii::$container->set('common\chip\event\EventDispatcher', $this->eventDispatcher);
        
        // Регистрируем репозиторий в контейнере
        Yii::$container->set('common\chip\event\repositories\EventRepository', $this->eventRepository);
        
        // Настраиваем очередь для повторной отправки задачи
        $this->testQueue->expects($this->exactly(2))
            ->method('delay')
            ->willReturnSelf();
        
        $this->testQueue->expects($this->exactly(2))
            ->method('push')
            ->will($this->returnCallback(function ($job) {
                // Имитируем повторное выполнение задачи
                $job->execute($this->testQueue);
                return true;
            }));
        
        // Создаем событие
        $event = $this->createProjectCreatedEvent();
        
        // Отправляем событие асинхронно
        $this->eventDispatcher->dispatchAsync($event, 'project', $this->testProject->id);
        
        // Получаем задачу
        $job = $this->pushedJobs[0];
        
        // Выполняем задачу первый раз
        $job->execute($this->testQueue);
        
        // Проверяем, что было 3 попытки
        $this->assertEquals(3, $this->attemptCount, 'Должно быть 3 попытки выполнения');
        
        // Проверяем, что событие было записано в журнал
        $eventLog = $this->assertEventLogged($event, 'project', $this->testProject->id);
        
        // Проверяем, что событие было отмечено как обработанное
        $this->assertTrue($eventLog->processed, 'Событие не было отмечено как обработанное');
        
        // Проверяем, что были записаны логи обработчиков
        $this->assertNotNull($eventLog->handlerLogs, 'Не записаны логи обработчиков');
        $this->assertGreaterThanOrEqual(3, count($eventLog->handlerLogs), 'Должно быть не менее 3 логов обработчика');
        
        // Проверяем, что первые две попытки были неуспешными, а третья успешной
        $successCount = 0;
        $errorCount = 0;
        foreach ($eventLog->handlerLogs as $handlerLog) {
            if ($handlerLog->is_success) {
                $successCount++;
            } else {
                $errorCount++;
                $this->assertStringContainsString('Test exception', $handlerLog->error_message, 'Сообщение об ошибке не содержит текст исключения');
            }
        }
        
        $this->assertGreaterThanOrEqual(1, $successCount, 'Должна быть хотя бы одна успешная попытка');
        $this->assertGreaterThanOrEqual(2, $errorCount, 'Должно быть не менее двух неуспешных попыток');
    }
    
    /**
     * Тест экспоненциального увеличения задержки между попытками
     */
    public function testExponentialBackoff()
    {
        // Создаем обработчик, который выбрасывает исключение
        $handler = $this->getMockBuilder(EventHandler::class)
            ->getMock();
        
        $handler->method('canHandle')
            ->willReturn(true);
        
        // Обработчик будет выбрасывать исключение при каждом вызове
        $handler->method('handle')
            ->will($this->returnCallback(function () {
                $this->attemptCount++;
                throw new \Exception("Test exception, attempt {$this->attemptCount}");
            }));
        
        // Регистрируем обработчик в контейнере
        Yii::$container->set(EventHandler::class, function () use ($handler) {
            return $handler;
        });
        
        // Регистрируем диспетчер в контейнере
        Yii::$container->set('common\chip\event\EventDispatcher', $this->eventDispatcher);
        
        // Регистрируем репозиторий в контейнере
        Yii::$container->set('common\chip\event\repositories\EventRepository', $this->eventRepository);
        
        // Создаем событие
        $event = $this->createProjectCreatedEvent();
        
        // Отправляем событие асинхронно
        $this->eventDispatcher->dispatchAsync($event, 'project', $this->testProject->id);
        
        // Получаем задачу
        $job = $this->pushedJobs[0];
        
        // Настраиваем ожидания для очереди
        $expectedDelays = [5, 25]; // 5, 5^2
        
        $this->testQueue->expects($this->exactly(2))
            ->method('delay')
            ->withConsecutive(
                [$this->equalTo($expectedDelays[0])],
                [$this->equalTo($expectedDelays[1])]
            )
            ->willReturnSelf();
        
        $this->testQueue->expects($this->exactly(2))
            ->method('push')
            ->willReturn(true);
        
        // Выполняем задачу первый раз
        $job->execute($this->testQueue);
        
        // Проверяем, что была одна попытка
        $this->assertEquals(1, $this->attemptCount, 'Должна быть 1 попытка выполнения');
        
        // Имитируем вторую попытку
        $job->attempt = 1;
        $job->execute($this->testQueue);
        
        // Проверяем, что было две попытки
        $this->assertEquals(2, $this->attemptCount, 'Должно быть 2 попытки выполнения');
        
        // Имитируем третью попытку
        $job->attempt = 2;
        $job->execute($this->testQueue);
        
        // Проверяем, что было три попытки
        $this->assertEquals(3, $this->attemptCount, 'Должно быть 3 попытки выполнения');
    }
}
