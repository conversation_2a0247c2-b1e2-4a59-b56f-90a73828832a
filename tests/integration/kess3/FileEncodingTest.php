<?php

declare(strict_types=1);

namespace tests\integration\kess3;

use PHPUnit\Framework\TestCase;
use common\chip\externalIntegrations\kess3\Infrastructure\Service\ModernEncodingService;
use common\chip\externalIntegrations\kess3\Infrastructure\Adapter\AlientechEncodingAdapter;
use common\chip\externalIntegrations\kess3\Infrastructure\DTO\EncodingRequestDto;

class FileEncodingTest extends TestCase
{
    public function testFullEncodingProcess(): void
    {
        $adapter = new AlientechEncodingAdapter();
        $service = new ModernEncodingService($adapter);

        $request = new EncodingRequestDto(
            projectId: 1,
            fileName: 'testfile.bin',
            filePath: '/tmp/testfile.bin'
        );

        $result = $service->encode($request);

        $this->assertTrue($result->success);
        $this->assertNotNull($result->encodedFilePath);
        $this->assertNull($result->error);
    }
}
