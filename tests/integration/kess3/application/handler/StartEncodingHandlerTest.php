<?php

declare(strict_types=1);

namespace tests\integration\kess3\application\handler;

use common\chip\externalIntegrations\kess3\Application\Command\StartEncodingCommand;
use common\chip\externalIntegrations\kess3\Application\Handler\StartEncodingHandler;
use common\chip\externalIntegrations\kess3\Domain\Entity\EncodingOperation;
use common\chip\externalIntegrations\kess3\Domain\Repository\EncodingOperationRepositoryInterface;
use common\chip\externalIntegrations\kess3\Domain\Service\EncodingDomainService;
use common\chip\externalIntegrations\kess3\Infrastructure\Factory\EncodingAdapterFactory;
use common\chip\event\EventDispatcher;
use common\models\ProjectFiles;
use common\models\Projects;
use PHPUnit\Framework\TestCase;
use Yii;

/**
 * Интеграционные тесты для StartEncodingHandler
 */
final class StartEncodingHandlerTest extends TestCase
{
    private StartEncodingHandler $handler;
    private EncodingOperationRepositoryInterface $repository;
    private int $testProjectId;

    protected function setUp(): void
    {
        $this->repository = Yii::$container->get(EncodingOperationRepositoryInterface::class);
        $domainService = Yii::$container->get(EncodingDomainService::class);
        $adapterFactory = Yii::$container->get(EncodingAdapterFactory::class);
        $eventDispatcher = Yii::$container->get(EventDispatcher::class);

        $this->handler = new StartEncodingHandler(
            $domainService,
            $this->repository,
            $adapterFactory,
            $eventDispatcher
        );

        $this->testProjectId = $this->createTestProject();
    }

    protected function tearDown(): void
    {
        $this->cleanupTestData();
    }

    public function testHandleStartEncoding(): void
    {
        // Создаем тестовые файлы проекта
        $this->createTestProjectFiles($this->testProjectId);

        $command = StartEncodingCommand::create(
            projectId: $this->testProjectId,
            service: 'custom',
            callbackUrl: 'https://test.example.com/callback'
        );

        $operation = $this->handler->handle($command);

        $this->assertInstanceOf(EncodingOperation::class, $operation);
        $this->assertEquals($this->testProjectId, $operation->getProjectId()->getValue());
        $this->assertTrue($operation->getStatus()->isInProgress());
        $this->assertNotEmpty($operation->getFileIds());
        $this->assertEquals('https://test.example.com/callback', $operation->getCallbackUrl());
        $this->assertTrue($operation->isStarted());
    }

    public function testHandleWithNoFiles(): void
    {
        $command = StartEncodingCommand::create(
            projectId: $this->testProjectId,
            service: 'custom'
        );

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('No files found for encoding');

        $this->handler->handle($command);
    }

    public function testHandleWithAlientechService(): void
    {
        $this->createTestProjectFiles($this->testProjectId);

        $command = StartEncodingCommand::create(
            projectId: $this->testProjectId,
            service: 'alientech'
        );

        $operation = $this->handler->handle($command);

        $this->assertInstanceOf(EncodingOperation::class, $operation);
        $this->assertTrue($operation->isStarted());
        $this->assertNotNull($operation->getExternalOperationId());
    }

    public function testHandleWithBoschService(): void
    {
        $this->createTestProjectFiles($this->testProjectId);

        $command = StartEncodingCommand::create(
            projectId: $this->testProjectId,
            service: 'bosch'
        );

        // Bosch адаптер пока не реализован, должен вернуть ошибку
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Failed to start encoding operation');

        $this->handler->handle($command);
    }

    public function testHandleWithInvalidService(): void
    {
        $this->createTestProjectFiles($this->testProjectId);

        $command = StartEncodingCommand::create(
            projectId: $this->testProjectId,
            service: 'invalid-service'
        );

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Failed to start encoding operation');

        $this->handler->handle($command);
    }

    public function testHandleCreatesOperationInRepository(): void
    {
        $this->createTestProjectFiles($this->testProjectId);

        $command = StartEncodingCommand::create(
            projectId: $this->testProjectId,
            service: 'custom'
        );

        $operation = $this->handler->handle($command);

        // Проверяем, что операция сохранена в репозитории
        $savedOperation = $this->repository->findById($operation->getOperationId());
        $this->assertNotNull($savedOperation);
        $this->assertEquals($operation->getOperationId(), $savedOperation->getOperationId());
    }

    public function testHandleGeneratesCallbackUrl(): void
    {
        $this->createTestProjectFiles($this->testProjectId);

        $command = StartEncodingCommand::create(
            projectId: $this->testProjectId,
            service: 'custom'
            // callbackUrl не указан
        );

        $operation = $this->handler->handle($command);

        // Должен быть сгенерирован callback URL
        $this->assertNotEmpty($operation->getCallbackUrl());
        $this->assertStringContains('/api/kess3-encoded', $operation->getCallbackUrl());
    }

    public function testHandleSetUserInfo(): void
    {
        $this->createTestProjectFiles($this->testProjectId);

        $command = StartEncodingCommand::create(
            projectId: $this->testProjectId,
            service: 'custom'
        );

        $operation = $this->handler->handle($command);

        $userInfo = $operation->getUserInfo();
        $this->assertNotNull($userInfo);
        $this->assertEquals($this->testProjectId, $userInfo['projectId']);
        $this->assertNotEmpty($userInfo['fileIds']);
        $this->assertEquals($operation->getOperationId()->getValue(), $userInfo['operationId']);
    }

    private function createTestProject(): int
    {
        $project = new Projects();
        $project->title = 'Test Project for Encoding Handler';
        $project->status = 'decoded';
        $project->created_on = date('Y-m-d H:i:s');
        $project->save();

        return $project->id;
    }

    private function createTestProjectFiles(int $projectId): void
    {
        for ($i = 1; $i <= 2; $i++) {
            $file = new ProjectFiles();
            $file->project_id = $projectId;
            $file->title = "test_file_{$i}.bin";
            $file->path = "/tmp/test_encoding_file_{$i}.bin";
            $file->file_type = \common\helpers\ProjectHelper::FILE_TYPE_MODIFIED_DECODED;
            $file->params = json_encode([
                'kess3FileSlotGUID' => "slot-guid-{$i}",
                'fileType' => 'OBDDecoded'
            ]);
            $file->isDeleted = 0;
            
            // Создаем временный файл для тестов
            file_put_contents($file->path, "test encoding content {$i}");
            
            $file->save();
        }
    }

    private function cleanupTestData(): void
    {
        // Удаляем тестовые файлы
        $files = ProjectFiles::find()->where(['project_id' => $this->testProjectId])->all();
        foreach ($files as $file) {
            if (file_exists($file->path)) {
                unlink($file->path);
            }
            $file->delete();
        }

        // Удаляем тестовый проект
        $project = Projects::findOne($this->testProjectId);
        if ($project) {
            $project->delete();
        }

        // Удаляем операции энкодирования
        \common\models\AlientechAsyncOperation::deleteAll([
            'project_id' => $this->testProjectId,
            'operation_type' => 'encoding'
        ]);
    }
}
