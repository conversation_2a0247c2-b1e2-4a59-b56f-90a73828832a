<?php

namespace tests\integration\kess3\infrastructure;

use common\chip\externalIntegrations\kess3\Application\Event\DecodingStartedEvent;
use common\chip\externalIntegrations\kess3\Application\Event\DecodingCompletedEvent;
use common\chip\externalIntegrations\kess3\Application\Event\DecodingFailedEvent;
use common\chip\externalIntegrations\kess3\Infrastructure\EventHandler\DecodingEventHandler;
use common\chip\event\EventSystemBootstrap;
use common\models\ProjectNotes;
use common\models\Notification;
use tests\integration\kess3\BaseKess3Test;
use Yii;

/**
 * Интеграционные тесты для DecodingEventHandler
 */
class DecodingEventHandlerTest extends BaseKess3Test
{
    /**
     * @var DecodingEventHandler
     */
    private $eventHandler;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->eventHandler = Yii::$container->get(DecodingEventHandler::class);
        $this->assertNotNull($this->eventHandler, 'DecodingEventHandler should be available');
    }

    /**
     * Тест обработки события начала декодирования
     */
    public function testHandleDecodingStartedEvent()
    {
        $this->debug("=== Testing DecodingStartedEvent Handling ===");
        
        $operation = $this->createTestOperation();
        $operationId = $operation->getOperationId()->getValue();
        $projectId = $operation->getProjectId()->getValue();
        $fileId = $operation->getFileId()->getValue();

        // Создаем событие
        $event = new DecodingStartedEvent(
            $operationId,
            $projectId,
            $fileId,
            'ext-op-123',
            'slot-456'
        );

        // Проверяем что handler может обработать событие
        $canHandle = $this->eventHandler->canHandle($event, 'kess3_decoding', $projectId);
        $this->assertTrue($canHandle, 'Handler should be able to handle DecodingStartedEvent');

        // Обрабатываем событие
        $result = $this->eventHandler->handle($event, 'kess3_decoding', $projectId);
        $this->assertTrue($result, 'Event should be handled successfully');

        // Проверяем что создана заметка к проекту
        $projectNote = ProjectNotes::find()
            ->where(['project_id' => $projectId])
            ->andWhere(['like', 'text', 'Kess3 decoding started'])
            ->orderBy(['id' => SORT_DESC])
            ->one();

        $this->assertNotNull($projectNote, 'Project note should be created for decoding started');
        $this->assertStringContains($operationId, $projectNote->text);

        $this->debug("DecodingStartedEvent handled successfully, project note created");
    }

    /**
     * Тест обработки события завершения декодирования
     */
    public function testHandleDecodingCompletedEvent()
    {
        $this->debug("=== Testing DecodingCompletedEvent Handling ===");
        
        $operation = $this->createTestOperation();
        $operationId = $operation->getOperationId()->getValue();
        $projectId = $operation->getProjectId()->getValue();
        $fileId = $operation->getFileId()->getValue();

        // Завершаем операцию
        $operation->startDecoding('ext-op-completed', 'slot-completed');
        $decodedFiles = [
            'eeprom' => 'https://api.alientech.to/files/eeprom.bin',
            'flash' => 'https://api.alientech.to/files/flash.bin',
            'id_file' => 'https://api.alientech.to/files/id.bin'
        ];
        $operation->completeSuccessfully($decodedFiles);
        $this->operationRepository->save($operation);

        // Создаем событие
        $event = new DecodingCompletedEvent(
            $operationId,
            $projectId,
            $fileId,
            'ext-op-completed',
            'slot-completed',
            $decodedFiles
        );

        // Обрабатываем событие
        $canHandle = $this->eventHandler->canHandle($event, 'kess3_decoding', $projectId);
        $this->assertTrue($canHandle);

        $result = $this->eventHandler->handle($event, 'kess3_decoding', $projectId);
        $this->assertTrue($result);

        // Проверяем что создана заметка об успешном завершении
        $projectNote = ProjectNotes::find()
            ->where(['project_id' => $projectId])
            ->andWhere(['like', 'text', 'Kess3 decoding completed successfully'])
            ->orderBy(['id' => SORT_DESC])
            ->one();

        $this->assertNotNull($projectNote, 'Project note should be created for successful completion');
        $this->assertStringContains('3 files', $projectNote->text); // eeprom, flash, id_file

        // Проверяем что создано уведомление
        $notification = Notification::find()
            ->where(['source_type' => 'kess3_decoding'])
            ->andWhere(['source_id' => $operationId])
            ->andWhere(['type' => 'decoding_completed'])
            ->orderBy(['id' => SORT_DESC])
            ->one();

        $this->assertNotNull($notification, 'Notification should be created for completed decoding');

        $this->debug("DecodingCompletedEvent handled successfully");
    }

    /**
     * Тест обработки события ошибки декодирования
     */
    public function testHandleDecodingFailedEvent()
    {
        $this->debug("=== Testing DecodingFailedEvent Handling ===");
        
        $operation = $this->createTestOperation();
        $operationId = $operation->getOperationId()->getValue();
        $projectId = $operation->getProjectId()->getValue();
        $fileId = $operation->getFileId()->getValue();

        // Помечаем операцию как проваленную
        $operation->startDecoding('ext-op-failed', 'slot-failed');
        $errorMessage = 'File format not supported by Kess3';
        $operation->failWithError($errorMessage);
        $this->operationRepository->save($operation);

        // Создаем событие
        $event = new DecodingFailedEvent(
            $operationId,
            $projectId,
            $fileId,
            'ext-op-failed',
            'slot-failed',
            $errorMessage
        );

        // Обрабатываем событие
        $canHandle = $this->eventHandler->canHandle($event, 'kess3_decoding', $projectId);
        $this->assertTrue($canHandle);

        $result = $this->eventHandler->handle($event, 'kess3_decoding', $projectId);
        $this->assertTrue($result);

        // Проверяем что создана заметка об ошибке
        $projectNote = ProjectNotes::find()
            ->where(['project_id' => $projectId])
            ->andWhere(['like', 'text', 'Kess3 decoding failed'])
            ->orderBy(['id' => SORT_DESC])
            ->one();

        $this->assertNotNull($projectNote, 'Project note should be created for failed decoding');
        $this->assertStringContains($errorMessage, $projectNote->text);

        // Проверяем что создано уведомление об ошибке
        $notification = Notification::find()
            ->where(['source_type' => 'kess3_decoding'])
            ->andWhere(['source_id' => $operationId])
            ->andWhere(['type' => 'decoding_failed'])
            ->orderBy(['id' => SORT_DESC])
            ->one();

        $this->assertNotNull($notification, 'Error notification should be created');

        $this->debug("DecodingFailedEvent handled successfully");
    }

    /**
     * Тест загрузки декодированных файлов
     */
    public function testDownloadDecodedFiles()
    {
        $this->debug("=== Testing Decoded Files Download ===");
        
        $operation = $this->createTestOperation();
        $projectId = $operation->getProjectId()->getValue();
        
        // Создаем event с реальными URL файлов (mock)
        $decodedFiles = [
            'eeprom' => 'https://httpbin.org/base64/SG93ZHkgZG9vZHk=', // Mock URL возвращающий данные
            'flash' => 'https://httpbin.org/json',
        ];

        $event = new DecodingCompletedEvent(
            $operation->getOperationId()->getValue(),
            $projectId,
            $operation->getFileId()->getValue(),
            'ext-op-download-test',
            'slot-download-test',
            $decodedFiles
        );

        // Включаем загрузку файлов в настройках handler'а
        $reflection = new \ReflectionClass($this->eventHandler);
        if ($reflection->hasProperty('shouldDownloadFiles')) {
            $property = $reflection->getProperty('shouldDownloadFiles');
            $property->setAccessible(true);
            $property->setValue($this->eventHandler, true);
        }

        // Обрабатываем событие
        $result = $this->eventHandler->handle($event, 'kess3_decoding', $projectId);
        $this->assertTrue($result);

        $this->debug("File download handling tested");
    }

    /**
     * Тест обработки неизвестного события
     */
    public function testHandleUnknownEvent()
    {
        $this->debug("=== Testing Unknown Event Handling ===");
        
        // Создаем неизвестное событие
        $unknownEvent = new class {
            public function getType(): string {
                return 'unknown_event';
            }
        };

        $canHandle = $this->eventHandler->canHandle($unknownEvent, 'kess3_decoding', 1);
        $this->assertFalse($canHandle, 'Handler should not handle unknown events');

        $this->debug("Unknown event correctly rejected");
    }

    /**
     * Тест обработки множественных событий
     */
    public function testHandleMultipleEvents()
    {
        $this->debug("=== Testing Multiple Events Handling ===");
        
        $testFile = $this->getRandomTestFile();
        $events = [];

        // Создаем несколько операций и событий
        for ($i = 0; $i < 3; $i++) {
            $operation = $this->createTestOperation($testFile);
            
            $events[] = new DecodingStartedEvent(
                $operation->getOperationId()->getValue(),
                $operation->getProjectId()->getValue(),
                $operation->getFileId()->getValue(),
                "ext-op-multi-{$i}",
                "slot-multi-{$i}"
            );
        }

        // Обрабатываем все события
        $successCount = 0;
        foreach ($events as $event) {
            if ($this->eventHandler->handle($event, 'kess3_decoding', $testFile->project_id)) {
                $successCount++;
            }
        }

        $this->assertEquals(3, $successCount, 'All events should be handled successfully');

        // Проверяем что создано соответствующее количество заметок
        $notesCount = ProjectNotes::find()
            ->where(['project_id' => $testFile->project_id])
            ->andWhere(['like', 'text', 'Kess3 decoding started'])
            ->count();

        $this->assertGreaterThanOrEqual(3, $notesCount);

        $this->debug("Multiple events handled successfully: {$successCount}");
    }

    /**
     * Тест создания системных заметок с правильным форматированием
     */
    public function testProjectNoteFormatting()
    {
        $this->debug("=== Testing Project Note Formatting ===");
        
        $operation = $this->createTestOperation();
        $operationId = $operation->getOperationId()->getValue();
        $projectId = $operation->getProjectId()->getValue();

        // Тестируем заметку для начала декодирования
        $startEvent = new DecodingStartedEvent(
            $operationId,
            $projectId,
            $operation->getFileId()->getValue(),
            'ext-op-format-test',
            'slot-format-test'
        );

        $this->eventHandler->handle($startEvent, 'kess3_decoding', $projectId);

        $startNote = ProjectNotes::find()
            ->where(['project_id' => $projectId])
            ->andWhere(['like', 'text', 'Kess3 decoding started'])
            ->orderBy(['id' => SORT_DESC])
            ->one();

        $this->assertNotNull($startNote);
        $this->assertStringContains('Operation ID:', $startNote->text);
        $this->assertStringContains('External ID:', $startNote->text);
        $this->assertStringContains('Slot ID:', $startNote->text);
        $this->assertStringContains($operationId, $startNote->text);

        // Тестируем заметку для завершения
        $completedEvent = new DecodingCompletedEvent(
            $operationId,
            $projectId,
            $operation->getFileId()->getValue(),
            'ext-op-format-test',
            'slot-format-test',
            ['eeprom' => 'url1', 'flash' => 'url2']
        );

        $this->eventHandler->handle($completedEvent, 'kess3_decoding', $projectId);

        $completedNote = ProjectNotes::find()
            ->where(['project_id' => $projectId])
            ->andWhere(['like', 'text', 'Kess3 decoding completed'])
            ->orderBy(['id' => SORT_DESC])
            ->one();

        $this->assertNotNull($completedNote);
        $this->assertStringContains('Successfully decoded 2 files', $completedNote->text);
        $this->assertStringContains('EEPROM', $completedNote->text);
        $this->assertStringContains('Flash', $completedNote->text);

        $this->debug("Project note formatting verified");
    }

    /**
     * Тест обработки событий через EventBus
     */
    public function testEventBusIntegration()
    {
        $this->debug("=== Testing EventBus Integration ===");
        
        $operation = $this->createTestOperation();
        $projectId = $operation->getProjectId()->getValue();

        // Создаем событие
        $event = new DecodingStartedEvent(
            $operation->getOperationId()->getValue(),
            $projectId,
            $operation->getFileId()->getValue(),
            'ext-op-eventbus-test',
            'slot-eventbus-test'
        );

        // Отправляем событие через EventBus
        $dispatcher = EventSystemBootstrap::getDispatcher();
        $this->assertNotNull($dispatcher, 'EventBus dispatcher should be available');

        // В реальной системе событие будет обработано асинхронно
        // Для тестов вызываем handler напрямую
        $result = $this->eventHandler->handle($event, 'kess3_decoding', $projectId);
        $this->assertTrue($result);

        $this->debug("EventBus integration verified");
    }

    /**
     * Тест обработки ошибок в event handler'е
     */
    public function testErrorHandling()
    {
        $this->debug("=== Testing Error Handling ===");
        
        // Создаем событие с некорректными данными
        $invalidEvent = new DecodingStartedEvent(
            'invalid-operation-id',
            999999, // несуществующий проект
            999999, // несуществующий файл
            'ext-op-invalid',
            'slot-invalid'
        );

        // Handler должен обработать ошибку gracefully
        $result = $this->eventHandler->handle($invalidEvent, 'kess3_decoding', 999999);
        
        // В зависимости от реализации, может вернуть false или true но без создания заметки
        $this->assertIsBool($result);

        $this->debug("Error handling verified");
    }

    /**
     * Тест производительности event handler'а
     */
    public function testEventHandlerPerformance()
    {
        $this->debug("=== Testing Event Handler Performance ===");
        
        $testFile = $this->getRandomTestFile();
        $startTime = microtime(true);

        // Обрабатываем множество событий
        for ($i = 0; $i < 20; $i++) {
            $operation = $this->createTestOperation($testFile);
            
            $event = new DecodingStartedEvent(
                $operation->getOperationId()->getValue(),
                $operation->getProjectId()->getValue(),
                $operation->getFileId()->getValue(),
                "ext-op-perf-{$i}",
                "slot-perf-{$i}"
            );

            $this->eventHandler->handle($event, 'kess3_decoding', $testFile->project_id);
        }

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        $this->assertLessThan(5.0, $executionTime, 'Event handler should process 20 events in less than 5 seconds');
        $this->debug("Event handler performance: 20 events in {$executionTime} seconds");
    }

    /**
     * Тест идемпотентности обработки событий
     */
    public function testEventIdempotency()
    {
        $this->debug("=== Testing Event Idempotency ===");
        
        $operation = $this->createTestOperation();
        $projectId = $operation->getProjectId()->getValue();

        $event = new DecodingStartedEvent(
            $operation->getOperationId()->getValue(),
            $projectId,
            $operation->getFileId()->getValue(),
            'ext-op-idempotent',
            'slot-idempotent'
        );

        // Обрабатываем событие дважды
        $result1 = $this->eventHandler->handle($event, 'kess3_decoding', $projectId);
        $result2 = $this->eventHandler->handle($event, 'kess3_decoding', $projectId);

        $this->assertTrue($result1);
        $this->assertTrue($result2);

        // Проверяем что не создались дублирующиеся заметки
        $notesCount = ProjectNotes::find()
            ->where(['project_id' => $projectId])
            ->andWhere(['like', 'text', 'ext-op-idempotent'])
            ->count();

        // В зависимости от реализации, может быть 1 или 2 заметки
        // Важно что система не падает при повторной обработке
        $this->assertGreaterThanOrEqual(1, $notesCount);
        $this->assertLessThanOrEqual(2, $notesCount);

        $this->debug("Event idempotency verified, notes count: {$notesCount}");
    }
}
