<?php

namespace tests\integration;

use common\chip\externalIntegrations\alientech\Application\Decoding\Command\StartDecodingCommand;
use common\chip\externalIntegrations\alientech\Application\Decoding\Handler\StartDecodingHandler;
use common\chip\externalIntegrations\alientech\Domain\Decoding\Repository\DecodingRepositoryInterface;
use common\chip\externalIntegrations\alientech\Domain\Decoding\Service\DecodingDomainService;
use common\chip\externalIntegrations\alientech\Infrastructure\Repository\DecodingRepository;
use common\chip\externalIntegrations\alientech\Infrastructure\ExternalService\AlientechApiClient;
use common\chip\event\EventDispatcher;
use common\models\Projects;
use common\models\ProjectFiles;
use Yii;
use yii\db\Connection;

/**
 * Интеграционные тесты для Alientech декодирования
 */
class AlientechDecodingTest extends \PHPUnit\Framework\TestCase
{
    private Connection $db;
    private Projects $testProject;
    private ProjectFiles $testFile;
    private StartDecodingHandler $handler;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->db = Yii::$app->db;
        $this->createTestData();
        $this->setupHandler();
    }

    protected function tearDown(): void
    {
        $this->cleanupTestData();
        parent::tearDown();
    }

    public function testStartDecodingIntegration()
    {
        // Arrange
        $command = new StartDecodingCommand(
            projectId: $this->testProject->id,
            fileId: $this->testFile->id,
            filePath: $this->testFile->path,
            callbackUrl: 'http://test.com/callback'
        );

        // Act
        $operation = $this->handler->handle($command);

        // Assert
        $this->assertNotNull($operation);
        $this->assertEquals($this->testProject->id, $operation->getProjectId()->getValue());
        $this->assertEquals($this->testFile->id, $operation->getFileId()->getValue());
        $this->assertEquals($this->testFile->path, $operation->getFilePath());
        $this->assertTrue($operation->getStatus()->isInProgress());
        $this->assertNotEmpty($operation->getOperationId()->getValue());

        // Проверяем, что операция сохранена в базе данных
        $repository = Yii::$container->get(DecodingRepositoryInterface::class);
        $savedOperation = $repository->findById($operation->getOperationId());
        
        $this->assertNotNull($savedOperation);
        $this->assertEquals($operation->getOperationId()->getValue(), $savedOperation->getOperationId()->getValue());
    }

    public function testCannotStartDecodingForSameFileTwice()
    {
        // Arrange
        $command = new StartDecodingCommand(
            projectId: $this->testProject->id,
            fileId: $this->testFile->id,
            filePath: $this->testFile->path
        );

        // Act - первый запуск
        $this->handler->handle($command);

        // Assert - второй запуск должен выбросить исключение
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Decoding operation for file ' . $this->testFile->id . ' is already in progress');
        
        $this->handler->handle($command);
    }

    public function testGetProjectStatistics()
    {
        // Arrange
        $command1 = new StartDecodingCommand(
            projectId: $this->testProject->id,
            fileId: $this->testFile->id,
            filePath: $this->testFile->path
        );

        // Создаем второй файл для тестирования
        $testFile2 = new ProjectFiles();
        $testFile2->project_id = $this->testProject->id;
        $testFile2->name = 'test2.bin';
        $testFile2->path = '/test/path2.bin';
        $testFile2->size = 1024;
        $testFile2->save();

        $command2 = new StartDecodingCommand(
            projectId: $this->testProject->id,
            fileId: $testFile2->id,
            filePath: $testFile2->path
        );

        // Act
        $this->handler->handle($command1);
        $this->handler->handle($command2);

        $domainService = Yii::$container->get(DecodingDomainService::class);
        $stats = $domainService->getProjectStatistics(
            \common\chip\externalIntegrations\alientech\Domain\Shared\ProjectId::fromInt($this->testProject->id)
        );

        // Assert
        $this->assertEquals(2, $stats['total']);
        $this->assertEquals(2, $stats['in_progress']);
        $this->assertEquals(0, $stats['completed']);

        // Cleanup
        $testFile2->delete();
    }

    private function createTestData(): void
    {
        // Создаем тестовый проект
        $this->testProject = new Projects();
        $this->testProject->name = 'Test Alientech Project';
        $this->testProject->created_by = 1;
        $this->testProject->save();

        // Создаем тестовый файл
        $this->testFile = new ProjectFiles();
        $this->testFile->project_id = $this->testProject->id;
        $this->testFile->name = 'test.bin';
        $this->testFile->path = '/test/path.bin';
        $this->testFile->size = 1024;
        $this->testFile->save();
    }

    private function cleanupTestData(): void
    {
        if (isset($this->testFile)) {
            $this->testFile->delete();
        }
        if (isset($this->testProject)) {
            $this->testProject->delete();
        }

        // Очищаем операции из базы данных
        $this->db->createCommand()
            ->delete('alientech_async_operation', ['project_id' => $this->testProject->id ?? 0])
            ->execute();
    }

    private function setupHandler(): void
    {
        // Настраиваем DI контейнер для тестов
        Yii::$container->set(DecodingRepositoryInterface::class, DecodingRepository::class);
        
        Yii::$container->set(DecodingDomainService::class, function ($container) {
            return new DecodingDomainService(
                $container->get(DecodingRepositoryInterface::class)
            );
        });

        // Мокаем API клиент для тестов
        $mockApiClient = $this->createMock(\common\chip\externalIntegrations\alientech\Application\Decoding\Handler\AlientechApiClientInterface::class);
        $mockApiClient->method('startDecoding')->willReturn([
            'guid' => 'test_guid_' . uniqid(),
            'slotGUID' => 'test_slot_' . uniqid(),
        ]);

        $this->handler = new StartDecodingHandler(
            Yii::$container->get(DecodingDomainService::class),
            Yii::$container->get(DecodingRepositoryInterface::class),
            $mockApiClient,
            Yii::$container->get(EventDispatcher::class)
        );
    }
}
