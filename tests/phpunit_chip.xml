<?xml version="1.0" encoding="UTF-8"?>
<phpunit bootstrap="bootstrap.php"
         colors="true"
         verbose="true"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         stopOnFailure="false"
         processIsolation="false"
         backupGlobals="false"
         backupStaticAttributes="false">
    <testsuites>
        <testsuite name="Chip Clean Architecture Tests">
            <directory>unit/chip</directory>
            <directory>integration/chip</directory>
        </testsuite>
        <testsuite name="Chip Domain Tests">
            <directory>unit/chip/domain</directory>
        </testsuite>
        <testsuite name="Chip Application Tests">
            <directory>unit/chip/application</directory>
        </testsuite>
        <testsuite name="Chip Infrastructure Tests">
            <directory>unit/chip/infrastructure</directory>
        </testsuite>
        <testsuite name="Chip Integration Tests">
            <directory>integration/chip</directory>
        </testsuite>
    </testsuites>
    <filter>
        <whitelist>
            <directory suffix=".php">../chip/Domain</directory>
            <directory suffix=".php">../chip/Application</directory>
            <directory suffix=".php">../chip/Infrastructure</directory>
        </whitelist>
    </filter>
    <logging>
        <log type="coverage-html" target="coverage/chip" lowUpperBound="35" highLowerBound="70"/>
        <log type="coverage-clover" target="coverage/chip/clover.xml"/>
        <log type="junit" target="coverage/chip/junit.xml"/>
    </logging>
</phpunit>
