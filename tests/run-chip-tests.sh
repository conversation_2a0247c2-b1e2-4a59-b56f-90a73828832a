#!/bin/bash

# Скрипт для запуска тестов Chip Clean Architecture

echo "=== Запуск тестов Chip Clean Architecture ==="

# Переходим в директорию тестов
cd "$(dirname "$0")"

# Проверяем наличие PHPUnit
if ! command -v phpunit &> /dev/null; then
    echo "PHPUnit не найден. Устанавливаем через Composer..."
    composer install
fi

echo ""
echo "=== Запуск всех тестов Chip ==="
phpunit --configuration phpunit_chip.xml

echo ""
echo "=== Запуск только доменных тестов ==="
phpunit --configuration phpunit_chip.xml --testsuite "Chip Domain Tests"

echo ""
echo "=== Запуск только тестов приложения ==="
phpunit --configuration phpunit_chip.xml --testsuite "Chip Application Tests"

echo ""
echo "=== Запуск только инфраструктурных тестов ==="
phpunit --configuration phpunit_chip.xml --testsuite "Chip Infrastructure Tests"

echo ""
echo "=== Запуск только интеграционных тестов ==="
phpunit --configuration phpunit_chip.xml --testsuite "Chip Integration Tests"

echo ""
echo "=== Генерация отчета о покрытии кода ==="
phpunit --configuration phpunit_chip.xml --coverage-html coverage/chip

echo ""
echo "=== Тесты завершены ==="
echo "Отчет о покрытии кода: coverage/chip/index.html"
