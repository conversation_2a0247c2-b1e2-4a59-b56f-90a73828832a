<?php

namespace tests\unit\chip\infrastructure;

use chip\Infrastructure\Services\YiiEventDispatcher;
use chip\Domain\Events\DomainEvent;
use chip\Domain\Events\ProjectCreatedEvent;
use chip\Domain\Events\FileUploadedEvent;
use chip\Domain\ValueObjects\ProjectId;
use chip\Domain\ValueObjects\ClientId;
use chip\Domain\ValueObjects\VehicleInfo;
use chip\Domain\ValueObjects\FileId;
use chip\Domain\ValueObjects\FileName;
use chip\Domain\ValueObjects\Brand;
use PHPUnit\Framework\TestCase;

/**
 * Unit тесты для YiiEventDispatcher
 */
class YiiEventDispatcherTest extends TestCase
{
    private YiiEventDispatcher $eventDispatcher;
    private DomainEvent $event;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->eventDispatcher = new YiiEventDispatcher();
        
        $this->event = new ProjectCreatedEvent(
            ProjectId::generate(),
            new ClientId('client-123'),
            new VehicleInfo(
                new Brand('BMW'),
                new Model('X5'),
                new Year(2020),
                new EngineType('3.0 TDI')
            )
        );
    }

    /**
     * Тест отправки одного события
     */
    public function testDispatchSingleEvent()
    {
        // Пока метод не реализован, проверяем что он вызывается без ошибок
        $this->eventDispatcher->dispatch($this->event);
        
        // Если дошли до этой точки, значит исключение не было выброшено
        $this->assertTrue(true);
    }

    /**
     * Тест отправки множественных событий
     */
    public function testDispatchMultipleEvents()
    {
        $events = [
            $this->event,
            new FileUploadedEvent(
                ProjectId::generate(),
                FileId::generate(),
                new FileName('test.bin')
            )
        ];
        
        // Пока метод не реализован, проверяем что он вызывается без ошибок
        $this->eventDispatcher->dispatchMultiple($events);
        
        // Если дошли до этой точки, значит исключение не было выброшено
        $this->assertTrue(true);
    }

    /**
     * Тест отправки пустого массива событий
     */
    public function testDispatchEmptyEventArray()
    {
        $this->eventDispatcher->dispatchMultiple([]);
        
        // Если дошли до этой точки, значит исключение не было выброшено
        $this->assertTrue(true);
    }

    /**
     * Тест отправки различных типов событий
     */
    public function testDispatchDifferentEventTypes()
    {
        $projectCreatedEvent = new ProjectCreatedEvent(
            ProjectId::generate(),
            new ClientId('client-123'),
            new VehicleInfo(
                new Brand('BMW'),
                new Model('X5'),
                new Year(2020),
                new EngineType('3.0 TDI')
            )
        );
        
        $fileUploadedEvent = new FileUploadedEvent(
            ProjectId::generate(),
            FileId::generate(),
            new FileName('test.bin')
        );
        
        // Проверяем что разные типы событий обрабатываются без ошибок
        $this->eventDispatcher->dispatch($projectCreatedEvent);
        $this->eventDispatcher->dispatch($fileUploadedEvent);
        
        $this->assertTrue(true);
    }

    /**
     * Тест создания экземпляра диспетчера
     */
    public function testEventDispatcherInstantiation()
    {
        $dispatcher = new YiiEventDispatcher();
        
        $this->assertInstanceOf(YiiEventDispatcher::class, $dispatcher);
    }
}
