<?php

namespace tests\unit\chip\domain;

use chip\Domain\Entities\Project;
use chip\Domain\Entities\ProjectFile;
use chip\Domain\ValueObjects\ProjectId;
use chip\Domain\ValueObjects\ClientId;
use chip\Domain\ValueObjects\ProjectStatus;
use chip\Domain\ValueObjects\VehicleInfo;
use chip\Domain\ValueObjects\EcuInfo;
use chip\Domain\ValueObjects\Money;
use chip\Domain\ValueObjects\Currency;
use chip\Domain\ValueObjects\Brand;
use chip\Domain\ValueObjects\FileId;
use chip\Domain\ValueObjects\FileName;
use chip\Domain\ValueObjects\FileType;
use chip\Domain\ValueObjects\FilePath;
use chip\Domain\ValueObjects\FileSize;
use chip\Domain\ValueObjects\FileChecksum;
use chip\Domain\Events\ProjectCreatedEvent;
use chip\Domain\Events\FileUploadedEvent;
use chip\Domain\Exceptions\ProjectException;
use PHPUnit\Framework\TestCase;

/**
 * Unit тесты для доменной сущности Project
 */
class ProjectTest extends TestCase
{
    private ProjectId $projectId;
    private ClientId $clientId;
    private VehicleInfo $vehicleInfo;
    private EcuInfo $ecuInfo;
    private Money $cost;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->projectId = ProjectId::generate();
        $this->clientId = new ClientId('client-123');
        $this->vehicleInfo = new VehicleInfo(
            new Brand('BMW'),
            new Model('X5'),
            new Year(2020),
            new EngineType('3.0 TDI')
        );
        $this->ecuInfo = new EcuInfo(); // TODO: implement when EcuInfo is created
        $this->cost = new Money(50000, Currency::rub());
    }

    /**
     * Тест создания проекта
     */
    public function testCreateProject()
    {
        $project = Project::create(
            $this->projectId,
            $this->clientId,
            $this->vehicleInfo,
            $this->ecuInfo,
            $this->cost
        );

        $this->assertTrue($project->getId()->equals($this->projectId));
        $this->assertTrue($project->getClientId()->equals($this->clientId));
        $this->assertTrue($project->getStatus()->equals(ProjectStatus::created()));
        $this->assertTrue($project->getVehicleInfo()->equals($this->vehicleInfo));
        $this->assertTrue($project->getCost()->equals($this->cost));
        $this->assertNotNull($project->getCreatedAt());
        $this->assertNull($project->getCompletedAt());
        
        // Проверяем что создалось событие
        $events = $project->releaseEvents();
        $this->assertCount(1, $events);
        $this->assertInstanceOf(ProjectCreatedEvent::class, $events[0]);
    }

    /**
     * Тест загрузки файла в проект
     */
    public function testUploadFile()
    {
        $project = Project::create(
            $this->projectId,
            $this->clientId,
            $this->vehicleInfo,
            $this->ecuInfo,
            $this->cost
        );

        $file = new ProjectFile(
            FileId::generate(),
            $this->projectId,
            new FileName('test.bin'),
            FileType::bin(),
            new FilePath('/tmp/test.bin'),
            new FileSize(1024),
            new FileChecksum('abc123')
        );

        $project->uploadFile($file);

        $this->assertTrue($project->getStatus()->equals(ProjectStatus::fileUploaded()));
        $this->assertCount(1, $project->getFiles());
        
        // Проверяем события
        $events = $project->releaseEvents();
        $this->assertCount(2, $events); // ProjectCreated + FileUploaded
        $this->assertInstanceOf(FileUploadedEvent::class, $events[1]);
    }

    /**
     * Тест начала обработки проекта
     */
    public function testStartProcessing()
    {
        $project = Project::create(
            $this->projectId,
            $this->clientId,
            $this->vehicleInfo,
            $this->ecuInfo,
            $this->cost
        );

        // Сначала загружаем файл
        $file = new ProjectFile(
            FileId::generate(),
            $this->projectId,
            new FileName('test.bin'),
            FileType::bin(),
            new FilePath('/tmp/test.bin'),
            new FileSize(1024),
            new FileChecksum('abc123')
        );
        $project->uploadFile($file);

        $project->startProcessing();

        $this->assertTrue($project->getStatus()->equals(ProjectStatus::processing()));
    }

    /**
     * Тест завершения проекта
     */
    public function testCompleteProject()
    {
        $project = Project::create(
            $this->projectId,
            $this->clientId,
            $this->vehicleInfo,
            $this->ecuInfo,
            $this->cost
        );

        // Переводим проект в состояние обработки
        $file = new ProjectFile(
            FileId::generate(),
            $this->projectId,
            new FileName('test.bin'),
            FileType::bin(),
            new FilePath('/tmp/test.bin'),
            new FileSize(1024),
            new FileChecksum('abc123')
        );
        $project->uploadFile($file);
        $project->startProcessing();

        $project->complete();

        $this->assertTrue($project->getStatus()->equals(ProjectStatus::completed()));
        $this->assertNotNull($project->getCompletedAt());
    }

    /**
     * Тест невозможности загрузки файла в неподходящем статусе
     */
    public function testCannotUploadFileInWrongStatus()
    {
        $project = Project::create(
            $this->projectId,
            $this->clientId,
            $this->vehicleInfo,
            $this->ecuInfo,
            $this->cost
        );

        // Переводим в статус обработки
        $file = new ProjectFile(
            FileId::generate(),
            $this->projectId,
            new FileName('test.bin'),
            FileType::bin(),
            new FilePath('/tmp/test.bin'),
            new FileSize(1024),
            new FileChecksum('abc123')
        );
        $project->uploadFile($file);
        $project->startProcessing();

        // Попытка загрузить еще один файл должна вызвать исключение
        $this->expectException(ProjectException::class);
        $project->uploadFile($file);
    }

    /**
     * Тест невозможности начать обработку без файлов
     */
    public function testCannotStartProcessingWithoutFiles()
    {
        $project = Project::create(
            $this->projectId,
            $this->clientId,
            $this->vehicleInfo,
            $this->ecuInfo,
            $this->cost
        );

        $this->expectException(ProjectException::class);
        $project->startProcessing();
    }

    /**
     * Тест переходов статусов
     */
    public function testStatusTransitions()
    {
        $project = Project::create(
            $this->projectId,
            $this->clientId,
            $this->vehicleInfo,
            $this->ecuInfo,
            $this->cost
        );

        // Изначально статус created
        $this->assertTrue($project->getStatus()->equals(ProjectStatus::created()));

        // После загрузки файла - file_uploaded
        $file = new ProjectFile(
            FileId::generate(),
            $this->projectId,
            new FileName('test.bin'),
            FileType::bin(),
            new FilePath('/tmp/test.bin'),
            new FileSize(1024),
            new FileChecksum('abc123')
        );
        $project->uploadFile($file);
        $this->assertTrue($project->getStatus()->equals(ProjectStatus::fileUploaded()));

        // После начала обработки - processing
        $project->startProcessing();
        $this->assertTrue($project->getStatus()->equals(ProjectStatus::processing()));

        // После завершения - completed
        $project->complete();
        $this->assertTrue($project->getStatus()->equals(ProjectStatus::completed()));
    }

    /**
     * Тест освобождения событий
     */
    public function testReleaseEvents()
    {
        $project = Project::create(
            $this->projectId,
            $this->clientId,
            $this->vehicleInfo,
            $this->ecuInfo,
            $this->cost
        );

        // Первый вызов должен вернуть события
        $events = $project->releaseEvents();
        $this->assertCount(1, $events);

        // Второй вызов должен вернуть пустой массив
        $events = $project->releaseEvents();
        $this->assertCount(0, $events);
    }
}
