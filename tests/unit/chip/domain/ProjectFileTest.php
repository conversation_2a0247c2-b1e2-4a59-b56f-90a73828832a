<?php

namespace tests\unit\chip\domain;

use chip\Domain\Entities\ProjectFile;
use chip\Domain\ValueObjects\FileId;
use chip\Domain\ValueObjects\ProjectId;
use chip\Domain\ValueObjects\FileName;
use chip\Domain\ValueObjects\FileType;
use chip\Domain\ValueObjects\FileStatus;
use chip\Domain\ValueObjects\FilePath;
use chip\Domain\ValueObjects\FileSize;
use chip\Domain\ValueObjects\FileChecksum;
use chip\Domain\Exceptions\FileException;
use PHPUnit\Framework\TestCase;

/**
 * Unit тесты для доменной сущности ProjectFile
 */
class ProjectFileTest extends TestCase
{
    private FileId $fileId;
    private ProjectId $projectId;
    private FileName $fileName;
    private FileType $fileType;
    private FilePath $filePath;
    private FileSize $fileSize;
    private FileChecksum $fileChecksum;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->fileId = FileId::generate();
        $this->projectId = ProjectId::generate();
        $this->fileName = new FileName('test.bin');
        $this->fileType = FileType::bin();
        $this->filePath = new FilePath('/tmp/test.bin');
        $this->fileSize = new FileSize(1024);
        $this->fileChecksum = new FileChecksum('abc123def456');
    }

    /**
     * Тест создания файла проекта
     */
    public function testCreateProjectFile()
    {
        $file = new ProjectFile(
            $this->fileId,
            $this->projectId,
            $this->fileName,
            $this->fileType,
            $this->filePath,
            $this->fileSize,
            $this->fileChecksum
        );

        $this->assertTrue($file->getId()->equals($this->fileId));
        $this->assertTrue($file->getProjectId()->equals($this->projectId));
        $this->assertTrue($file->getName()->equals($this->fileName));
        $this->assertTrue($file->getType()->equals($this->fileType));
        $this->assertTrue($file->getStatus()->equals(FileStatus::uploaded()));
        $this->assertTrue($file->getPath()->equals($this->filePath));
        $this->assertTrue($file->getSize()->equals($this->fileSize));
        $this->assertTrue($file->getChecksum()->equals($this->fileChecksum));
        $this->assertNotNull($file->getUploadedAt());
        $this->assertNull($file->getProcessedAt());
    }

    /**
     * Тест отметки файла как обрабатываемого
     */
    public function testMarkAsProcessing()
    {
        $file = new ProjectFile(
            $this->fileId,
            $this->projectId,
            $this->fileName,
            $this->fileType,
            $this->filePath,
            $this->fileSize,
            $this->fileChecksum
        );

        $file->markAsProcessing();

        $this->assertTrue($file->getStatus()->equals(FileStatus::processing()));
    }

    /**
     * Тест отметки файла как завершенного
     */
    public function testMarkAsCompleted()
    {
        $file = new ProjectFile(
            $this->fileId,
            $this->projectId,
            $this->fileName,
            $this->fileType,
            $this->filePath,
            $this->fileSize,
            $this->fileChecksum
        );

        $file->markAsProcessing();
        $file->markAsCompleted();

        $this->assertTrue($file->getStatus()->equals(FileStatus::completed()));
        $this->assertNotNull($file->getProcessedAt());
    }

    /**
     * Тест отметки файла как проваленного
     */
    public function testMarkAsFailed()
    {
        $file = new ProjectFile(
            $this->fileId,
            $this->projectId,
            $this->fileName,
            $this->fileType,
            $this->filePath,
            $this->fileSize,
            $this->fileChecksum
        );

        $file->markAsProcessing();
        $file->markAsFailed('Processing error');

        $this->assertTrue($file->getStatus()->equals(FileStatus::failed('Processing error')));
    }

    /**
     * Тест невозможности перехода в неправильный статус
     */
    public function testCannotTransitionToInvalidStatus()
    {
        $file = new ProjectFile(
            $this->fileId,
            $this->projectId,
            $this->fileName,
            $this->fileType,
            $this->filePath,
            $this->fileSize,
            $this->fileChecksum
        );

        // Попытка сразу отметить как завершенный без обработки
        $this->expectException(FileException::class);
        $file->markAsCompleted();
    }

    /**
     * Тест обновления пути файла
     */
    public function testUpdatePath()
    {
        $file = new ProjectFile(
            $this->fileId,
            $this->projectId,
            $this->fileName,
            $this->fileType,
            $this->filePath,
            $this->fileSize,
            $this->fileChecksum
        );

        $newPath = new FilePath('/new/path/test.bin');
        $file->updatePath($newPath);

        $this->assertTrue($file->getPath()->equals($newPath));
    }

    /**
     * Тест переходов статусов файла
     */
    public function testFileStatusTransitions()
    {
        $file = new ProjectFile(
            $this->fileId,
            $this->projectId,
            $this->fileName,
            $this->fileType,
            $this->filePath,
            $this->fileSize,
            $this->fileChecksum
        );

        // Изначально uploaded
        $this->assertTrue($file->getStatus()->equals(FileStatus::uploaded()));

        // Переход в processing
        $file->markAsProcessing();
        $this->assertTrue($file->getStatus()->equals(FileStatus::processing()));

        // Переход в completed
        $file->markAsCompleted();
        $this->assertTrue($file->getStatus()->equals(FileStatus::completed()));
    }

    /**
     * Тест переходов статусов с ошибкой
     */
    public function testFileStatusTransitionsWithError()
    {
        $file = new ProjectFile(
            $this->fileId,
            $this->projectId,
            $this->fileName,
            $this->fileType,
            $this->filePath,
            $this->fileSize,
            $this->fileChecksum
        );

        // Переход в processing
        $file->markAsProcessing();
        $this->assertTrue($file->getStatus()->equals(FileStatus::processing()));

        // Переход в failed
        $file->markAsFailed('Test error');
        $this->assertTrue($file->getStatus()->equals(FileStatus::failed('Test error')));

        // Можно снова попробовать обработать
        $file->markAsProcessing();
        $this->assertTrue($file->getStatus()->equals(FileStatus::processing()));
    }

    /**
     * Тест проверки возможности обработки файла
     */
    public function testIsProcessable()
    {
        $processableFile = new ProjectFile(
            $this->fileId,
            $this->projectId,
            $this->fileName,
            FileType::bin(), // processable type
            $this->filePath,
            $this->fileSize,
            $this->fileChecksum
        );

        $nonProcessableFile = new ProjectFile(
            FileId::generate(),
            $this->projectId,
            new FileName('readme.txt'),
            FileType::fromExtension('txt'), // non-processable type
            new FilePath('/tmp/readme.txt'),
            new FileSize(100),
            new FileChecksum('def456')
        );

        $this->assertTrue($processableFile->isProcessable());
        $this->assertFalse($nonProcessableFile->isProcessable());
    }
}
