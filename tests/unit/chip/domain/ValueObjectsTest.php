<?php

namespace tests\unit\chip\domain;

use chip\Domain\ValueObjects\ProjectId;
use chip\Domain\ValueObjects\ProjectStatus;
use chip\Domain\ValueObjects\ClientId;
use chip\Domain\ValueObjects\Money;
use chip\Domain\ValueObjects\Currency;
use chip\Domain\ValueObjects\FileId;
use chip\Domain\ValueObjects\FileName;
use chip\Domain\ValueObjects\FileType;
use chip\Domain\ValueObjects\FileStatus;
use chip\Domain\ValueObjects\Brand;
use PHPUnit\Framework\TestCase;

/**
 * Unit тесты для Value Objects доменного слоя
 */
class ValueObjectsTest extends TestCase
{
    /**
     * Тест ProjectId
     */
    public function testProjectId()
    {
        // Тест генерации
        $projectId = ProjectId::generate();
        $this->assertNotEmpty($projectId->value());
        $this->assertIsString($projectId->value());
        
        // Тест создания из строки
        $idString = 'project-123-456-789';
        $projectId2 = new ProjectId($idString);
        $this->assertEquals($idString, $projectId2->value());
        
        // Тест equals
        $projectId3 = new ProjectId($idString);
        $this->assertTrue($projectId2->equals($projectId3));
        $this->assertFalse($projectId->equals($projectId2));
        
        // Тест toString
        $this->assertEquals($idString, (string) $projectId2);
        
        // Тест валидации
        $this->expectException(\InvalidArgumentException::class);
        new ProjectId('');
    }

    /**
     * Тест ProjectStatus
     */
    public function testProjectStatus()
    {
        // Тест создания статусов
        $created = ProjectStatus::created();
        $fileUploaded = ProjectStatus::fileUploaded();
        $processing = ProjectStatus::processing();
        $completed = ProjectStatus::completed();
        $failed = ProjectStatus::failed();
        $cancelled = ProjectStatus::cancelled();
        
        // Тест значений
        $this->assertEquals('created', $created->value());
        $this->assertEquals('file_uploaded', $fileUploaded->value());
        $this->assertEquals('processing', $processing->value());
        $this->assertEquals('completed', $completed->value());
        $this->assertEquals('failed', $failed->value());
        $this->assertEquals('cancelled', $cancelled->value());
        
        // Тест equals
        $created2 = ProjectStatus::created();
        $this->assertTrue($created->equals($created2));
        $this->assertFalse($created->equals($processing));
        
        // Тест toString
        $this->assertEquals('created', (string) $created);
    }

    /**
     * Тест переходов статусов проекта
     */
    public function testProjectStatusTransitions()
    {
        $created = ProjectStatus::created();
        $fileUploaded = ProjectStatus::fileUploaded();
        $processing = ProjectStatus::processing();
        $completed = ProjectStatus::completed();
        $failed = ProjectStatus::failed();
        $cancelled = ProjectStatus::cancelled();
        
        // Валидные переходы
        $this->assertTrue($created->canTransitionTo($fileUploaded));
        $this->assertTrue($created->canTransitionTo($cancelled));
        $this->assertTrue($fileUploaded->canTransitionTo($processing));
        $this->assertTrue($processing->canTransitionTo($completed));
        $this->assertTrue($processing->canTransitionTo($failed));
        $this->assertTrue($failed->canTransitionTo($processing));
        
        // Невалидные переходы
        $this->assertFalse($created->canTransitionTo($processing));
        $this->assertFalse($completed->canTransitionTo($processing));
        $this->assertFalse($cancelled->canTransitionTo($processing));
    }

    /**
     * Тест Money
     */
    public function testMoney()
    {
        $currency = Currency::rub();
        $money = new Money(50000, $currency); // 500.00 RUB
        
        $this->assertEquals(50000, $money->getAmount());
        $this->assertTrue($money->getCurrency()->equals($currency));
        $this->assertEquals(500.0, $money->toFloat());
        
        // Тест создания из float
        $money2 = Money::fromFloat(250.50, $currency);
        $this->assertEquals(25050, $money2->getAmount());
        $this->assertEquals(250.50, $money2->toFloat());
        
        // Тест операций
        $sum = $money->add($money2);
        $this->assertEquals(75050, $sum->getAmount());
        
        $diff = $money->subtract($money2);
        $this->assertEquals(24950, $diff->getAmount());
        
        $multiplied = $money->multiply(2.0);
        $this->assertEquals(100000, $multiplied->getAmount());
        
        // Тест equals
        $money3 = new Money(50000, $currency);
        $this->assertTrue($money->equals($money3));
        
        // Тест валидации отрицательной суммы
        $this->expectException(\InvalidArgumentException::class);
        new Money(-1000, $currency);
    }

    /**
     * Тест Currency
     */
    public function testCurrency()
    {
        $usd = Currency::usd();
        $eur = Currency::eur();
        $rub = Currency::rub();
        
        $this->assertEquals('USD', $usd->value());
        $this->assertEquals('EUR', $eur->value());
        $this->assertEquals('RUB', $rub->value());
        
        $this->assertTrue($usd->equals(Currency::usd()));
        $this->assertFalse($usd->equals($eur));
        
        $this->assertEquals('USD', (string) $usd);
    }

    /**
     * Тест FileType
     */
    public function testFileType()
    {
        $bin = FileType::bin();
        $hex = FileType::hex();
        $kess = FileType::kess();
        
        $this->assertEquals('bin', $bin->value());
        $this->assertEquals('hex', $hex->value());
        $this->assertEquals('kess', $kess->value());
        
        // Тест создания из расширения
        $binFromExt = FileType::fromExtension('bin');
        $this->assertTrue($bin->equals($binFromExt));
        
        // Тест проверки возможности обработки
        $this->assertTrue($bin->isProcessable());
        $this->assertTrue($hex->isProcessable());
        $this->assertTrue($kess->isProcessable());
        
        // Тест невалидного расширения
        $this->expectException(\InvalidArgumentException::class);
        FileType::fromExtension('invalid');
    }

    /**
     * Тест FileStatus
     */
    public function testFileStatus()
    {
        $uploaded = FileStatus::uploaded();
        $processing = FileStatus::processing();
        $completed = FileStatus::completed();
        $failed = FileStatus::failed('Error message');
        
        $this->assertEquals('uploaded', $uploaded->value());
        $this->assertEquals('processing', $processing->value());
        $this->assertEquals('completed', $completed->value());
        $this->assertEquals('failed', $failed->value());
        $this->assertEquals('Error message', $failed->getReason());
        
        // Тест переходов
        $this->assertTrue($uploaded->canTransitionTo($processing));
        $this->assertTrue($processing->canTransitionTo($completed));
        $this->assertTrue($processing->canTransitionTo($failed));
        $this->assertTrue($failed->canTransitionTo($processing));
        
        $this->assertFalse($uploaded->canTransitionTo($completed));
        $this->assertFalse($completed->canTransitionTo($processing));
    }

    /**
     * Тест FileName
     */
    public function testFileName()
    {
        $fileName = new FileName('test.bin');
        
        $this->assertEquals('test.bin', $fileName->value());
        $this->assertEquals('bin', $fileName->getExtension());
        $this->assertEquals('test', $fileName->getBaseName());
        $this->assertEquals('test.bin', (string) $fileName);
        
        // Тест equals
        $fileName2 = new FileName('test.bin');
        $this->assertTrue($fileName->equals($fileName2));
        
        // Тест валидации пустого имени
        $this->expectException(\InvalidArgumentException::class);
        new FileName('');
    }

    /**
     * Тест Brand
     */
    public function testBrand()
    {
        $brand = new Brand('BMW');
        
        $this->assertEquals('BMW', $brand->value());
        $this->assertEquals('BMW', (string) $brand);
        
        // Тест equals
        $brand2 = new Brand('BMW');
        $this->assertTrue($brand->equals($brand2));
        
        // Тест валидации пустого бренда
        $this->expectException(\InvalidArgumentException::class);
        new Brand('');
    }

    /**
     * Тест immutability Value Objects
     */
    public function testImmutability()
    {
        $projectId = ProjectId::generate();
        $originalValue = $projectId->value();
        
        // Value objects должны быть immutable
        $newProjectId = new ProjectId('new-value');
        $this->assertEquals($originalValue, $projectId->value());
        $this->assertNotEquals($originalValue, $newProjectId->value());
    }

    /**
     * Тест уникальности генерируемых ID
     */
    public function testIdUniqueness()
    {
        $ids = [];
        
        // Генерируем 100 ID и проверяем уникальность
        for ($i = 0; $i < 100; $i++) {
            $projectId = ProjectId::generate();
            $fileId = FileId::generate();
            
            $projectValue = $projectId->value();
            $fileValue = $fileId->value();
            
            $this->assertNotContains($projectValue, $ids);
            $this->assertNotContains($fileValue, $ids);
            
            $ids[] = $projectValue;
            $ids[] = $fileValue;
        }
    }
}
