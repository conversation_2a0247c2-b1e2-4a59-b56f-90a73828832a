<?php

namespace tests\unit\chip\domain;

use chip\Domain\Collections\ProjectFiles;
use chip\Domain\Entities\ProjectFile;
use chip\Domain\ValueObjects\FileId;
use chip\Domain\ValueObjects\ProjectId;
use chip\Domain\ValueObjects\FileName;
use chip\Domain\ValueObjects\FileType;
use chip\Domain\ValueObjects\FilePath;
use chip\Domain\ValueObjects\FileSize;
use chip\Domain\ValueObjects\FileChecksum;
use PHPUnit\Framework\TestCase;

/**
 * Unit тесты для коллекции ProjectFiles
 */
class ProjectFilesCollectionTest extends TestCase
{
    private ProjectFiles $collection;
    private ProjectFile $file1;
    private ProjectFile $file2;
    private FileId $fileId1;
    private FileId $fileId2;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->collection = new ProjectFiles();
        
        $projectId = ProjectId::generate();
        $this->fileId1 = FileId::generate();
        $this->fileId2 = FileId::generate();
        
        $this->file1 = new ProjectFile(
            $this->fileId1,
            $projectId,
            new FileName('test1.bin'),
            FileType::bin(),
            new FilePath('/tmp/test1.bin'),
            new FileSize(1024),
            new FileChecksum('abc123')
        );
        
        $this->file2 = new ProjectFile(
            $this->fileId2,
            $projectId,
            new FileName('test2.hex'),
            FileType::hex(),
            new FilePath('/tmp/test2.hex'),
            new FileSize(2048),
            new FileChecksum('def456')
        );
    }

    /**
     * Тест создания пустой коллекции
     */
    public function testCreateEmptyCollection()
    {
        $this->assertCount(0, $this->collection);
        $this->assertTrue($this->collection->isEmpty());
    }

    /**
     * Тест добавления файла в коллекцию
     */
    public function testAddFile()
    {
        $this->collection->add($this->file1);
        
        $this->assertCount(1, $this->collection);
        $this->assertFalse($this->collection->isEmpty());
    }

    /**
     * Тест добавления нескольких файлов
     */
    public function testAddMultipleFiles()
    {
        $this->collection->add($this->file1);
        $this->collection->add($this->file2);
        
        $this->assertCount(2, $this->collection);
    }

    /**
     * Тест поиска файла по ID
     */
    public function testFindById()
    {
        $this->collection->add($this->file1);
        $this->collection->add($this->file2);
        
        $foundFile = $this->collection->findById($this->fileId1);
        $this->assertSame($this->file1, $foundFile);
        
        $foundFile2 = $this->collection->findById($this->fileId2);
        $this->assertSame($this->file2, $foundFile2);
        
        $notFound = $this->collection->findById(FileId::generate());
        $this->assertNull($notFound);
    }

    /**
     * Тест удаления файла из коллекции
     */
    public function testRemoveFile()
    {
        $this->collection->add($this->file1);
        $this->collection->add($this->file2);
        
        $this->assertCount(2, $this->collection);
        
        $this->collection->remove($this->fileId1);
        
        $this->assertCount(1, $this->collection);
        $this->assertNull($this->collection->findById($this->fileId1));
        $this->assertSame($this->file2, $this->collection->findById($this->fileId2));
    }

    /**
     * Тест удаления несуществующего файла
     */
    public function testRemoveNonExistentFile()
    {
        $this->collection->add($this->file1);
        
        $this->assertCount(1, $this->collection);
        
        // Удаление несуществующего файла не должно вызывать ошибку
        $this->collection->remove(FileId::generate());
        
        $this->assertCount(1, $this->collection);
    }

    /**
     * Тест итерации по коллекции
     */
    public function testIteration()
    {
        $this->collection->add($this->file1);
        $this->collection->add($this->file2);
        
        $files = [];
        foreach ($this->collection as $file) {
            $files[] = $file;
        }
        
        $this->assertCount(2, $files);
        $this->assertContains($this->file1, $files);
        $this->assertContains($this->file2, $files);
    }

    /**
     * Тест преобразования в массив
     */
    public function testToArray()
    {
        $this->collection->add($this->file1);
        $this->collection->add($this->file2);
        
        $array = $this->collection->toArray();
        
        $this->assertIsArray($array);
        $this->assertCount(2, $array);
        $this->assertContains($this->file1, $array);
        $this->assertContains($this->file2, $array);
    }

    /**
     * Тест замены файла с тем же ID
     */
    public function testReplaceFileWithSameId()
    {
        $this->collection->add($this->file1);
        
        // Создаем новый файл с тем же ID
        $newFile = new ProjectFile(
            $this->fileId1, // тот же ID
            ProjectId::generate(),
            new FileName('updated.bin'),
            FileType::bin(),
            new FilePath('/tmp/updated.bin'),
            new FileSize(4096),
            new FileChecksum('updated123')
        );
        
        $this->collection->add($newFile);
        
        // Должен остаться только один файл (новый заменил старый)
        $this->assertCount(1, $this->collection);
        $this->assertSame($newFile, $this->collection->findById($this->fileId1));
    }

    /**
     * Тест подсчета файлов
     */
    public function testCount()
    {
        $this->assertEquals(0, $this->collection->count());
        
        $this->collection->add($this->file1);
        $this->assertEquals(1, $this->collection->count());
        
        $this->collection->add($this->file2);
        $this->assertEquals(2, $this->collection->count());
        
        $this->collection->remove($this->fileId1);
        $this->assertEquals(1, $this->collection->count());
    }

    /**
     * Тест проверки пустоты коллекции
     */
    public function testIsEmpty()
    {
        $this->assertTrue($this->collection->isEmpty());
        
        $this->collection->add($this->file1);
        $this->assertFalse($this->collection->isEmpty());
        
        $this->collection->remove($this->fileId1);
        $this->assertTrue($this->collection->isEmpty());
    }
}
