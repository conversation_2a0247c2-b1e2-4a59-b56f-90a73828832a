<?php

namespace tests\unit\chip\domain;

use chip\Domain\Services\ProjectDomainService;
use chip\Domain\Services\PricingService;
use chip\Domain\Services\ProjectValidationService;
use chip\Domain\Entities\Project;
use chip\Domain\Entities\User;
use chip\Domain\ValueObjects\ClientId;
use chip\Domain\ValueObjects\VehicleInfo;
use chip\Domain\ValueObjects\EcuInfo;
use chip\Domain\ValueObjects\Brand;
use chip\Domain\ValueObjects\UserId;
use chip\Domain\ValueObjects\UserEmail;
use chip\Domain\ValueObjects\UserRole;
use chip\Domain\ValueObjects\UserProfile;
use chip\Domain\ValueObjects\Money;
use chip\Domain\ValueObjects\Currency;
use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;

/**
 * Unit тесты для доменного сервиса ProjectDomainService
 */
class ProjectDomainServiceTest extends TestCase
{
    private ProjectDomainService $projectDomainService;
    private PricingService|MockObject $pricingService;
    private ProjectValidationService|MockObject $validationService;
    private ClientId $clientId;
    private VehicleInfo $vehicleInfo;
    private EcuInfo $ecuInfo;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->pricingService = $this->createMock(PricingService::class);
        $this->validationService = $this->createMock(ProjectValidationService::class);
        
        $this->projectDomainService = new ProjectDomainService(
            $this->pricingService,
            $this->validationService
        );
        
        $this->clientId = new ClientId('client-123');
        $this->vehicleInfo = new VehicleInfo(
            new Brand('BMW'),
            new Model('X5'),
            new Year(2020),
            new EngineType('3.0 TDI')
        );
        $this->ecuInfo = new EcuInfo(); // TODO: implement when EcuInfo is created
        
        $this->user = new User(
            new UserId('user-123'),
            new UserEmail('<EMAIL>'),
            new UserRole('client'),
            new UserProfile('Test User', '+1234567890')
        );
    }

    /**
     * Тест создания проекта
     */
    public function testCreateProject()
    {
        $expectedCost = new Money(50000, Currency::rub());
        
        $this->validationService
            ->expects($this->once())
            ->method('validateProjectCreation')
            ->with($this->clientId, $this->vehicleInfo, $this->ecuInfo, $this->user);
            
        $this->pricingService
            ->expects($this->once())
            ->method('calculateProjectCost')
            ->with($this->vehicleInfo, $this->ecuInfo)
            ->willReturn($expectedCost);

        $project = $this->projectDomainService->createProject(
            $this->clientId,
            $this->vehicleInfo,
            $this->ecuInfo,
            $this->user
        );

        $this->assertInstanceOf(Project::class, $project);
        $this->assertTrue($project->getClientId()->equals($this->clientId));
        $this->assertTrue($project->getVehicleInfo()->equals($this->vehicleInfo));
        $this->assertTrue($project->getCost()->equals($expectedCost));
    }

    /**
     * Тест проверки доступа пользователя к проекту
     */
    public function testCanUserAccessProject()
    {
        $project = $this->createMock(Project::class);
        
        $result = $this->projectDomainService->canUserAccessProject($this->user, $project);
        
        // Пока метод не реализован, проверяем что он вызывается без ошибок
        $this->assertIsBool($result);
    }

    /**
     * Тест проверки возможности обработки проекта
     */
    public function testCanProjectBeProcessed()
    {
        $project = $this->createMock(Project::class);
        
        $result = $this->projectDomainService->canProjectBeProcessed($project);
        
        // Пока метод не реализован, проверяем что он вызывается без ошибок
        $this->assertIsBool($result);
    }

    /**
     * Тест расчета приоритета проекта
     */
    public function testCalculateProjectPriority()
    {
        $project = $this->createMock(Project::class);
        
        $priority = $this->projectDomainService->calculateProjectPriority($project);
        
        // Пока метод не реализован, проверяем что он возвращает число
        $this->assertIsInt($priority);
    }

    /**
     * Тест проверки права на скидку
     */
    public function testIsProjectEligibleForDiscount()
    {
        $project = $this->createMock(Project::class);
        
        $result = $this->projectDomainService->isProjectEligibleForDiscount($project);
        
        // Пока метод не реализован, проверяем что он вызывается без ошибок
        $this->assertIsBool($result);
    }

    /**
     * Тест валидации завершения проекта
     */
    public function testValidateProjectCompletion()
    {
        $project = $this->createMock(Project::class);
        
        // Метод не должен выбрасывать исключения для валидного проекта
        $this->projectDomainService->validateProjectCompletion($project);
        
        // Если дошли до этой точки, значит исключение не было выброшено
        $this->assertTrue(true);
    }

    /**
     * Тест создания проекта с невалидными данными
     */
    public function testCreateProjectWithInvalidData()
    {
        $this->validationService
            ->expects($this->once())
            ->method('validateProjectCreation')
            ->with($this->clientId, $this->vehicleInfo, $this->ecuInfo, $this->user)
            ->willThrowException(new \InvalidArgumentException('Invalid project data'));

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid project data');

        $this->projectDomainService->createProject(
            $this->clientId,
            $this->vehicleInfo,
            $this->ecuInfo,
            $this->user
        );
    }

    /**
     * Тест создания проекта с ошибкой расчета стоимости
     */
    public function testCreateProjectWithPricingError()
    {
        $this->validationService
            ->expects($this->once())
            ->method('validateProjectCreation')
            ->with($this->clientId, $this->vehicleInfo, $this->ecuInfo, $this->user);

        $this->pricingService
            ->expects($this->once())
            ->method('calculateProjectCost')
            ->with($this->vehicleInfo, $this->ecuInfo)
            ->willThrowException(new \RuntimeException('Pricing service error'));

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Pricing service error');

        $this->projectDomainService->createProject(
            $this->clientId,
            $this->vehicleInfo,
            $this->ecuInfo,
            $this->user
        );
    }
}
