<?php

namespace tests\unit\chip\domain;

use chip\Domain\Events\ProjectCreatedEvent;
use chip\Domain\Events\FileUploadedEvent;
use chip\Domain\ValueObjects\ProjectId;
use chip\Domain\ValueObjects\ClientId;
use chip\Domain\ValueObjects\VehicleInfo;
use chip\Domain\ValueObjects\FileId;
use chip\Domain\ValueObjects\FileName;
use chip\Domain\ValueObjects\Brand;
use PHPUnit\Framework\TestCase;

/**
 * Unit тесты для доменных событий
 */
class DomainEventsTest extends TestCase
{
    private ProjectId $projectId;
    private ClientId $clientId;
    private VehicleInfo $vehicleInfo;
    private FileId $fileId;
    private FileName $fileName;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->projectId = ProjectId::generate();
        $this->clientId = new ClientId('client-123');
        $this->vehicleInfo = new VehicleInfo(
            new Brand('BMW'),
            new Model('X5'),
            new Year(2020),
            new EngineType('3.0 TDI')
        );
        $this->fileId = FileId::generate();
        $this->fileName = new FileName('test.bin');
    }

    /**
     * Тест создания события ProjectCreatedEvent
     */
    public function testProjectCreatedEvent()
    {
        $event = new ProjectCreatedEvent(
            $this->projectId,
            $this->clientId,
            $this->vehicleInfo
        );

        $this->assertEquals('project.created', $event->getEventName());
        $this->assertTrue($event->getProjectId()->equals($this->projectId));
        $this->assertTrue($event->getClientId()->equals($this->clientId));
        $this->assertTrue($event->getVehicleInfo()->equals($this->vehicleInfo));
        $this->assertInstanceOf(\DateTimeImmutable::class, $event->getOccurredAt());
    }

    /**
     * Тест payload события ProjectCreatedEvent
     */
    public function testProjectCreatedEventPayload()
    {
        $event = new ProjectCreatedEvent(
            $this->projectId,
            $this->clientId,
            $this->vehicleInfo
        );

        $payload = $event->getPayload();

        $this->assertIsArray($payload);
        $this->assertArrayHasKey('projectId', $payload);
        $this->assertArrayHasKey('clientId', $payload);
        $this->assertArrayHasKey('vehicleInfo', $payload);
        $this->assertArrayHasKey('occurredAt', $payload);
        
        $this->assertEquals($this->projectId->value(), $payload['projectId']);
        $this->assertEquals($this->clientId->value(), $payload['clientId']);
        $this->assertIsArray($payload['vehicleInfo']);
        $this->assertIsString($payload['occurredAt']);
    }

    /**
     * Тест создания события FileUploadedEvent
     */
    public function testFileUploadedEvent()
    {
        $event = new FileUploadedEvent(
            $this->projectId,
            $this->fileId,
            $this->fileName
        );

        $this->assertEquals('file.uploaded', $event->getEventName());
        $this->assertTrue($event->getProjectId()->equals($this->projectId));
        $this->assertTrue($event->getFileId()->equals($this->fileId));
        $this->assertTrue($event->getFileName()->equals($this->fileName));
        $this->assertInstanceOf(\DateTimeImmutable::class, $event->getOccurredAt());
    }

    /**
     * Тест payload события FileUploadedEvent
     */
    public function testFileUploadedEventPayload()
    {
        $event = new FileUploadedEvent(
            $this->projectId,
            $this->fileId,
            $this->fileName
        );

        $payload = $event->getPayload();

        $this->assertIsArray($payload);
        $this->assertArrayHasKey('projectId', $payload);
        $this->assertArrayHasKey('fileId', $payload);
        $this->assertArrayHasKey('fileName', $payload);
        $this->assertArrayHasKey('occurredAt', $payload);
        
        $this->assertEquals($this->projectId->value(), $payload['projectId']);
        $this->assertEquals($this->fileId->value(), $payload['fileId']);
        $this->assertEquals($this->fileName->value(), $payload['fileName']);
        $this->assertIsString($payload['occurredAt']);
    }

    /**
     * Тест времени создания события
     */
    public function testEventOccurredAt()
    {
        $beforeEvent = new \DateTimeImmutable();
        
        $event = new ProjectCreatedEvent(
            $this->projectId,
            $this->clientId,
            $this->vehicleInfo
        );
        
        $afterEvent = new \DateTimeImmutable();

        $this->assertGreaterThanOrEqual($beforeEvent, $event->getOccurredAt());
        $this->assertLessThanOrEqual($afterEvent, $event->getOccurredAt());
    }

    /**
     * Тест уникальности времени событий
     */
    public function testEventTimestampUniqueness()
    {
        $event1 = new ProjectCreatedEvent(
            $this->projectId,
            $this->clientId,
            $this->vehicleInfo
        );
        
        // Небольшая задержка для гарантии разного времени
        usleep(1000); // 1ms
        
        $event2 = new FileUploadedEvent(
            $this->projectId,
            $this->fileId,
            $this->fileName
        );

        $this->assertNotEquals(
            $event1->getOccurredAt()->format('Y-m-d H:i:s.u'),
            $event2->getOccurredAt()->format('Y-m-d H:i:s.u')
        );
    }

    /**
     * Тест сериализации событий
     */
    public function testEventSerialization()
    {
        $event = new ProjectCreatedEvent(
            $this->projectId,
            $this->clientId,
            $this->vehicleInfo
        );

        $payload = $event->getPayload();
        $serialized = json_encode($payload);
        $unserialized = json_decode($serialized, true);

        $this->assertIsString($serialized);
        $this->assertIsArray($unserialized);
        $this->assertEquals($payload, $unserialized);
    }

    /**
     * Тест immutability событий
     */
    public function testEventImmutability()
    {
        $event = new ProjectCreatedEvent(
            $this->projectId,
            $this->clientId,
            $this->vehicleInfo
        );

        $originalTime = $event->getOccurredAt();
        $originalProjectId = $event->getProjectId();

        // События должны быть неизменяемыми
        // Создаем новое событие и проверяем что старое не изменилось
        $newEvent = new ProjectCreatedEvent(
            ProjectId::generate(),
            new ClientId('different-client'),
            $this->vehicleInfo
        );

        $this->assertEquals($originalTime, $event->getOccurredAt());
        $this->assertTrue($event->getProjectId()->equals($originalProjectId));
        $this->assertFalse($event->getProjectId()->equals($newEvent->getProjectId()));
    }

    /**
     * Тест производительности создания событий
     */
    public function testEventCreationPerformance()
    {
        $startTime = microtime(true);
        
        // Создаем множество событий
        for ($i = 0; $i < 1000; $i++) {
            $event = new ProjectCreatedEvent(
                ProjectId::generate(),
                $this->clientId,
                $this->vehicleInfo
            );
        }
        
        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;
        
        // Проверяем что создание 1000 событий занимает меньше 100ms
        $this->assertLessThan(0.1, $executionTime, 'Event creation should be fast');
    }
}
