<?php

namespace tests\unit\chip\application;

use chip\Application\UseCases\CreateProjectUseCase;
use chip\Application\Commands\CreateProjectCommand;
use chip\Application\DTOs\ProjectDTO;
use chip\Application\Interfaces\EventDispatcherInterface;
use chip\Application\Exceptions\UseCaseException;
use chip\Domain\Services\ProjectDomainService;
use chip\Domain\Repositories\ProjectRepositoryInterface;
use chip\Domain\Repositories\UserRepositoryInterface;
use chip\Domain\Entities\Project;
use chip\Domain\Entities\User;
use chip\Domain\ValueObjects\UserId;
use chip\Domain\ValueObjects\ClientId;
use chip\Domain\ValueObjects\VehicleInfo;
use chip\Domain\ValueObjects\EcuInfo;
use chip\Domain\ValueObjects\Brand;
use chip\Domain\ValueObjects\UserEmail;
use chip\Domain\ValueObjects\UserRole;
use chip\Domain\ValueObjects\UserProfile;
use chip\Domain\Events\ProjectCreatedEvent;
use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;

/**
 * Unit тесты для CreateProjectUseCase
 */
class CreateProjectUseCaseTest extends TestCase
{
    private CreateProjectUseCase $useCase;
    private ProjectDomainService|MockObject $projectDomainService;
    private ProjectRepositoryInterface|MockObject $projectRepository;
    private UserRepositoryInterface|MockObject $userRepository;
    private EventDispatcherInterface|MockObject $eventDispatcher;
    private CreateProjectCommand $command;
    private User $user;
    private Project|MockObject $project;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->projectDomainService = $this->createMock(ProjectDomainService::class);
        $this->projectRepository = $this->createMock(ProjectRepositoryInterface::class);
        $this->userRepository = $this->createMock(UserRepositoryInterface::class);
        $this->eventDispatcher = $this->createMock(EventDispatcherInterface::class);
        
        $this->useCase = new CreateProjectUseCase(
            $this->projectDomainService,
            $this->projectRepository,
            $this->userRepository,
            $this->eventDispatcher
        );
        
        $userId = new UserId('user-123');
        $clientId = new ClientId('client-123');
        $vehicleInfo = new VehicleInfo(
            new Brand('BMW'),
            new Model('X5'),
            new Year(2020),
            new EngineType('3.0 TDI')
        );
        $ecuInfo = new EcuInfo(); // TODO: implement when EcuInfo is created
        
        $this->command = new CreateProjectCommand(
            $userId,
            $clientId,
            $vehicleInfo,
            $ecuInfo,
            []
        );
        
        $this->user = new User(
            $userId,
            new UserEmail('<EMAIL>'),
            new UserRole('client'),
            new UserProfile('Test User', '+1234567890')
        );
        
        $this->project = $this->createMock(Project::class);
    }

    /**
     * Тест успешного создания проекта
     */
    public function testSuccessfulProjectCreation()
    {
        $events = [new ProjectCreatedEvent(
            $this->command->getClientId(),
            $this->command->getClientId(),
            $this->command->getVehicleInfo()
        )];
        
        $this->userRepository
            ->expects($this->once())
            ->method('findById')
            ->with($this->command->getUserId())
            ->willReturn($this->user);
            
        $this->projectDomainService
            ->expects($this->once())
            ->method('createProject')
            ->with(
                $this->command->getClientId(),
                $this->command->getVehicleInfo(),
                $this->command->getEcuInfo(),
                $this->user
            )
            ->willReturn($this->project);
            
        $this->projectRepository
            ->expects($this->once())
            ->method('save')
            ->with($this->project);
            
        $this->project
            ->expects($this->once())
            ->method('releaseEvents')
            ->willReturn($events);
            
        $this->eventDispatcher
            ->expects($this->once())
            ->method('dispatch')
            ->with($events[0]);

        $result = $this->useCase->execute($this->command);

        $this->assertInstanceOf(ProjectDTO::class, $result);
    }

    /**
     * Тест создания проекта с несуществующим пользователем
     */
    public function testProjectCreationWithNonExistentUser()
    {
        $this->userRepository
            ->expects($this->once())
            ->method('findById')
            ->with($this->command->getUserId())
            ->willReturn(null);

        $this->expectException(UseCaseException::class);
        $this->expectExceptionMessage('User not found');

        $this->useCase->execute($this->command);
    }

    /**
     * Тест создания проекта с ошибкой доменного сервиса
     */
    public function testProjectCreationWithDomainServiceError()
    {
        $this->userRepository
            ->expects($this->once())
            ->method('findById')
            ->with($this->command->getUserId())
            ->willReturn($this->user);
            
        $this->projectDomainService
            ->expects($this->once())
            ->method('createProject')
            ->with(
                $this->command->getClientId(),
                $this->command->getVehicleInfo(),
                $this->command->getEcuInfo(),
                $this->user
            )
            ->willThrowException(new \InvalidArgumentException('Invalid project data'));

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid project data');

        $this->useCase->execute($this->command);
    }

    /**
     * Тест создания проекта с ошибкой сохранения
     */
    public function testProjectCreationWithRepositoryError()
    {
        $this->userRepository
            ->expects($this->once())
            ->method('findById')
            ->with($this->command->getUserId())
            ->willReturn($this->user);
            
        $this->projectDomainService
            ->expects($this->once())
            ->method('createProject')
            ->willReturn($this->project);
            
        $this->projectRepository
            ->expects($this->once())
            ->method('save')
            ->with($this->project)
            ->willThrowException(new \RuntimeException('Database error'));

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->execute($this->command);
    }

    /**
     * Тест создания проекта с множественными событиями
     */
    public function testProjectCreationWithMultipleEvents()
    {
        $events = [
            new ProjectCreatedEvent(
                $this->command->getClientId(),
                $this->command->getClientId(),
                $this->command->getVehicleInfo()
            ),
            new ProjectCreatedEvent(
                $this->command->getClientId(),
                $this->command->getClientId(),
                $this->command->getVehicleInfo()
            )
        ];
        
        $this->userRepository
            ->expects($this->once())
            ->method('findById')
            ->willReturn($this->user);
            
        $this->projectDomainService
            ->expects($this->once())
            ->method('createProject')
            ->willReturn($this->project);
            
        $this->projectRepository
            ->expects($this->once())
            ->method('save');
            
        $this->project
            ->expects($this->once())
            ->method('releaseEvents')
            ->willReturn($events);
            
        $this->eventDispatcher
            ->expects($this->exactly(2))
            ->method('dispatch')
            ->withConsecutive([$events[0]], [$events[1]]);

        $result = $this->useCase->execute($this->command);

        $this->assertInstanceOf(ProjectDTO::class, $result);
    }

    /**
     * Тест создания проекта без событий
     */
    public function testProjectCreationWithoutEvents()
    {
        $this->userRepository
            ->expects($this->once())
            ->method('findById')
            ->willReturn($this->user);
            
        $this->projectDomainService
            ->expects($this->once())
            ->method('createProject')
            ->willReturn($this->project);
            
        $this->projectRepository
            ->expects($this->once())
            ->method('save');
            
        $this->project
            ->expects($this->once())
            ->method('releaseEvents')
            ->willReturn([]);
            
        $this->eventDispatcher
            ->expects($this->never())
            ->method('dispatch');

        $result = $this->useCase->execute($this->command);

        $this->assertInstanceOf(ProjectDTO::class, $result);
    }
}
