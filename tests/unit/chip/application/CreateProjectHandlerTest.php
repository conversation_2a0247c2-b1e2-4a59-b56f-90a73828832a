<?php

namespace tests\unit\chip\application;

use chip\Application\Handlers\CreateProjectHandler;
use chip\Application\UseCases\CreateProjectUseCase;
use chip\Application\Commands\CreateProjectCommand;
use chip\Application\DTOs\ProjectDTO;
use chip\Domain\ValueObjects\UserId;
use chip\Domain\ValueObjects\ClientId;
use chip\Domain\ValueObjects\VehicleInfo;
use chip\Domain\ValueObjects\EcuInfo;
use chip\Domain\ValueObjects\Brand;
use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;

/**
 * Unit тесты для CreateProjectHandler
 */
class CreateProjectHandlerTest extends TestCase
{
    private CreateProjectHandler $handler;
    private CreateProjectUseCase|MockObject $useCase;
    private CreateProjectCommand $command;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->useCase = $this->createMock(CreateProjectUseCase::class);
        $this->handler = new CreateProjectHandler($this->useCase);
        
        $this->command = new CreateProjectCommand(
            new UserId('user-123'),
            new ClientId('client-123'),
            new VehicleInfo(
                new Brand('BMW'),
                new Model('X5'),
                new Year(2020),
                new EngineType('3.0 TDI')
            ),
            new EcuInfo(), // TODO: implement when EcuInfo is created
            []
        );
    }

    /**
     * Тест успешной обработки команды
     */
    public function testSuccessfulCommandHandling()
    {
        $expectedDto = new ProjectDTO(
            'project-123',
            'client-123',
            'created',
            [],
            [],
            [],
            '2023-01-01 12:00:00'
        );
        
        $this->useCase
            ->expects($this->once())
            ->method('execute')
            ->with($this->command)
            ->willReturn($expectedDto);

        $result = $this->handler->handle($this->command);

        $this->assertSame($expectedDto, $result);
    }

    /**
     * Тест обработки команды с исключением
     */
    public function testCommandHandlingWithException()
    {
        $this->useCase
            ->expects($this->once())
            ->method('execute')
            ->with($this->command)
            ->willThrowException(new \RuntimeException('Use case error'));

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Use case error');

        $this->handler->handle($this->command);
    }

    /**
     * Тест передачи правильной команды в use case
     */
    public function testCorrectCommandPassedToUseCase()
    {
        $this->useCase
            ->expects($this->once())
            ->method('execute')
            ->with($this->identicalTo($this->command))
            ->willReturn($this->createMock(ProjectDTO::class));

        $this->handler->handle($this->command);
    }
}
