<?php

namespace tests\unit\chip\application;

use chip\Application\UseCases\UploadFileUseCase;
use chip\Application\Commands\UploadFileCommand;
use chip\Application\DTOs\FileDTO;
use chip\Application\Interfaces\FileStorageInterface;
use chip\Application\Interfaces\EventDispatcherInterface;
use chip\Application\Exceptions\UseCaseException;
use chip\Domain\Services\FileProcessingService;
use chip\Domain\Repositories\ProjectRepositoryInterface;
use chip\Domain\Entities\Project;
use chip\Domain\Entities\ProjectFile;
use chip\Domain\ValueObjects\ProjectId;
use chip\Domain\ValueObjects\FileId;
use chip\Domain\ValueObjects\FileName;
use chip\Domain\ValueObjects\FileType;
use chip\Domain\ValueObjects\FilePath;
use chip\Domain\ValueObjects\FileSize;
use chip\Domain\ValueObjects\FileChecksum;
use chip\Domain\Events\FileUploadedEvent;
use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;

/**
 * Unit тесты для UploadFileUseCase
 */
class UploadFileUseCaseTest extends TestCase
{
    private UploadFileUseCase $useCase;
    private ProjectRepositoryInterface|MockObject $projectRepository;
    private FileProcessingService|MockObject $fileProcessingService;
    private FileStorageInterface|MockObject $fileStorage;
    private EventDispatcherInterface|MockObject $eventDispatcher;
    private UploadFileCommand $command;
    private Project|MockObject $project;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->projectRepository = $this->createMock(ProjectRepositoryInterface::class);
        $this->fileProcessingService = $this->createMock(FileProcessingService::class);
        $this->fileStorage = $this->createMock(FileStorageInterface::class);
        $this->eventDispatcher = $this->createMock(EventDispatcherInterface::class);
        
        $this->useCase = new UploadFileUseCase(
            $this->projectRepository,
            $this->fileProcessingService,
            $this->fileStorage,
            $this->eventDispatcher
        );
        
        $projectId = ProjectId::generate();
        $this->command = new UploadFileCommand(
            $projectId,
            FileId::generate(),
            new FileName('test.bin'),
            FileType::bin(),
            new FilePath('/tmp/test.bin'),
            new FileSize(1024),
            new FileChecksum('abc123def456')
        );
        
        $this->project = $this->createMock(Project::class);
    }

    /**
     * Тест успешной загрузки файла
     */
    public function testSuccessfulFileUpload()
    {
        $events = [new FileUploadedEvent(
            $this->command->getProjectId(),
            $this->command->getFileId(),
            $this->command->getFileName()
        )];
        
        $this->projectRepository
            ->expects($this->once())
            ->method('findById')
            ->with($this->command->getProjectId())
            ->willReturn($this->project);
            
        $this->fileProcessingService
            ->expects($this->once())
            ->method('validateFileForProcessing')
            ->with($this->isInstanceOf(ProjectFile::class));
            
        $this->fileStorage
            ->expects($this->once())
            ->method('store')
            ->with($this->isInstanceOf(ProjectFile::class));
            
        $this->project
            ->expects($this->once())
            ->method('uploadFile')
            ->with($this->isInstanceOf(ProjectFile::class));
            
        $this->projectRepository
            ->expects($this->once())
            ->method('save')
            ->with($this->project);
            
        $this->project
            ->expects($this->once())
            ->method('releaseEvents')
            ->willReturn($events);
            
        $this->eventDispatcher
            ->expects($this->once())
            ->method('dispatch')
            ->with($events[0]);

        $result = $this->useCase->execute($this->command);

        $this->assertInstanceOf(FileDTO::class, $result);
    }

    /**
     * Тест загрузки файла в несуществующий проект
     */
    public function testFileUploadToNonExistentProject()
    {
        $this->projectRepository
            ->expects($this->once())
            ->method('findById')
            ->with($this->command->getProjectId())
            ->willReturn(null);

        $this->expectException(UseCaseException::class);
        $this->expectExceptionMessage('Project not found');

        $this->useCase->execute($this->command);
    }

    /**
     * Тест загрузки файла с ошибкой валидации
     */
    public function testFileUploadWithValidationError()
    {
        $this->projectRepository
            ->expects($this->once())
            ->method('findById')
            ->willReturn($this->project);
            
        $this->fileProcessingService
            ->expects($this->once())
            ->method('validateFileForProcessing')
            ->willThrowException(new \InvalidArgumentException('Invalid file format'));

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid file format');

        $this->useCase->execute($this->command);
    }

    /**
     * Тест загрузки файла с ошибкой хранилища
     */
    public function testFileUploadWithStorageError()
    {
        $this->projectRepository
            ->expects($this->once())
            ->method('findById')
            ->willReturn($this->project);
            
        $this->fileProcessingService
            ->expects($this->once())
            ->method('validateFileForProcessing');
            
        $this->fileStorage
            ->expects($this->once())
            ->method('store')
            ->willThrowException(new \RuntimeException('Storage error'));

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Storage error');

        $this->useCase->execute($this->command);
    }

    /**
     * Тест загрузки файла с ошибкой проекта
     */
    public function testFileUploadWithProjectError()
    {
        $this->projectRepository
            ->expects($this->once())
            ->method('findById')
            ->willReturn($this->project);
            
        $this->fileProcessingService
            ->expects($this->once())
            ->method('validateFileForProcessing');
            
        $this->fileStorage
            ->expects($this->once())
            ->method('store');
            
        $this->project
            ->expects($this->once())
            ->method('uploadFile')
            ->willThrowException(new \DomainException('Cannot upload file'));

        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Cannot upload file');

        $this->useCase->execute($this->command);
    }

    /**
     * Тест загрузки файла с ошибкой сохранения проекта
     */
    public function testFileUploadWithProjectSaveError()
    {
        $this->projectRepository
            ->expects($this->once())
            ->method('findById')
            ->willReturn($this->project);
            
        $this->fileProcessingService
            ->expects($this->once())
            ->method('validateFileForProcessing');
            
        $this->fileStorage
            ->expects($this->once())
            ->method('store');
            
        $this->project
            ->expects($this->once())
            ->method('uploadFile');
            
        $this->projectRepository
            ->expects($this->once())
            ->method('save')
            ->willThrowException(new \RuntimeException('Database error'));

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->execute($this->command);
    }

    /**
     * Тест загрузки файла без событий
     */
    public function testFileUploadWithoutEvents()
    {
        $this->projectRepository
            ->expects($this->once())
            ->method('findById')
            ->willReturn($this->project);
            
        $this->fileProcessingService
            ->expects($this->once())
            ->method('validateFileForProcessing');
            
        $this->fileStorage
            ->expects($this->once())
            ->method('store');
            
        $this->project
            ->expects($this->once())
            ->method('uploadFile');
            
        $this->projectRepository
            ->expects($this->once())
            ->method('save');
            
        $this->project
            ->expects($this->once())
            ->method('releaseEvents')
            ->willReturn([]);
            
        $this->eventDispatcher
            ->expects($this->never())
            ->method('dispatch');

        $result = $this->useCase->execute($this->command);

        $this->assertInstanceOf(FileDTO::class, $result);
    }
}
