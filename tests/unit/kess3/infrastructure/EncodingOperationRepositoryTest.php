<?php

declare(strict_types=1);

namespace tests\unit\kess3\infrastructure;

use PHPUnit\Framework\TestCase;
use common\chip\externalIntegrations\kess3\Infrastructure\Repository\EncodingOperationRepository;
use common\chip\externalIntegrations\kess3\Domain\Entities\EncodingOperation;
use common\chip\externalIntegrations\kess3\Domain\ValueObjects\UserInfo;
use common\chip\externalIntegrations\kess3\Domain\ValueObjects\OperationStatus;

/**
 * Тесты репозитория операций энкодинга
 */
class EncodingOperationRepositoryTest extends TestCase
{
    private EncodingOperationRepository $repository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = new EncodingOperationRepository();
    }

    public function testCreateEncodingOperation()
    {
        // Arrange
        $userInfo = UserInfo::create(
            projectId: 123,
            fileId: 1,
            userId: 456,
            additionalData: ['key' => 'value']
        );

        $operation = EncodingOperation::create(
            id: 'test_operation_123',
            projectId: 123,
            fileIds: [1, 2, 3],
            userInfo: $userInfo,
            callbackUrl: 'https://example.com/callback'
        );

        // Assert initial state
        $this->assertEquals('test_operation_123', $operation->getId());
        $this->assertEquals(123, $operation->getProjectId());
        $this->assertEquals([1, 2, 3], $operation->getFileIds());
        $this->assertTrue($operation->isInProgress());
        $this->assertFalse($operation->isCompleted());
        $this->assertFalse($operation->isFailed());
        $this->assertEquals('https://example.com/callback', $operation->getCallbackUrl());
    }

    public function testOperationStatusTransitions()
    {
        // Arrange
        $userInfo = UserInfo::create(123, 1, 456);
        $operation = EncodingOperation::create(
            id: 'test_operation_123',
            projectId: 123,
            fileIds: [1, 2, 3],
            userInfo: $userInfo
        );

        // Test start
        $operation->start('external_op_123', 'slot_guid_123');
        $this->assertEquals('external_op_123', $operation->getExternalOperationId());
        $this->assertEquals('slot_guid_123', $operation->getSlotGuid());

        // Test successful completion
        $result = ['encodedFiles' => ['file1.bin', 'file2.bin']];
        $operation->complete(true, $result);
        
        $this->assertTrue($operation->isCompleted());
        $this->assertFalse($operation->isFailed());
        $this->assertTrue($operation->isFinished());
        $this->assertEquals($result, $operation->getResult());
        $this->assertNull($operation->getError());
        $this->assertInstanceOf(\DateTimeImmutable::class, $operation->getCompletedAt());
    }

    public function testOperationFailure()
    {
        // Arrange
        $userInfo = UserInfo::create(123, 1, 456);
        $operation = EncodingOperation::create(
            id: 'test_operation_123',
            projectId: 123,
            fileIds: [1, 2, 3],
            userInfo: $userInfo
        );

        // Test failure
        $operation->complete(false, null, 'Encoding failed due to invalid file format');
        
        $this->assertFalse($operation->isCompleted());
        $this->assertTrue($operation->isFailed());
        $this->assertTrue($operation->isFinished());
        $this->assertNull($operation->getResult());
        $this->assertEquals('Encoding failed due to invalid file format', $operation->getError());
        $this->assertInstanceOf(\DateTimeImmutable::class, $operation->getCompletedAt());
    }

    public function testPollingIntervalUpdate()
    {
        // Arrange
        $userInfo = UserInfo::create(123, 1, 456);
        $operation = EncodingOperation::create(
            id: 'test_operation_123',
            projectId: 123,
            fileIds: [1, 2, 3],
            userInfo: $userInfo
        );

        // Test polling interval update
        $operation->updatePollingInterval(30);
        $this->assertEquals(30, $operation->getRecommendedPollingInterval());
    }

    public function testUserInfoIntegration()
    {
        // Arrange
        $additionalData = [
            'source' => 'web_interface',
            'priority' => 'high',
            'options' => ['compression' => true]
        ];

        $userInfo = UserInfo::create(
            projectId: 123,
            fileId: 1,
            userId: 456,
            additionalData: $additionalData
        );

        $operation = EncodingOperation::create(
            id: 'test_operation_123',
            projectId: 123,
            fileIds: [1, 2, 3],
            userInfo: $userInfo
        );

        // Assert UserInfo integration
        $retrievedUserInfo = $operation->getUserInfo();
        $this->assertEquals(123, $retrievedUserInfo->getProjectId());
        $this->assertEquals(1, $retrievedUserInfo->getFileId());
        $this->assertEquals(456, $retrievedUserInfo->getUserId());
        $this->assertEquals($additionalData, $retrievedUserInfo->getAdditionalData());
    }

    public function testOperationTimestamps()
    {
        // Arrange
        $beforeCreation = new \DateTimeImmutable();
        
        $userInfo = UserInfo::create(123, 1, 456);
        $operation = EncodingOperation::create(
            id: 'test_operation_123',
            projectId: 123,
            fileIds: [1, 2, 3],
            userInfo: $userInfo
        );

        $afterCreation = new \DateTimeImmutable();

        // Assert timestamps
        $this->assertGreaterThanOrEqual($beforeCreation, $operation->getStartedAt());
        $this->assertLessThanOrEqual($afterCreation, $operation->getStartedAt());
        $this->assertNull($operation->getCompletedAt());

        // Complete operation and check completion timestamp
        $beforeCompletion = new \DateTimeImmutable();
        $operation->complete(true, ['result' => 'success']);
        $afterCompletion = new \DateTimeImmutable();

        $this->assertNotNull($operation->getCompletedAt());
        $this->assertGreaterThanOrEqual($beforeCompletion, $operation->getCompletedAt());
        $this->assertLessThanOrEqual($afterCompletion, $operation->getCompletedAt());
    }

    public function testMultipleFileIdsHandling()
    {
        // Arrange
        $fileIds = [1, 2, 3, 4, 5];
        $userInfo = UserInfo::create(123, 1, 456);
        
        $operation = EncodingOperation::create(
            id: 'test_operation_123',
            projectId: 123,
            fileIds: $fileIds,
            userInfo: $userInfo
        );

        // Assert
        $this->assertEquals($fileIds, $operation->getFileIds());
        $this->assertCount(5, $operation->getFileIds());
    }
}
