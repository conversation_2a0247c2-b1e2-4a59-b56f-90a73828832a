<?php

declare(strict_types=1);

namespace tests\unit\kess3\infrastructure;

use PHPUnit\Framework\TestCase;
use common\chip\externalIntegrations\kess3\Infrastructure\EventHandler\EncodingEventHandler;
use common\chip\externalIntegrations\kess3\Domain\Events\EncodingCompletedEvent;
use common\chip\event\routing\NotificationRouter;

class EncodingEventHandlerNotificationTest extends TestCase
{
    public function testNotifyAuthorOnSuccess()
    {
        $router = $this->createMock(NotificationRouter::class);
        $router->expects($this->once())
            ->method('handleEvent')
            ->with($this->isInstanceOf(EncodingCompletedEvent::class), ['role' => 'author']);

        $event = new EncodingCompletedEvent('op-1', 1, 'file.bin', new \DateTimeImmutable(), true);
        $handler = new EncodingEventHandler($router);
        $handler->onEncodingCompleted($event);
    }

    public function testNotifyAdminOnError()
    {
        $router = $this->createMock(NotificationRouter::class);
        $router->expects($this->once())
            ->method('handleEvent')
            ->with($this->isInstanceOf(EncodingCompletedEvent::class), ['role' => 'admin']);

        $event = new EncodingCompletedEvent('op-2', 2, 'file2.bin', new \DateTimeImmutable(), false, 'Ошибка энкодинга');
        $handler = new EncodingEventHandler($router);
        $handler->onEncodingCompleted($event);
    }
}
