<?php

declare(strict_types=1);

namespace tests\unit\kess3\infrastructure;

use PHPUnit\Framework\TestCase;
use common\chip\externalIntegrations\kess3\Infrastructure\DTO\EncodingRequestDto;
use common\chip\externalIntegrations\kess3\Infrastructure\DTO\EncodingResultDto;

class EncodingRequestResultDtoTest extends TestCase
{
    public function testEncodingRequestDtoProperties()
    {
        $dto = new EncodingRequestDto(42, 'test.bin', '/tmp/test.bin', ['opt' => 1]);
        $this->assertSame(42, $dto->projectId);
        $this->assertSame('test.bin', $dto->fileName);
        $this->assertSame('/tmp/test.bin', $dto->filePath);
        $this->assertSame(['opt' => 1], $dto->options);
    }

    public function testEncodingResultDtoProperties()
    {
        $dto = new EncodingResultDto(true, null, '/tmp/encoded.bin', ['meta' => 'val']);
        $this->assertTrue($dto->success);
        $this->assertNull($dto->error);
        $this->assertSame('/tmp/encoded.bin', $dto->encodedFilePath);
        $this->assertSame(['meta' => 'val'], $dto->meta);
    }
}
