<?php

namespace tests\unit\kess3\domain;

use common\chip\externalIntegrations\kess3\Domain\ValueObject\OperationId;
use common\chip\externalIntegrations\kess3\Domain\ValueObject\ProjectId;
use common\chip\externalIntegrations\kess3\Domain\ValueObject\FileId;
use common\chip\externalIntegrations\kess3\Domain\ValueObject\DecodingStatus;
use PHPUnit\Framework\TestCase;

/**
 * Unit тесты для Value Objects доменного слоя
 */
class ValueObjectsTest extends TestCase
{
    /**
     * Тест OperationId
     */
    public function testOperationId()
    {
        // Тест генерации
        $operationId = OperationId::generate();
        $this->assertNotEmpty($operationId->getValue());
        $this->assertIsString($operationId->getValue());
        
        // Тест создания из строки
        $idString = 'op-123-456-789';
        $operationId2 = OperationId::fromString($idString);
        $this->assertEquals($idString, $operationId2->getValue());
        
        // Тест equals
        $operationId3 = OperationId::fromString($idString);
        $this->assertTrue($operationId2->equals($operationId3));
        $this->assertFalse($operationId->equals($operationId2));
        
        // Тест toString
        $this->assertEquals($idString, (string) $operationId2);
        
        // Тест валидации
        $this->expectException(\InvalidArgumentException::class);
        OperationId::fromString('');
    }

    /**
     * Тест ProjectId
     */
    public function testProjectId()
    {
        // Тест создания из int
        $projectId = ProjectId::fromInt(123);
        $this->assertEquals(123, $projectId->getValue());
        
        // Тест equals
        $projectId2 = ProjectId::fromInt(123);
        $projectId3 = ProjectId::fromInt(456);
        
        $this->assertTrue($projectId->equals($projectId2));
        $this->assertFalse($projectId->equals($projectId3));
        
        // Тест toString
        $this->assertEquals('123', (string) $projectId);
        
        // Тест валидации
        $this->expectException(\InvalidArgumentException::class);
        ProjectId::fromInt(0);
    }

    /**
     * Тест FileId
     */
    public function testFileId()
    {
        // Тест создания из int
        $fileId = FileId::fromInt(456);
        $this->assertEquals(456, $fileId->getValue());
        
        // Тест equals
        $fileId2 = FileId::fromInt(456);
        $fileId3 = FileId::fromInt(789);
        
        $this->assertTrue($fileId->equals($fileId2));
        $this->assertFalse($fileId->equals($fileId3));
        
        // Тест toString
        $this->assertEquals('456', (string) $fileId);
        
        // Тест валидации
        $this->expectException(\InvalidArgumentException::class);
        FileId::fromInt(-1);
    }

    /**
     * Тест DecodingStatus
     */
    public function testDecodingStatus()
    {
        // Тест создания статусов
        $inProgress = DecodingStatus::inProgress();
        $completed = DecodingStatus::completed();
        $failed = DecodingStatus::failed();
        
        // Тест проверки статусов
        $this->assertTrue($inProgress->isInProgress());
        $this->assertFalse($inProgress->isCompleted());
        $this->assertFalse($inProgress->isFailed());
        
        $this->assertFalse($completed->isInProgress());
        $this->assertTrue($completed->isCompleted());
        $this->assertFalse($completed->isFailed());
        
        $this->assertFalse($failed->isInProgress());
        $this->assertFalse($failed->isCompleted());
        $this->assertTrue($failed->isFailed());
        
        // Тест equals
        $inProgress2 = DecodingStatus::inProgress();
        $this->assertTrue($inProgress->equals($inProgress2));
        $this->assertFalse($inProgress->equals($completed));
        
        // Тест toString
        $this->assertEquals('in_progress', (string) $inProgress);
        $this->assertEquals('completed', (string) $completed);
        $this->assertEquals('failed', (string) $failed);
        
        // Тест создания из строки
        $fromString = DecodingStatus::fromString('in_progress');
        $this->assertTrue($fromString->equals($inProgress));
        
        // Тест валидации
        $this->expectException(\InvalidArgumentException::class);
        DecodingStatus::fromString('invalid_status');
    }

    /**
     * Тест сериализации Value Objects
     */
    public function testSerialization()
    {
        $operationId = OperationId::generate();
        $projectId = ProjectId::fromInt(123);
        $fileId = FileId::fromInt(456);
        $status = DecodingStatus::inProgress();
        
        // Тест JSON serialization
        $this->assertEquals('"' . $operationId->getValue() . '"', json_encode($operationId));
        $this->assertEquals('123', json_encode($projectId));
        $this->assertEquals('456', json_encode($fileId));
        $this->assertEquals('"in_progress"', json_encode($status));
    }

    /**
     * Тест immutability Value Objects
     */
    public function testImmutability()
    {
        $operationId = OperationId::generate();
        $originalValue = $operationId->getValue();
        
        // Value objects должны быть immutable
        // Попытка изменить значение не должна влиять на оригинальный объект
        $newOperationId = OperationId::fromString('new-value');
        $this->assertEquals($originalValue, $operationId->getValue());
        $this->assertNotEquals($originalValue, $newOperationId->getValue());
    }

    /**
     * Тест производительности создания множества Value Objects
     */
    public function testPerformance()
    {
        $startTime = microtime(true);
        
        // Создаем множество Value Objects
        for ($i = 0; $i < 1000; $i++) {
            $operationId = OperationId::generate();
            $projectId = ProjectId::fromInt($i + 1);
            $fileId = FileId::fromInt($i + 1);
            $status = DecodingStatus::inProgress();
        }
        
        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;
        
        // Проверяем что создание 1000 объектов занимает меньше 1 секунды
        $this->assertLessThan(1.0, $executionTime, 'Value Object creation should be fast');
    }

    /**
     * Тест уникальности генерируемых OperationId
     */
    public function testOperationIdUniqueness()
    {
        $ids = [];
        
        // Генерируем 100 ID и проверяем уникальность
        for ($i = 0; $i < 100; $i++) {
            $id = OperationId::generate();
            $value = $id->getValue();
            
            $this->assertNotContains($value, $ids, 'Generated OperationId should be unique');
            $ids[] = $value;
        }
    }

    /**
     * Тест граничных значений
     */
    public function testBoundaryValues()
    {
        // Тест максимальных значений для ProjectId и FileId
        $maxInt = PHP_INT_MAX;
        
        $projectId = ProjectId::fromInt($maxInt);
        $this->assertEquals($maxInt, $projectId->getValue());
        
        $fileId = FileId::fromInt($maxInt);
        $this->assertEquals($maxInt, $fileId->getValue());
        
        // Тест минимальных допустимых значений
        $minProjectId = ProjectId::fromInt(1);
        $this->assertEquals(1, $minProjectId->getValue());
        
        $minFileId = FileId::fromInt(1);
        $this->assertEquals(1, $minFileId->getValue());
    }
}
