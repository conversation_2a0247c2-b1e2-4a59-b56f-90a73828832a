<?php

declare(strict_types=1);

namespace tests\unit\kess3\domain;

use common\chip\externalIntegrations\kess3\Domain\Entity\EncodingOperation;
use common\chip\externalIntegrations\kess3\Domain\ValueObject\EncodingStatus;
use common\chip\externalIntegrations\kess3\Domain\ValueObject\OperationId;
use common\chip\externalIntegrations\kess3\Domain\ValueObject\ProjectId;
use PHPUnit\Framework\TestCase;

/**
 * Тесты для EncodingOperation entity
 */
final class EncodingOperationTest extends TestCase
{
    private EncodingOperation $operation;
    private OperationId $operationId;
    private ProjectId $projectId;
    private array $fileIds;

    protected function setUp(): void
    {
        $this->operationId = OperationId::generate();
        $this->projectId = ProjectId::fromInt(123);
        $this->fileIds = [1, 2, 3];
        
        $this->operation = EncodingOperation::create(
            operationId: $this->operationId,
            projectId: $this->projectId,
            fileIds: $this->fileIds,
            callbackUrl: 'https://example.com/callback'
        );
    }

    public function testCreate(): void
    {
        $this->assertEquals($this->operationId, $this->operation->getOperationId());
        $this->assertEquals($this->projectId, $this->operation->getProjectId());
        $this->assertEquals($this->fileIds, $this->operation->getFileIds());
        $this->assertEquals('https://example.com/callback', $this->operation->getCallbackUrl());
        $this->assertTrue($this->operation->getStatus()->isInProgress());
        $this->assertFalse($this->operation->isStarted());
        $this->assertNotNull($this->operation->getCreatedAt());
    }

    public function testStart(): void
    {
        $externalOperationId = 'ext-123';
        $slotGuid = 'slot-456';

        $this->operation->start($externalOperationId, $slotGuid);

        $this->assertTrue($this->operation->isStarted());
        $this->assertEquals($externalOperationId, $this->operation->getExternalOperationId());
        $this->assertEquals($slotGuid, $this->operation->getSlotGuid());
        $this->assertNotNull($this->operation->getStartedAt());
    }

    public function testStartThrowsExceptionWhenNotInProgress(): void
    {
        $this->operation->complete(['result' => 'success']);

        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Operation can only be started when in progress');

        $this->operation->start('ext-123', 'slot-456');
    }

    public function testComplete(): void
    {
        $result = ['encodedFiles' => ['file1.bin', 'file2.bin']];

        $this->operation->complete($result);

        $this->assertTrue($this->operation->isCompleted());
        $this->assertTrue($this->operation->isFinished());
        $this->assertEquals($result, $this->operation->getResult());
        $this->assertNotNull($this->operation->getCompletedAt());
    }

    public function testCompleteThrowsExceptionWhenNotInProgress(): void
    {
        $this->operation->fail(['error' => 'test error']);

        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Operation can only be completed when in progress');

        $this->operation->complete(['result' => 'success']);
    }

    public function testFail(): void
    {
        $error = ['message' => 'Encoding failed', 'code' => 500];

        $this->operation->fail($error);

        $this->assertTrue($this->operation->isFailed());
        $this->assertTrue($this->operation->isFinished());
        $this->assertEquals($error, $this->operation->getError());
        $this->assertNotNull($this->operation->getCompletedAt());
    }

    public function testFailThrowsExceptionWhenAlreadyFinished(): void
    {
        $this->operation->complete(['result' => 'success']);

        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Cannot fail an already finished operation');

        $this->operation->fail(['error' => 'test error']);
    }

    public function testCancel(): void
    {
        $this->operation->cancel();

        $this->assertTrue($this->operation->getStatus()->isCancelled());
        $this->assertTrue($this->operation->isFinished());
        $this->assertNotNull($this->operation->getCompletedAt());
    }

    public function testCancelThrowsExceptionWhenAlreadyFinished(): void
    {
        $this->operation->complete(['result' => 'success']);

        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Cannot cancel an already finished operation');

        $this->operation->cancel();
    }

    public function testSettersAndGetters(): void
    {
        $userInfo = ['userId' => 123, 'username' => 'test'];
        $result = ['encodedFiles' => ['file1.bin']];
        $error = ['message' => 'test error'];

        $this->operation->setUserInfo($userInfo);
        $this->operation->setResult($result);
        $this->operation->setError($error);

        $this->assertEquals($userInfo, $this->operation->getUserInfo());
        $this->assertEquals($result, $this->operation->getResult());
        $this->assertEquals($error, $this->operation->getError());
    }

    public function testStatusChecks(): void
    {
        // Initial state
        $this->assertTrue($this->operation->getStatus()->isInProgress());
        $this->assertFalse($this->operation->isStarted());
        $this->assertFalse($this->operation->isCompleted());
        $this->assertFalse($this->operation->isFailed());
        $this->assertFalse($this->operation->isFinished());

        // After start
        $this->operation->start('ext-123');
        $this->assertTrue($this->operation->isStarted());
        $this->assertFalse($this->operation->isFinished());

        // After completion
        $this->operation->complete(['result' => 'success']);
        $this->assertTrue($this->operation->isCompleted());
        $this->assertTrue($this->operation->isFinished());
        $this->assertFalse($this->operation->isFailed());
    }
}
