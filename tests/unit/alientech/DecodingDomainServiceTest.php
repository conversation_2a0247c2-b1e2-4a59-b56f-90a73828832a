<?php

namespace tests\unit\alientech;

use common\chip\externalIntegrations\alientech\Domain\Decoding\Entity\DecodingOperation;
use common\chip\externalIntegrations\alientech\Domain\Decoding\Repository\DecodingRepositoryInterface;
use common\chip\externalIntegrations\alientech\Domain\Decoding\Service\DecodingDomainService;
use common\chip\externalIntegrations\alientech\Domain\Shared\FileId;
use common\chip\externalIntegrations\alientech\Domain\Shared\ProjectId;
use PHPUnit\Framework\TestCase;

/**
 * Тесты для доменного сервиса декодирования
 */
class DecodingDomainServiceTest extends TestCase
{
    private DecodingDomainService $service;
    private DecodingRepositoryInterface $repository;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->repository = $this->createMock(DecodingRepositoryInterface::class);
        $this->service = new DecodingDomainService($this->repository);
    }

    public function testCreateOperationSuccess()
    {
        // Arrange
        $projectId = ProjectId::fromInt(123);
        $fileId = FileId::fromInt(456);
        $filePath = '/test/file.bin';
        $callbackUrl = 'http://test.com/callback';

        $this->repository
            ->expects($this->once())
            ->method('findByFileId')
            ->with($fileId)
            ->willReturn([]);

        // Act
        $operation = $this->service->createOperation($projectId, $fileId, $filePath, $callbackUrl);

        // Assert
        $this->assertInstanceOf(DecodingOperation::class, $operation);
        $this->assertEquals($projectId, $operation->getProjectId());
        $this->assertEquals($fileId, $operation->getFileId());
        $this->assertEquals($filePath, $operation->getFilePath());
        $this->assertEquals($callbackUrl, $operation->getCallbackUrl());
        $this->assertTrue($operation->getStatus()->isInProgress());
    }

    public function testCreateOperationFailsWhenFileAlreadyInProgress()
    {
        // Arrange
        $projectId = ProjectId::fromInt(123);
        $fileId = FileId::fromInt(456);
        $filePath = '/test/file.bin';
        $callbackUrl = 'http://test.com/callback';

        $existingOperation = $this->createMock(DecodingOperation::class);
        $existingOperation->method('getStatus')->willReturn(
            $this->createMock(\common\chip\externalIntegrations\alientech\Domain\Decoding\ValueObject\DecodingStatus::class)
        );
        $existingOperation->method('getStatus')->willReturn(
            \common\chip\externalIntegrations\alientech\Domain\Decoding\ValueObject\DecodingStatus::inProgress()
        );

        $this->repository
            ->expects($this->once())
            ->method('findByFileId')
            ->with($fileId)
            ->willReturn([$existingOperation]);

        // Act & Assert
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Decoding operation for file 456 is already in progress');

        $this->service->createOperation($projectId, $fileId, $filePath, $callbackUrl);
    }

    public function testCanStartDecoding()
    {
        // Arrange
        $operation = $this->createMock(DecodingOperation::class);
        $operation->method('getStatus')->willReturn(
            \common\chip\externalIntegrations\alientech\Domain\Decoding\ValueObject\DecodingStatus::inProgress()
        );
        $operation->method('isStarted')->willReturn(false);

        // Act
        $result = $this->service->canStartDecoding($operation);

        // Assert
        $this->assertTrue($result);
    }

    public function testCannotStartDecodingWhenAlreadyStarted()
    {
        // Arrange
        $operation = $this->createMock(DecodingOperation::class);
        $operation->method('getStatus')->willReturn(
            \common\chip\externalIntegrations\alientech\Domain\Decoding\ValueObject\DecodingStatus::inProgress()
        );
        $operation->method('isStarted')->willReturn(true);

        // Act
        $result = $this->service->canStartDecoding($operation);

        // Assert
        $this->assertFalse($result);
    }

    public function testGetProjectStatistics()
    {
        // Arrange
        $projectId = ProjectId::fromInt(123);
        
        $operation1 = $this->createMock(DecodingOperation::class);
        $operation1->method('getStatus')->willReturn(
            \common\chip\externalIntegrations\alientech\Domain\Decoding\ValueObject\DecodingStatus::inProgress()
        );
        
        $operation2 = $this->createMock(DecodingOperation::class);
        $operation2->method('getStatus')->willReturn(
            \common\chip\externalIntegrations\alientech\Domain\Decoding\ValueObject\DecodingStatus::completed()
        );

        $this->repository
            ->expects($this->once())
            ->method('findByProjectId')
            ->with($projectId)
            ->willReturn([$operation1, $operation2]);

        // Act
        $stats = $this->service->getProjectStatistics($projectId);

        // Assert
        $this->assertEquals(2, $stats['total']);
        $this->assertEquals(1, $stats['in_progress']);
        $this->assertEquals(1, $stats['completed']);
        $this->assertEquals(0, $stats['failed']);
        $this->assertEquals(0, $stats['cancelled']);
    }

    public function testFindExpiredOperations()
    {
        // Arrange
        $timeoutMinutes = 30;
        
        $expiredOperation = $this->createMock(DecodingOperation::class);
        $expiredOperation->method('getCreatedAt')->willReturn(
            new \DateTimeImmutable('-1 hour')
        );
        $expiredOperation->method('getStartedAt')->willReturn(null);

        $recentOperation = $this->createMock(DecodingOperation::class);
        $recentOperation->method('getCreatedAt')->willReturn(
            new \DateTimeImmutable('-10 minutes')
        );
        $recentOperation->method('getStartedAt')->willReturn(null);

        $this->repository
            ->expects($this->once())
            ->method('findInProgress')
            ->willReturn([$expiredOperation, $recentOperation]);

        // Act
        $expiredOperations = $this->service->findExpiredOperations($timeoutMinutes);

        // Assert
        $this->assertCount(1, $expiredOperations);
        $this->assertSame($expiredOperation, $expiredOperations[0]);
    }

    public function testCancelExpiredOperations()
    {
        // Arrange
        $timeoutMinutes = 30;
        
        $expiredOperation = $this->createMock(DecodingOperation::class);
        $expiredOperation->method('getCreatedAt')->willReturn(
            new \DateTimeImmutable('-1 hour')
        );
        $expiredOperation->method('getStartedAt')->willReturn(null);
        $expiredOperation->expects($this->once())->method('cancel');

        $this->repository
            ->expects($this->once())
            ->method('findInProgress')
            ->willReturn([$expiredOperation]);

        $this->repository
            ->expects($this->once())
            ->method('save')
            ->with($expiredOperation);

        // Act
        $cancelledCount = $this->service->cancelExpiredOperations($timeoutMinutes);

        // Assert
        $this->assertEquals(1, $cancelledCount);
    }
}
