version: "3"

services:
  app:
    build: docker/php
    volumes:
      - ./:/app
    restart: unless-stopped
    env_file:
      - .env
    ports:
      - 9009:9000
    networks:
      - chip-network

  nginx:
    image: nginx:1.23-alpine
    ports:
      - 80:80
    #    cap_drop:
    #      - ALL
    volumes:
      - ./:/app
      - ./docker/nginx/vhost.conf:/etc/nginx/conf.d/vhost.conf
    depends_on:
      - app
    networks:
      - chip-network

  db:
    platform: linux/x86_64
    image: mysql:5.7
    volumes:
      - ./:/app
      #      - /var/lib/mysql
      - ./database:/var/lib/mysql
      - ./docker/mysql/config.cnf:/etc/mysql/my.cnf
      # /etc/mysql/my.cnf must be chmod 644
    #      - ./docker/data/:/docker-entrypoint-initdb.d/
    ports:
      - 3306:3306
    #    cap_drop:
    #      - ALL
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_USER: ${DB_USERNAME}
      MYSQL_PASSWORD: ${DB_PASSWORD}
      MYSQL_ROOT_HOST: '%'
    networks:
      - chip-network

  php-supervisor:
    build:
      context: ./docker/php/supervisor
    restart: unless-stopped
    env_file:
      - .env
    volumes:
      - ./:/app
      - ./docker/php/supervisor/supervisor.conf:/etc/supervisor/conf.d/supervisor.conf
      - ./console/runtime/logs:/var/log/supervisor
    depends_on:
      - app
    networks:
      - chip-network

  python:
    build:
      context: ./docker/python
    container_name: chip_app_python
    command: uvicorn app:app --host 0.0.0.0 --port 5001 --workers 4
    restart: unless-stopped
    ports:
      - 5001:5001
    volumes:
      - ./:/app
    networks:
      - chip-network

  webpacker:
    image: node:9-alpine
    restart: unless-stopped
    working_dir: /app
    volumes:
      - ./:/app
    command: /bin/true

networks:
  chip-network:
    driver: bridge
