<?php

namespace notification;

use common\chip\event\routing\NotificationRouter;
use notification\Core\Event\BaseEvent;
use notification\Core\Event\Bus\EventBusInterface;
use notification\Core\Event\FileUploadedEvent;
use notification\Core\Event\NoteAddedEvent;
use notification\Core\Event\ProjectCreatedEvent;
use notification\Core\TemplateEngine;
use notification\Factory\NotificationFactory;
use notification\Legacy\LegacyEventEmitter;
use notification\Legacy\LegacyNotificationAdapter;
use notification\Service\AsyncEventService;
use notification\Service\EventHandlerService;
use notification\Service\NotificationService;

/**
 * Фасад для системы уведомлений с поддержкой асинхронной обработки
 */
class NotificationFacade
{
    /**
     * Флаг использования новой системы уведомлений
     * 
     * @var bool
     */
    public bool $useNewSystem = true;
    
    /**
     * Флаги для конкретных функций
     * 
     * @var array<string, bool>
     */
    public array $featureFlags = [];
    
    /**
     * Флаги для каналов доставки
     * 
     * @var array<string, bool>
     */
    public array $channelFlags = [];
    
    /**
     * @param EventBusInterface $eventBus Шина событий
     * @param EventHandlerService $eventHandlerService Сервис обработки событий
     * @param TemplateEngine $templateEngine Движок шаблонов
     * @param NotificationFactory $notificationFactory Фабрика уведомлений
     * @param NotificationRouter $notificationRouter Маршрутизатор уведомлений
     * @param NotificationService $notificationService Сервис уведомлений
     * @param LegacyEventEmitter $legacyEventEmitter Эмиттер событий для устаревшей системы
     * @param LegacyNotificationAdapter $legacyAdapter Адаптер для устаревшей системы
     * @param AsyncEventService|null $asyncEventService Сервис для асинхронной обработки
     */
    public function __construct(
        private readonly EventBusInterface $eventBus,
        private readonly EventHandlerService $eventHandlerService,
        private readonly TemplateEngine $templateEngine,
        private readonly NotificationFactory $notificationFactory,
        private readonly NotificationRouter $notificationRouter,
        private readonly NotificationService $notificationService,
        private readonly LegacyEventEmitter $legacyEventEmitter,
        private readonly LegacyNotificationAdapter $legacyAdapter,
        private readonly ?AsyncEventService $asyncEventService = null
    ) {
    }
    
    /**
     * Инициализирует систему уведомлений
     * 
     * @return void
     */
    public function init(): void
    {
        // Регистрируем обработчик событий
        $this->eventHandlerService->registerHandlers();
        
        // Загружаем шаблоны уведомлений
        $this->loadTemplates();
    }
    
    /**
     * Загружает шаблоны уведомлений
     * 
     * @return void
     */
    private function loadTemplates(): void
    {
        // Здесь может быть загрузка шаблонов из конфигурации или базы данных
    }
    
    /**
     * Отправляет уведомление о создании проекта
     * 
     * @param array $projectData Данные о проекте
     * @param bool $async Флаг асинхронной обработки
     * @return void
     */
    public function notifyProjectCreated(array $projectData, bool $async = false): void
    {
        if (!$this->useNewSystem) {
            $this->legacyAdapter->sendLegacyNotification(
                $projectData['id'],
                'Новый проект',
                "Создан новый проект {$projectData['name']}"
            );
            return;
        }
        
        // Создаем событие
        $event = new ProjectCreatedEvent();
        $event->setContextData('projectId', $projectData['id']);
        $event->setContextData('projectName', $projectData['name']);
        $event->setContextData('createdBy', $projectData['createdBy']);
        
        // Публикуем событие
        $this->publishEvent($event, $async);
    }
    
    /**
     * Отправляет уведомление о добавлении заметки
     * 
     * @param array $noteData Данные о заметке
     * @param bool $async Флаг асинхронной обработки
     * @return void
     */
    public function notifyNoteAdded(array $noteData, bool $async = false): void
    {
        if (!$this->useNewSystem) {
            $this->legacyAdapter->sendLegacyNotification(
                $noteData['projectId'],
                'Новая заметка',
                "Добавлена новая заметка: {$noteData['title']}"
            );
            return;
        }
        
        // Создаем событие
        $event = new NoteAddedEvent();
        $event->setContextData('noteId', $noteData['id']);
        $event->setContextData('noteTitle', $noteData['title']);
        $event->setContextData('projectId', $noteData['projectId']);
        $event->setContextData('createdBy', $noteData['createdBy']);
        
        // Публикуем событие
        $this->publishEvent($event, $async);
    }
    
    /**
     * Отправляет уведомление о загрузке файла
     * 
     * @param array $fileData Данные о файле
     * @param bool $async Флаг асинхронной обработки
     * @return void
     */
    public function notifyFileUploaded(array $fileData, bool $async = false): void
    {
        if (!$this->useNewSystem) {
            $this->legacyAdapter->sendLegacyNotification(
                $fileData['projectId'],
                'Новый файл',
                "Загружен новый файл: {$fileData['name']}"
            );
            return;
        }
        
        // Создаем событие
        $event = new FileUploadedEvent();
        $event->setContextData('fileId', $fileData['id']);
        $event->setContextData('fileName', $fileData['name']);
        $event->setContextData('projectId', $fileData['projectId']);
        $event->setContextData('uploadedBy', $fileData['uploadedBy']);
        
        // Публикуем событие
        $this->publishEvent($event, $async);
    }
    
    /**
     * Публикует событие в шину событий
     * 
     * @param BaseEvent $event Событие
     * @param bool $async Флаг асинхронной обработки
     * @return void
     */
    private function publishEvent(BaseEvent $event, bool $async = false): void
    {
        if ($async && $this->asyncEventService !== null) {
            $this->eventBus->publishAsync($event);
        } else {
            $this->eventBus->publish($event);
        }
    }
    
    /**
     * Получает непрочитанные уведомления для пользователя
     * 
     * @param int|string $userId Идентификатор пользователя
     * @return array Массив непрочитанных уведомлений
     */
    public function getUnreadNotificationsForUser(int|string $userId): array
    {
        if (!$this->useNewSystem) {
            // Используем устаревшую систему
            return [];
        }
        
        return $this->notificationService->getUnreadNotificationsForUser($userId);
    }
    
    /**
     * Устанавливает флаг для асинхронной обработки указанного типа события
     * 
     * @param string $eventType Тип события
     * @param bool $async Флаг асинхронной обработки
     * @return void
     */
    public function setEventTypeAsync(string $eventType, bool $async = true): void
    {
        if ($this->asyncEventService === null) {
            return;
        }
        
        if ($async) {
            $this->asyncEventService->registerAsyncEventType($eventType);
        } else {
            $this->asyncEventService->unregisterAsyncEventType($eventType);
        }
    }
    
    /**
     * Устанавливает флаг для асинхронной доставки через указанный канал
     * 
     * @param string $channelId Идентификатор канала
     * @param bool $async Флаг асинхронной доставки
     * @return void
     */
    public function setChannelAsync(string $channelId, bool $async = true): void
    {
        if ($this->asyncEventService === null) {
            return;
        }
        
        if ($async) {
            $this->asyncEventService->registerAsyncChannel($channelId);
        } else {
            $this->asyncEventService->unregisterAsyncChannel($channelId);
        }
    }
}
