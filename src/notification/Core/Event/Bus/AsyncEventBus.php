<?php

namespace notification\Core\Event\Bus;

use common\chip\notification\core\event\BaseEvent;
use common\chip\notification\Job\EventProcessJob;
use yii\queue\Queue;

/**
 * Реализация шины событий с асинхронной обработкой через Yii Queue
 */
class AsyncEventBus implements EventBusInterface
{
    /**
     * @param Queue $queue Компонент очереди Yii
     * @param StandardEventBus $syncBus Синхронная шина событий для делегирования
     * @param array $asyncEventTypes Типы событий, которые обрабатываются асинхронно
     */
    public function __construct(
        private readonly Queue $queue,
        private readonly StandardEventBus $syncBus,
        private array $asyncEventTypes = []
    ) {
    }
    
    /**
     * {@inheritdoc}
     */
    public function subscribe(string $eventType, callable $listener): void
    {
        $this->syncBus->subscribe($eventType, $listener);
    }
    
    /**
     * {@inheritdoc}
     */
    public function unsubscribe(string $eventType, callable $listener): void
    {
        $this->syncBus->unsubscribe($eventType, $listener);
    }
    
    /**
     * {@inheritdoc}
     */
    public function publish(BaseEvent $event): void
    {
        $eventType = get_class($event);
        
        // Если тип события в списке асинхронных, публикуем асинхронно
        if (in_array($eventType, $this->asyncEventTypes, true)) {
            $this->publishAsync($event);
            return;
        }
        
        // Иначе публикуем синхронно
        $this->syncBus->publish($event);
    }
    
    /**
     * {@inheritdoc}
     */
    public function publishAsync(BaseEvent $event, array $options = []): void
    {
        // Добавляем событие в журнал
        $this->syncBus->getEventLog();
        
        // Создаем задачу для обработки события
        $job = new EventProcessJob([
            'event' => serialize($event),
            'eventType' => get_class($event),
            'options' => $options
        ]);
        
        // Помещаем задачу в очередь с задержкой, если указана
        $delay = $options['delay'] ?? 0;
        $this->queue->delay($delay)->push($job);
    }
    
    /**
     * {@inheritdoc}
     */
    public function getEventLog(): array
    {
        return $this->syncBus->getEventLog();
    }
    
    /**
     * Регистрирует тип события для асинхронной обработки
     * 
     * @param string $eventType Тип события
     * @return void
     */
    public function registerAsyncEventType(string $eventType): void
    {
        if (!in_array($eventType, $this->asyncEventTypes, true)) {
            $this->asyncEventTypes[] = $eventType;
        }
    }
    
    /**
     * Удаляет тип события из списка асинхронных
     * 
     * @param string $eventType Тип события
     * @return void
     */
    public function unregisterAsyncEventType(string $eventType): void
    {
        $key = array_search($eventType, $this->asyncEventTypes, true);
        
        if ($key !== false) {
            unset($this->asyncEventTypes[$key]);
            $this->asyncEventTypes = array_values($this->asyncEventTypes);
        }
    }
    
    /**
     * Возвращает список типов событий, обрабатываемых асинхронно
     * 
     * @return array Массив типов событий
     */
    public function getAsyncEventTypes(): array
    {
        return $this->asyncEventTypes;
    }
}
