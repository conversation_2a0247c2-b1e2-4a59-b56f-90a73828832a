<?php

use yii\db\Migration;

/**
 * Миграция для создания таблицы notification_deliveries
 */
class m241101_000003_create_notification_deliveries_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Создаем таблицу с явным указанием кодировки UTF-8
        $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';

        $this->createTable('{{%notification_deliveries}}', [
            'id' => $this->primaryKey(),
            'notification_id' => $this->integer()->notNull()->comment('ID уведомления'),
            'channel_id' => $this->integer()->notNull()->comment('ID канала'),

            // Получатель может быть не только пользователем
            'recipient_type' => $this->string(50)->notNull()->defaultValue('user')->comment('Тип получателя (user, role, group, project, system)'),
            'recipient_id' => $this->string(100)->notNull()->comment('ID получателя'),

            'status' => "ENUM('pending', 'delivered', 'read', 'clicked', 'failed') NOT NULL DEFAULT 'pending'",
            'display_location' => $this->string(100)->comment('Место отображения (header, sidebar, modal и т.д.)'),
            'display_count' => $this->integer()->notNull()->defaultValue(0)->comment('Сколько раз уведомление было показано'),
            'max_displays' => $this->integer()->null()->comment('Максимальное количество показов'),
            'show_until_clicked' => $this->boolean()->notNull()->defaultValue(false)->comment('Показывать, пока не кликнут'),
            'delivered_at' => $this->timestamp()->null()->comment('Когда уведомление было доставлено'),
            'read_at' => $this->timestamp()->null()->comment('Когда уведомление было прочитано'),
            'clicked_at' => $this->timestamp()->null()->comment('Когда на уведомление кликнули'),
            'created_at' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP'),
            'updated_at' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP')->append('ON UPDATE CURRENT_TIMESTAMP'),
        ], $tableOptions);

        // Создаем индексы для оптимизации запросов
        $this->createIndex('idx_notification_deliveries_notification', '{{%notification_deliveries}}', 'notification_id');
        $this->createIndex('idx_notification_deliveries_channel', '{{%notification_deliveries}}', 'channel_id');
        $this->createIndex('idx_notification_deliveries_recipient', '{{%notification_deliveries}}', ['recipient_type', 'recipient_id']);
        $this->createIndex('idx_notification_deliveries_status', '{{%notification_deliveries}}', 'status');
        $this->createIndex('idx_notification_deliveries_display_location', '{{%notification_deliveries}}', 'display_location');
        $this->createIndex('idx_notification_deliveries_show_until_clicked', '{{%notification_deliveries}}', 'show_until_clicked');

        // Добавляем внешние ключи
        $this->addForeignKey(
            'fk_notification_deliveries_notification',
            '{{%notification_deliveries}}',
            'notification_id',
            '{{%notifications}}',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk_notification_deliveries_channel',
            '{{%notification_deliveries}}',
            'channel_id',
            '{{%notification_channels}}',
            'id',
            'CASCADE',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk_notification_deliveries_notification', '{{%notification_deliveries}}');
        $this->dropForeignKey('fk_notification_deliveries_channel', '{{%notification_deliveries}}');
        $this->dropTable('{{%notification_deliveries}}');
    }
}
