<?php

use yii\db\Migration;

/**
 * Class m241030_202615_add_project_report_table
 */
class m241030_202615_add_project_report_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%project_report}}', [
            'id' => $this->primaryKey(),
            'project_id' => $this->integer(11)->notNull()->defaultValue(0),
            'created_by' => $this->integer(11)->notNull()->defaultValue(0),
            'send_to' => $this->string(255)->notNull()->defaultValue(''),
            'title' => $this->string(255)->notNull()->defaultValue(''),
            'comment' => $this->text()->null(),
            'content' => $this->text()->null(),
            'created_at' => $this->timestamp()->null()->defaultExpression('CURRENT_TIMESTAMP'),
            'updated_at' => $this->timestamp()->null()->defaultExpression('CURRENT_TIMESTAMP')->append('ON UPDATE CURRENT_TIMESTAMP'),
            'isDeleted' => $this->integer(11)->notNull()->defaultValue(0),
            'deleted_by' => $this->integer(11)->notNull()->defaultValue(0),
            'deleted_at' => $this->timestamp()->null()
        ]);
    }

    /**
     *
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%project_report}}');
    }
}