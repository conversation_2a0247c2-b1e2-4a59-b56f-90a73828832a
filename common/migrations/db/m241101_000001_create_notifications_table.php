<?php

use yii\db\Migration;

/**
 * Миграция для создания таблицы notifications
 */
class m241101_000001_create_notifications_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Создаем таблицу с явным указанием кодировки UTF-8
        $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';

        $this->createTable('{{%notifications}}', [
            'id' => $this->primaryKey(),
            'type' => $this->string(50)->notNull()->comment('Тип уведомления'),
            'subject' => $this->string(255)->notNull()->comment('Краткое описание уведомления'),
            'content' => $this->text()->comment('Полное содержимое уведомления'),
            'context' => $this->json()->comment('Дополнительные данные в формате JSON'),

            // Информация об источнике
            'source_type' => "ENUM('user', 'service', 'system', 'external') NOT NULL DEFAULT 'system'",
            'source_id' => $this->string(100)->null()->comment('ID пользователя или код сервиса'),
            'source_context' => $this->json()->comment('Дополнительный контекст источника'),

            'importance' => "ENUM('low', 'normal', 'high') NOT NULL DEFAULT 'normal'",
            'created_at' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP'),
            'expires_at' => $this->timestamp()->null()->comment('Когда уведомление истекает'),
        ], $tableOptions);

        // Создаем индексы для оптимизации запросов
        $this->createIndex('idx_notifications_type', '{{%notifications}}', 'type');
        $this->createIndex('idx_notifications_source', '{{%notifications}}', ['source_type', 'source_id']);
        $this->createIndex('idx_notifications_created_at', '{{%notifications}}', 'created_at');
        $this->createIndex('idx_notifications_expires_at', '{{%notifications}}', 'expires_at');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%notifications}}');
    }
}
