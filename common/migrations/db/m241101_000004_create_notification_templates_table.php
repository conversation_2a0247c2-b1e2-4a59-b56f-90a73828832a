<?php

use yii\db\Migration;

/**
 * Миграция для создания таблицы notification_templates
 */
class m241101_000004_create_notification_templates_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Создаем таблицу с явным указанием кодировки UTF-8
        $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';

        $this->createTable('{{%notification_templates}}', [
            'id' => $this->primaryKey(),
            'type' => $this->string(50)->notNull()->comment('Тип уведомления'),
            'channel_id' => $this->integer()->notNull()->comment('Канал доставки'),
            'subject_template' => $this->text()->notNull()->comment('Шаблон заголовка'),
            'content_template' => $this->text()->notNull()->comment('Шаблон содержимого'),
            'default_display_location' => $this->string(100)->comment('Место отображения по умолчанию'),
            'default_importance' => "ENUM('low', 'normal', 'high') NOT NULL DEFAULT 'normal'",
            'default_show_until_clicked' => $this->boolean()->notNull()->defaultValue(false),
            'default_max_displays' => $this->integer()->null()->comment('Максимальное количество показов по умолчанию'),
            'created_at' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP'),
            'updated_at' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP')->append('ON UPDATE CURRENT_TIMESTAMP'),
        ], $tableOptions);

        // Создаем уникальный индекс для комбинации тип + канал
        $this->createIndex('unique_type_channel', '{{%notification_templates}}', ['type', 'channel_id'], true);

        // Добавляем внешний ключ
        $this->addForeignKey(
            'fk_notification_templates_channel',
            '{{%notification_templates}}',
            'channel_id',
            '{{%notification_channels}}',
            'id',
            'CASCADE',
            'CASCADE'
        );

        // Добавляем базовые шаблоны
        $this->batchInsert('{{%notification_templates}}',
            ['type', 'channel_id', 'subject_template', 'content_template', 'default_display_location', 'default_importance', 'default_show_until_clicked'],
            [
                // Шаблон для UI-уведомления о создании проекта
                ['project_created', 1, 'Проект создан', 'Проект "{{project_name}}" был успешно создан.', 'top-right', 'normal', false],

                // Шаблон для Telegram-уведомления о создании проекта
                ['project_created', 2, 'Новый проект создан', "*Новый проект создан*\n\nНазвание: *{{project_name}}*\nID проекта: `{{project_id}}`\nСоздан: {{created_at}}", null, 'normal', false],

                // Шаблон для нотисов проекта о создании проекта
                ['project_created', 3, 'Проект создан', 'Проект "{{project_name}}" был создан {{created_at}}', 'project_timeline', 'normal', false],

                // Шаблоны для добавления заметки
                ['project_note_added', 1, 'Добавлена заметка', 'К проекту "{{project_name}}" добавлена заметка "{{title}}".', 'top-right', 'normal', false],
                ['project_note_added', 2, 'Новая заметка', "*Новая заметка*\n\nПроект: *{{project_name}}*\nЗаголовок: *{{title}}*\nСодержание: {{content}}", null, 'normal', false],
                ['project_note_added', 3, 'Добавлена заметка', 'Добавлена заметка "{{title}}" {{created_at}}', 'project_timeline', 'normal', false],

                // Шаблоны для загрузки файла
                ['file_uploaded', 1, 'Файл загружен', 'В проект "{{project_name}}" загружен файл "{{file_name}}".', 'top-right', 'normal', false],
                ['file_uploaded', 2, 'Новый файл', "*Новый файл загружен*\n\nПроект: *{{project_name}}*\nФайл: *{{file_name}}*\nКомментарий: {{comment}}", null, 'normal', false],
                ['file_uploaded', 3, 'Файл загружен', 'Загружен файл "{{file_name}}" {{created_at}}', 'project_timeline', 'normal', false],
            ]
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk_notification_templates_channel', '{{%notification_templates}}');
        $this->dropTable('{{%notification_templates}}');
    }
}
