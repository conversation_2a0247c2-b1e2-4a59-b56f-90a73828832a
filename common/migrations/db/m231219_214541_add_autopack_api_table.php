<?php

use yii\db\Migration;


class m231219_214541_add_autopack_api_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%autopack_api}}', [
            'id' => $this->primaryKey(),
            'time' => $this->integer(11),
            'accessToken' => $this->string(255)->unique()
        ]);
    }

    /**
     *
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%autopack_api}}');
    }
}