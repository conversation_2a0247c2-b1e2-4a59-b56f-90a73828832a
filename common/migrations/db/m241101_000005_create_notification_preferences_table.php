<?php

use yii\db\Migration;

/**
 * Миграция для создания таблицы notification_preferences
 */
class m241101_000005_create_notification_preferences_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Создаем таблицу с явным указанием кодировки UTF-8
        $tableOptions = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';

        $this->createTable('{{%notification_preferences}}', [
            'id' => $this->primaryKey(),
            'user_id' => $this->integer()->notNull()->comment('ID пользователя'),
            'notification_type' => $this->string(50)->notNull()->comment('Тип уведомления'),
            'channel_id' => $this->integer()->notNull()->comment('Канал доставки'),
            'is_enabled' => $this->boolean()->notNull()->defaultValue(true)->comment('Включены ли уведомления этого типа'),
            'created_at' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP'),
            'updated_at' => $this->timestamp()->notNull()->defaultExpression('CURRENT_TIMESTAMP')->append('ON UPDATE CURRENT_TIMESTAMP'),
        ], $tableOptions);

        // Создаем уникальный индекс для комбинации пользователь + тип + канал
        $this->createIndex('unique_user_type_channel', '{{%notification_preferences}}', ['user_id', 'notification_type', 'channel_id'], true);

        // Создаем индексы для оптимизации запросов
        $this->createIndex('idx_notification_preferences_user_id', '{{%notification_preferences}}', 'user_id');
        $this->createIndex('idx_notification_preferences_notification_type', '{{%notification_preferences}}', 'notification_type');

        // Добавляем внешний ключ
        $this->addForeignKey(
            'fk_notification_preferences_channel',
            '{{%notification_preferences}}',
            'channel_id',
            '{{%notification_channels}}',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk_notification_preferences_user',
            '{{%notification_preferences}}',
            'user_id',
            '{{%user}}',
            'id',
            'CASCADE',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk_notification_preferences_channel', '{{%notification_preferences}}');
        $this->dropForeignKey('fk_notification_preferences_user', '{{%notification_preferences}}');
        $this->dropTable('{{%notification_preferences}}');
    }
}
