<?php

use yii\db\Migration;

/**
 * Class m190130_155645_add_article_slug_index
 */
class m230804_233325_add_telegram_accounts_table extends Migration
{
    /**
     *             'id' => Yii::t('backend', 'ID'),
    'user_id' => Yii::t('backend', 'User'),
    'chat_id' => Yii::t('backend', 'Chat Id'),
    'tg_account' => Yii::t('backend', 'Telegram account'),
    'created_at' => Yii::t('backend', 'Created At'),
    'updated_at' => Yii::t('backend', 'Updated At'),
    'deleted_at' => Yii::t('backend', 'Deleted At'),
    'updated_by' => Yii::t('backend', 'Updated By'),
    'deleted_by' => Yii::t('backend', 'Deleted By'),
    'isDeleted' => Yii::t('backend', 'Is Deleted'),

     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%telegram_account}}', [
            'id' => $this->primaryKey(),
            'user_id' => $this->integer()->notNull(),
            'chat_id' => $this->integer()->notNull(),
            'tg_account' => $this->string(255)->notNull(),
            'created_at' => $this->integer(),
            'updated_at' => $this->integer(),
            'deleted_at' => $this->integer(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
            'deleted_by' => $this->integer(),
            'isDeleted' => $this->integer(),
        ]);
        $this->addForeignKey('fk_telegram_account_user', '{{%telegram_account}}', 'user_id', '{{%user}}', 'id', 'cascade', 'cascade');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk_telegram_account_user', '{{%telegram_account}}');
        $this->dropTable('{{%file_storage_item}}');
    }
}