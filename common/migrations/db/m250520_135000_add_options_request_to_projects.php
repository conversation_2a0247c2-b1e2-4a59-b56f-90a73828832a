<?php

use yii\db\Migration;

/**
 * Класс миграции для добавления поля options_request в таблицу projects
 */
class m250520_135000_add_options_request_to_projects extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Добавляем текстовое поле options_request после поля comments
        $this->addColumn('{{%projects}}', 'options_request', $this->text()->null()->after('comments'));
        
        // Добавляем комментарий к полю
        $this->execute('ALTER TABLE {{%projects}} CHANGE `options_request` `options_request` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT "Запрос дополнительных опций"');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Удаляем поле options_request
        $this->dropColumn('{{%projects}}', 'options_request');
    }
}
