<?php

namespace common\models;

use common\helpers\AutopackHelper;
use common\models\query\AutopackSettingsQuery;
use Yii;

/**
 * This is the model class for table "autopack_settings".
 *
 * @property int $id
 * @property int $ecu_id
 * @property int $group_id
 * @property string $description
 * @property string $created_at
 * @property string $updated_at
 * @property int $created_by
 * @property int $updated_by
 * @property int $isDeleted
 * @property int $deleted_by
 */
class AutopackSetting extends BaseChipModel
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'autopack_settings';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['ecu_id', 'group_id', 'created_by', 'updated_by', 'isDeleted', 'deleted_by'], 'integer'],
            [['description'], 'string'],
            [['created_at', 'updated_at'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'ecu_id' => Yii::t('app', 'Ecu ID'),
            'group_id' => Yii::t('app', 'Group ID'),
            'description' => Yii::t('app', 'Description'),
            'created_at' => Yii::t('app', 'Created At'),
            'updated_at' => Yii::t('app', 'Updated At'),
            'created_by' => Yii::t('app', 'Created By'),
            'updated_by' => Yii::t('app', 'Updated By'),
            'isDeleted' => Yii::t('app', 'Is Deleted'),
            'deleted_by' => Yii::t('app', 'Deleted By'),
        ];
    }

    /**
     * {@inheritdoc}
     * @return AutopackSettingsQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new AutopackSettingsQuery(get_called_class());
    }

    public function getFiles()
    {
        return $this->hasMany(AutopackSettFile::class, ['id' => 'sett_file_id'])
            ->with('childrenFiles')
            ->viaTable('autopack_sett_rel', ['settings_id' => 'id'])
            ->where(['file_type' => AutopackHelper::FILE_TYPE_ORIGINAL])->andWhere('isDeleted is NULL');
    }

    public function getModFiles()
    {
        return $this->hasMany(AutopackSettFile::class, ['id' => 'sett_file_id'])
            ->with('childrenFiles')
            ->viaTable('autopack_sett_rel', ['settings_id' => 'id'])
            ->where(['file_type' => AutopackHelper::FILE_TYPE_MODIFIED])
            ->andWhere('isDeleted is NULL');
    }

    public function getAllFiles()
    {
        return $this->hasMany(AutopackSettFile::class, ['id' => 'sett_file_id'])
            ->with('childrenFiles')
            ->viaTable('autopack_sett_rel', ['settings_id' => 'id'])
            ->where('isDeleted is NULL');
    }

    public function getEcu()
    {
        return $this->hasOne(ChipEcuDict::class, ['id' => 'ecu_id']);
    }

    public function getGroup()
    {
        return $this->hasOne(AutopackGroup::class, ['id' => 'group_id']);
    }
}
