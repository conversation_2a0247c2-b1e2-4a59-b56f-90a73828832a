<?php

namespace common\models;

use common\models\query\ChipEcuDictQuery;
use Yii;

/**
 * This is the model class for table "chip_ecu_dict".
 *
 * @property int $id
 * @property string $title
 * @property string $slug
 * @property string $created_at
 * @property string $updated_at
 * @property string $deleted_at
 */
class ChipEcuDict extends BaseChipModel
{
//    public $brand_id;
//    public $model_id;
//    public $generation_id;
//    public $engine_id;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'chip_ecu_dict';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['title'], 'required'],
            [['created_at', 'updated_at', 'deleted_at'], 'safe'],
            [['title', 'slug'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'title' => Yii::t('app', 'Title'),
            'slug' => Yii::t('app', 'Slug'),
            'created_at' => Yii::t('app', 'Created At'),
            'updated_at' => Yii::t('app', 'Updated At'),
            'deleted_at' => Yii::t('app', 'Deleted At'),
        ];
    }

//    public function afterFind()
//    {
//        if ($this->ecu) {
//            $this->brand_id = isset($this->ecu->brand) ? $this->ecu->brand->id : null;
//            $this->model_id = isset($this->ecu->model) ? $this->ecu->model->id : null;
//            $this->generation_id = isset($this->ecu->generation) ? $this->ecu->generation->id : null;
//            $this->engine_id = isset($this->ecu->engine) ? $this->ecu->engine->id : null;
//        } else {
//            $this->brand_id = 0;
//            $this->model_id = 0;
//            $this->generation_id = 0;
//            $this->engine_id = 0;
//        }
//        parent::afterFind(); // TODO: Change the autogenerated stub
//    }
    /**
     * @return \yii\db\ActiveQuery
     */
    public function getAdditions()
    {
//        return $this->ecu->additions;
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getEcu()
    {
        $ecu = null;
        if ($ecu = $this->hasOne(ChipEcu::className(), ['ecu_id' => 'id'])) {
//            return $ecu;
        } else {
            $ecu = $this->hasOne(ChipEcu::className(), ['ecu_id' => 1904]);
//            return $ecu;
        }
        return $ecu; // todo если возникнет ошибка - надо возвращать объект по умолчанию
//        if ($this->id == 151) {
// echo '<pre>';
////        var_dump($this->id); die;
//            var_dump($ecu); die;
//        } else {
//            return $ecu;
//        }
    }

    /**
     * @return \yii\db\ActiveQuery
     */
//    public function getBrand()
//    {
//        return $this->ecu->brand;
//    }


    /**
     * @return \yii\db\ActiveQuery
     */
//    public function getModel()
//    {
//        return $this->ecu->model;
//    }

    /**
     * @return \yii\db\ActiveQuery
     */
//    public function getGeneration()
//    {
//        return $this->ecu->generation;
//    }

    /**
     * @return \yii\db\ActiveQuery
     */
//    public function getEngine()
//    {
//        return $this->ecu->engine;
//    }

    /**
     * @return \yii\db\ActiveQuery
     */
//    public function getStages()
//    {
//        return $this->hasOne(ChipStagesEcu::className(), ['ecu_id' => 'id']);
//    }

    /**
     * {@inheritdoc}
     * @return ChipEcuDictQuery the active query used by this AR class.
     */
    public static function find()
    {
        return parent::find(new ChipEcuDictQuery(get_called_class()));
    }

    public function afterSave($insert, $changedAttributes)
    {
//        $request = Yii::$app->request;
//        $post = $request->post();
//        echo 'actionCreate<pre>';
//        $chipEcuDict = isset($post['ChipEcuDict']) ? $post['ChipEcuDict']: null;
//        if (!empty($chipEcuDict)) {
//            ChipEcu::deleteAll([
//                'brand_id' => $chipEcuDict['brand_id'],
//                'model_id' => $chipEcuDict['model_id'],
//                'generation_id' => $chipEcuDict['generation_id'],
//                'engine_id' => $chipEcuDict['engine_id'],
//            ]);
//            $chipEcu = new ChipEcu();
//            $chipEcu->setAttribute('brand_id', $chipEcuDict['brand_id']);
//            $chipEcu->setAttribute('model_id', $chipEcuDict['model_id']);
//            $chipEcu->setAttribute('generation_id', $chipEcuDict['generation_id']);
//            $chipEcu->setAttribute('engine_id', $chipEcuDict['engine_id']);
//            $chipEcu->setAttribute('ecu_id', $this->id);
//            $chipEcu->setAttribute('title', $this->title);
//            $chipEcu->needAfterSave = false;
//            $chipEcu->save(false);
//        }

//        print_r($chipEcu->firstErrors);
//        die;
//        'brand_id', 'model_id', 'generation_id', 'engine_id'
        parent::afterSave($insert, $changedAttributes); // TODO: Change the autogenerated stub
    }
}
