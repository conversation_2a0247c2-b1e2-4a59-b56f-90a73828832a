<?php

namespace common\models;

use common\models\query\ChipEcuAdditionsQuery;
use Yii;

/**
 * This is the model class for table "chip_ecu_additions".
 *
 * @property int $id
 * @property int $brand_id
 * @property int $model_id
 * @property int $generation_id
 * @property int $engine_id
 * @property int $ecu_id
 * @property int $addition_id
 * @property int $price
 * @property string $created_at
 * @property string $comment
 * @property string $updated_at
 * @property string $deleted_at
 * @property ChipAddition[] $additions
 * @property int $isDeleted
 * @property int $deleted_by
 */
class ChipEcuAdditions extends BaseChipModel
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'chip_ecu_additions';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['brand_id', 'ecu_id'], 'required'],
            [['brand_id', 'deleted_by', 'isDeleted'], 'integer'],
            [['created_at', 'ecu_id', 'updated_at', 'deleted_at', 'model_id', 'generation_id', 'engine_id', 'addition_id', 'comment', 'price', 'deleted_by', 'isDeleted'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('backend', 'ID'),
            'brand_id' => Yii::t('backend', 'Brand ID'),
            'model_id' => Yii::t('backend', 'Model ID'),
            'generation_id' => Yii::t('backend', 'Generation ID'),
            'engine_id' => Yii::t('backend', 'Engine ID'),
            'ecu_id' => Yii::t('backend', 'Ecu ID'),
            'comment' => Yii::t('backend', 'Comment'),
            'price' => Yii::t('backend', 'Price'),
            'addition_id' => Yii::t('backend', 'Addition ID'),
            'created_at' => Yii::t('backend', 'Created At'),
            'updated_at' => Yii::t('backend', 'Updated At'),
            'deleted_at' => Yii::t('backend', 'Deleted At'),
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getAdditions()
    {
        if ($this->isNewRecord) {
            return ChipAddition::find()->all();
        }
//        $adds = ChipAddition::find()->where();
        return $this->hasMany(ChipAddition::className(), ['id' => 'addition_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getAddition()
    {
        return $this->hasOne(ChipAddition::className(), ['id' => 'addition_id']);
    }
    /**
     * @return \yii\db\ActiveQuery
     */
    public function getEcu()
    {
        return $this->hasOne(ChipEcuDict::className(), ['id' => 'ecu_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getBrand()
    {
        return $this->hasOne(ChipBrand::className(), ['id' => 'brand_id']);
    }

    /**
     * {@inheritdoc}
     * @return ChipEcuAdditionsQuery the active query used by this AR class.
     */
    public static function find()
    {
        return parent::find(new ChipEcuAdditionsQuery(get_called_class()));
    }

    public function afterSave($insert, $changedAttributes)
    {
//        if (!$this->needAfterSave) {
//            return parent::afterSave($insert, $changedAttributes); // TODO: Change the autogenerated stub
//        }
        return parent::afterSave($insert, $changedAttributes); // TODO: Change the autogenerated stub
    }

}
