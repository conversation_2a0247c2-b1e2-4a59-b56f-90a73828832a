<?php

namespace common\models\notification;

use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;
use yii\db\Expression;

/**
 * Модель для таблицы "notification_feature_flags"
 *
 * @property int $id
 * @property string $code Код флага
 * @property string $name Название флага
 * @property string|null $description Описание флага
 * @property bool $is_enabled Включен ли флаг
 * @property string $category Категория флага (feature, channel)
 * @property string $created_at Дата и время создания
 * @property string $updated_at Дата и время обновления
 */
class NotificationFeatureFlag extends ActiveRecord
{
    // Константы для категорий
    const CATEGORY_GENERAL = 'general';
    const CATEGORY_FEATURE = 'feature';
    const CATEGORY_CHANNEL = 'channel';
    
    // Константы для кодов флагов
    const FLAG_USE_NEW_SYSTEM = 'use_new_system';
    const FLAG_PROJECT_NOTIFICATIONS = 'project_notifications';
    const FLAG_FILE_NOTIFICATIONS = 'file_notifications';
    const FLAG_SYSTEM_NOTIFICATIONS = 'system_notifications';
    const FLAG_USER_NOTIFICATIONS = 'user_notifications';
    
    const FLAG_UI_CHANNEL = 'ui_channel';
    const FLAG_TELEGRAM_CHANNEL = 'telegram_channel';
    const FLAG_EMAIL_CHANNEL = 'email_channel';
    const FLAG_SMS_CHANNEL = 'sms_channel';
    const FLAG_PROJECT_NOTES_CHANNEL = 'project_notes_channel';
    
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%notification_feature_flags}}';
    }
    
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => 'updated_at',
                'value' => new Expression('NOW()'),
            ],
        ];
    }
    
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['code', 'name'], 'required'],
            [['description'], 'string'],
            [['is_enabled'], 'boolean'],
            [['created_at', 'updated_at'], 'safe'],
            [['code'], 'string', 'max' => 50],
            [['name'], 'string', 'max' => 100],
            [['category'], 'string', 'max' => 50],
            [['code'], 'unique'],
            [['category'], 'in', 'range' => [self::CATEGORY_GENERAL, self::CATEGORY_FEATURE, self::CATEGORY_CHANNEL]],
        ];
    }
    
    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'code' => 'Код',
            'name' => 'Название',
            'description' => 'Описание',
            'is_enabled' => 'Включен',
            'category' => 'Категория',
            'created_at' => 'Дата создания',
            'updated_at' => 'Дата обновления',
        ];
    }
    
    /**
     * Проверить, включен ли флаг
     *
     * @param string $code Код флага
     * @return bool
     */
    public static function isEnabled($code)
    {
        $flag = static::findOne(['code' => $code]);
        return $flag ? $flag->is_enabled : false;
    }
    
    /**
     * Включить флаг
     *
     * @param string $code Код флага
     * @return bool
     */
    public static function enable($code)
    {
        $flag = static::findOne(['code' => $code]);
        if (!$flag) {
            return false;
        }
        
        $flag->is_enabled = true;
        return $flag->save();
    }
    
    /**
     * Отключить флаг
     *
     * @param string $code Код флага
     * @return bool
     */
    public static function disable($code)
    {
        $flag = static::findOne(['code' => $code]);
        if (!$flag) {
            return false;
        }
        
        $flag->is_enabled = false;
        return $flag->save();
    }
    
    /**
     * Получить все флаги определенной категории
     *
     * @param string $category Категория
     * @return NotificationFeatureFlag[]
     */
    public static function findAllByCategory($category)
    {
        return static::findAll(['category' => $category]);
    }
    
    /**
     * Получить все флаги функций
     *
     * @return NotificationFeatureFlag[]
     */
    public static function findAllFeatures()
    {
        return static::findAllByCategory(self::CATEGORY_FEATURE);
    }
    
    /**
     * Получить все флаги каналов
     *
     * @return NotificationFeatureFlag[]
     */
    public static function findAllChannels()
    {
        return static::findAllByCategory(self::CATEGORY_CHANNEL);
    }
    
    /**
     * Получить все флаги в виде ассоциативного массива
     *
     * @return array
     */
    public static function getAll()
    {
        $flags = static::find()->all();
        $result = [];
        
        foreach ($flags as $flag) {
            $result[$flag->code] = $flag->is_enabled;
        }
        
        return $result;
    }
    
    /**
     * Получить все флаги функций в виде ассоциативного массива
     *
     * @return array
     */
    public static function getAllFeatures()
    {
        $flags = static::findAllFeatures();
        $result = [];
        
        foreach ($flags as $flag) {
            $result[$flag->code] = $flag->is_enabled;
        }
        
        return $result;
    }
    
    /**
     * Получить все флаги каналов в виде ассоциативного массива
     *
     * @return array
     */
    public static function getAllChannels()
    {
        $flags = static::findAllChannels();
        $result = [];
        
        foreach ($flags as $flag) {
            $result[$flag->code] = $flag->is_enabled;
        }
        
        return $result;
    }
}
