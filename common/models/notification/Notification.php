<?php

namespace common\models\notification;

use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;
use yii\db\Expression;

/**
 * Модель для таблицы "notifications"
 *
 * @property int $id
 * @property string $type Тип уведомления
 * @property string $subject Краткое описание уведомления
 * @property string|null $content Полное содержимое уведомления
 * @property array|null $context Дополнительные данные в формате JSON
 * @property string $source_type Тип источника (user, service, system, external)
 * @property string|null $source_id ID пользователя или код сервиса
 * @property array|null $source_context Дополнительный контекст источника
 * @property string $importance Важность уведомления (low, normal, high)
 * @property string $created_at Дата и время создания
 * @property string|null $expires_at Когда уведомление истекает
 *
 * @property NotificationDelivery[] $deliveries Доставки уведомления
 * @property NotificationLog[] $logs Логи уведомления
 */
class Notification extends ActiveRecord
{
    // Константы для типов источников
    const SOURCE_TYPE_USER = 'user';
    const SOURCE_TYPE_SERVICE = 'service';
    const SOURCE_TYPE_SYSTEM = 'system';
    const SOURCE_TYPE_EXTERNAL = 'external';

    // Константы для важности
    const IMPORTANCE_LOW = 'low';
    const IMPORTANCE_NORMAL = 'normal';
    const IMPORTANCE_HIGH = 'high';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%notifications}}';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => false,
                'value' => new Expression('NOW()'),
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['type', 'subject'], 'required'],
            [['content'], 'string'],
            [['context', 'source_context'], 'safe'],
            [['created_at', 'expires_at'], 'safe'],
            [['type'], 'string', 'max' => 50],
            [['subject'], 'string', 'max' => 255],
            [['source_id'], 'string', 'max' => 100],
            [['source_type'], 'in', 'range' => [self::SOURCE_TYPE_USER, self::SOURCE_TYPE_SERVICE, self::SOURCE_TYPE_SYSTEM, self::SOURCE_TYPE_EXTERNAL]],
            [['importance'], 'in', 'range' => [self::IMPORTANCE_LOW, self::IMPORTANCE_NORMAL, self::IMPORTANCE_HIGH]],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'type' => 'Тип уведомления',
            'subject' => 'Заголовок',
            'content' => 'Содержимое',
            'context' => 'Контекст',
            'source_type' => 'Тип источника',
            'source_id' => 'ID источника',
            'source_context' => 'Контекст источника',
            'importance' => 'Важность',
            'created_at' => 'Дата создания',
            'expires_at' => 'Дата истечения',
        ];
    }

    /**
     * Получить доставки уведомления
     *
     * @return \yii\db\ActiveQuery
     */
    public function getDeliveries()
    {
        return $this->hasMany(NotificationDelivery::class, ['notification_id' => 'id']);
    }

    /**
     * Получить логи уведомления
     *
     * @return \yii\db\ActiveQuery
     */
    public function getLogs()
    {
        return $this->hasMany(NotificationLog::class, ['notification_id' => 'id']);
    }

    /**
     * Получить значение из контекста
     *
     * @param string $key Ключ
     * @param mixed $default Значение по умолчанию
     * @return mixed
     */
    public function getContextValue($key, $default = null)
    {
        return $this->context[$key] ?? $default;
    }

    /**
     * Установить значение в контекст
     *
     * @param string $key Ключ
     * @param mixed $value Значение
     * @return $this
     */
    public function setContextValue($key, $value)
    {
        $context = $this->context ?? [];
        $context[$key] = $value;
        $this->context = $context;
        return $this;
    }

    /**
     * Получить значение из контекста источника
     *
     * @param string $key Ключ
     * @param mixed $default Значение по умолчанию
     * @return mixed
     */
    public function getSourceContextValue($key, $default = null)
    {
        return $this->source_context[$key] ?? $default;
    }

    /**
     * Установить значение в контекст источника
     *
     * @param string $key Ключ
     * @param mixed $value Значение
     * @return $this
     */
    public function setSourceContextValue($key, $value)
    {
        $context = $this->source_context ?? [];
        $context[$key] = $value;
        $this->source_context = $context;
        return $this;
    }

    /**
     * Проверить, истекло ли уведомление
     *
     * @return bool
     */
    public function isExpired()
    {
        if ($this->expires_at === null) {
            return false;
        }

        return strtotime($this->expires_at) < time();
    }

    /**
     * Получить тип события (для совместимости с DefaultRecipientResolver)
     *
     * @return string
     */
    public function getEventType(): string
    {
        return $this->type;
    }

    /**
     * Получить данные уведомления (для совместимости с DefaultRecipientResolver)
     * Возвращает объединение context и source_context
     *
     * @return array
     */
    public function getData(): array
    {
        $data = $this->source_context ?? [];

        // Добавляем основные поля для роутинга
        if (isset($this->context)) {
            $data = array_merge($data, $this->context);
        }

        return $data;
    }

    /**
     * Проверить наличие метаданных (для совместимости с DefaultRecipientResolver)
     *
     * @param string $key Ключ метаданных
     * @return bool
     */
    public function hasMetadata(string $key): bool
    {
        return isset($this->context[$key]);
    }

    /**
     * Получить метаданные (для совместимости с DefaultRecipientResolver)
     *
     * @param string $key Ключ метаданных
     * @param mixed $default Значение по умолчанию
     * @return mixed
     */
    public function getMetadata(string $key, $default = null)
    {
        return $this->context[$key] ?? $default;
    }

    /**
     * Установить метаданные
     *
     * @param string $key Ключ
     * @param mixed $value Значение
     * @return $this
     */
    public function setMetadata(string $key, $value): self
    {
        $context = $this->context ?? [];
        $context[$key] = $value;
        $this->context = $context;
        return $this;
    }

    /**
     * Добавить доставку уведомления
     *
     * @param int $channelId ID канала
     * @param string $recipientType Тип получателя
     * @param string|int $recipientId ID получателя
     * @param array $options Дополнительные опции
     * @return NotificationDelivery
     */
    public function addDelivery($channelId, $recipientType, $recipientId, $options = [])
    {
        $delivery = new NotificationDelivery();
        $delivery->notification_id = $this->id;
        $delivery->channel_id = $channelId;
        $delivery->recipient_type = $recipientType;
        $delivery->recipient_id = $recipientId;

        if (isset($options['display_location'])) {
            $delivery->display_location = $options['display_location'];
        }

        if (isset($options['max_displays'])) {
            $delivery->max_displays = $options['max_displays'];
        }

        if (isset($options['show_until_clicked'])) {
            $delivery->show_until_clicked = $options['show_until_clicked'];
        }

        $delivery->save();

        return $delivery;
    }

    /**
     * Добавить запись в лог
     *
     * @param string $eventType Тип события
     * @param array $details Детали события
     * @param int|null $deliveryId ID доставки
     * @return NotificationLog
     */
    public function addLog($eventType, $details = [], $deliveryId = null)
    {
        $log = new NotificationLog();
        $log->notification_id = $this->id;
        $log->event_type = $eventType;
        $log->details = $details;

        if ($deliveryId !== null) {
            $log->delivery_id = $deliveryId;

            $delivery = NotificationDelivery::findOne($deliveryId);
            if ($delivery) {
                $log->channel_id = $delivery->channel_id;
                $log->recipient_type = $delivery->recipient_type;
                $log->recipient_id = $delivery->recipient_id;
            }
        }

        $log->save();

        return $log;
    }

    public function getTitle(): string
    {
        return $this->subject;
    }

    public function getBody(): string
    {
        return $this->content;
    }

}
