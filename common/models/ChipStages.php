<?php

namespace common\models;

use common\models\query\ChipStagesQuery;
use Yii;

/**
 * This is the model class for table "chip_stages".
 *
 * @property int $id
 * @property string $title
 * @property string $comment
 * @property string $price
 * @property string $price_track
 * @property string $price_moto
 * @property string $price_boat
 * @property string $isDefault
 * @property string $created_at
 * @property string $deleted_at
 *
 * @property ChipStagesEcu[] $chipStagesEcus
 */
class ChipStages extends BaseChipModel
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'chip_stages';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['title'], 'required'],
            [['created_at', 'deleted_at', 'comment', 'price', 'price_track', 'price_moto', 'price_boat', 'price_agrarian', 'isDefault'], 'safe'],
            [['title'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('backend', 'ID'),
            'title' => Yii::t('backend', 'Title'),
            'comment' => Yii::t('backend', 'Comment'),
            'price_auto' => Yii::t('backend', 'Price Car'),
            'price_track' => Yii::t('backend', 'Price Track'),
            'price_moto' => Yii::t('backend', 'Price Moto'),
            'price_boat' => Yii::t('backend', 'Price Boat'),
            'price_agrarian' => Yii::t('backend', 'Price Agro'),
            'isDefault' => Yii::t('backend', 'isDefault'),
            'created_at' => Yii::t('backend', 'Created At'),
            'deleted_at' => Yii::t('backend', 'Deleted At'),
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getChipStagesEcus()
    {
        return $this->hasMany(ChipStagesEcu::className(), ['stage_id' => 'id']);
    }

    /**
     * {@inheritdoc}
     * @return ChipStagesQuery the active query used by this AR class.
     */
    public static function find()
    {
        return parent::find(new ChipStagesQuery(get_called_class()));
    }
}
