<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "api1C_log".
 *
 * @property int $id
 * @property int $project_file_id
 * @property int $project_id
 * @property int $type
 * @property int $step
 * @property int $errors
 * @property string $guid
 * @property string $operation_data
 * @property string $result_data
 * @property string $mod_guid
 * @property string $created_at
 * @property string $deleted_at
 * @property int $completed
 * @property int $processed
 * @property int $deleted_by
 * @property int $isDeleted
 * @property int $updated_by
 * ALTER TABLE `alientech_operations` ADD `errors` INT(11) NULL AFTER `step`;
 * ALTER TABLE `alientech_operations` ADD `mod_guid` VARCHAR(255) NULL AFTER `errors`;
 * ALTER TABLE `alientech_operations` ADD `updated_by` INT(11) NULL AFTER `deleted_by`;
 */
class AlientechOperation extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'alientech_operations';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['operation_data', 'result_data', 'guid', 'mod_guid'], 'string'],
            [['created_at', 'operation_data', 'result_data','deleted_at','completed','processed','step','type','errors','mod_guid'], 'safe'],
            [['project_file_id', 'project_id', 'deleted_by', 'isDeleted', 'completed', 'processed', 'step', 'type', 'errors'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'project_file_id' => Yii::t('app', 'project_file_id'),
            'project_id' => Yii::t('app', 'project_id'),
            'operation_data' => Yii::t('app', 'operation_data'),
            'created_at' => Yii::t('app', 'Created At'),
            'deleted_at' => Yii::t('app', 'Deleted At'),
            'deleted_by' => Yii::t('app', 'Deleted By'),
            'updated_by' => Yii::t('app', 'updated_by'),
            'isDeleted' => Yii::t('app', 'Is Deleted'),
        ];
    }
    /**
     * @return \yii\db\ActiveQuery
     */
    public function getProjectFile()
    {
        return $this->hasOne(ProjectFiles::className(), ['id' => 'project_file_id']);
    }

    public function getProject()
    {
        return $this->hasOne(Projects::className(), ['id' => 'project_id']);
    }

    /**
     * {@inheritdoc}
     * @return Api1CLogQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new Api1CLogQuery(get_called_class());
    }

    public function process()
    {
        $this->processed = 1;
        return $this->save(false);
    }

    public function deprocess()
    {
        $this->processed = 0;
        return $this->save(false);
    }

    public function helper()
    {
        $this->processed = 0;
        return $this->save(false);
    }
}
