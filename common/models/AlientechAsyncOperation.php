<?php

namespace common\models;

use common\helpers\slave\operation\Operation;
use Yii;

/**
 * This is the model class for table "slave_operations".
 *
 * @property int $id
 * @property int $file_id
 * @property int $project_id
 * @property string|null $guid
 * @property string|null $slotGUID
 * @property int $asyncOperationType
 * @property int $status
 * @property int|null $isCompleted
 * @property int|null $recommendedPollingInterval
 * @property string|null $startedOn
 * @property string|null $completedOn
 * @property int $isSuccessful
 * @property int $hasFailed
 * @property string|null $error
 * @property string|null $result
 * @property string|null $additionalInfo
 * @property string|null $userInfo
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property string|null $files
 * @property string|null $client_application_guid
 * @property string|null $duration
 * @property string|null $job_request_guid
 * @property int|null $api_status
 * @property string|null $external_guid
 * @property string|null $file_path
 * @property string|null $callback_url
 */
class AlientechAsyncOperation extends \yii\db\ActiveRecord
{

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'alientech_async_operation';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['file_id', 'project_id', 'asyncOperationType', 'status', 'isSuccessful', 'hasFailed'], 'required'],
            [['file_id', 'project_id', 'asyncOperationType', 'status', 'isCompleted', 'recommendedPollingInterval', 'isSuccessful', 'hasFailed'], 'integer'],
            [['error', 'result', 'slotGUID', 'additionalInfo', 'userInfo', 'files', 'external_guid', 'file_path', 'callback_url'], 'string'],
            [['created_at', 'updated_at', 'slotGUID', 'additionalInfo', 'userInfo'], 'safe'],
            [['guid', 'startedOn', 'completedOn'], 'string', 'max' => 255],
        ];
    }

    public function afterFind()
    {
        parent::afterFind(); // TODO: Change the autogenerated stub
        $this->result = $this->result ? json_decode($this->result, true) : [];
        $this->error = $this->error ? json_decode($this->error, true) : [];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'file_id' => Yii::t('app', 'File ID'),
            'guid' => Yii::t('app', 'Guid'),
            'asyncOperationType' => Yii::t('app', 'Async Operation Type'),
            'status' => Yii::t('app', 'Status'),
            'isCompleted' => Yii::t('app', 'Is Completed'),
            'recommendedPollingInterval' => Yii::t('app', 'Recommended Polling Interval'),
            'startedOn' => Yii::t('app', 'Started On'),
            'completedOn' => Yii::t('app', 'Completed On'),
            'isSuccessful' => Yii::t('app', 'Is Successful'),
            'hasFailed' => Yii::t('app', 'Has Failed'),
            'error' => Yii::t('app', 'Error'),
            'result' => Yii::t('app', 'Result'),
            'created_at' => Yii::t('app', 'Created At'),
            'updated_at' => Yii::t('app', 'Updated At'),
        ];
    }
}