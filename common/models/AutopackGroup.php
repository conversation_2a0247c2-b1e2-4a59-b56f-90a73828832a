<?php

namespace common\models;

use common\models\query\AutopackGroupsQuery;
use Yii;

/**
 * This is the model class for table "autopack_groups".
 *
 * @property int $id
 * @property string $title
 * @property int $parent_id
 * @property string $chip_brands
 * @property string $description
 * @property string $created_at
 * @property string $updated_at
 * @property int $created_by
 * @property int $updated_by
 * @property int $isDeleted
 * @property int $deleted_by
 */
class AutopackGroup extends BaseChipModel
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'autopack_groups';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id', 'parent_id', 'created_by', 'updated_by', 'isDeleted', 'deleted_by'], 'integer'],
            [['chip_brands', 'description'], 'string'],
            [['created_at', 'updated_at'], 'safe'],
            [['title'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'title' => Yii::t('app', 'Title'),
            'parent_id' => Yii::t('app', 'Parent ID'),
            'chip_brands' => Yii::t('app', 'Chip Brands'),
            'description' => Yii::t('app', 'Description'),
            'created_at' => Yii::t('app', 'Created At'),
            'updated_at' => Yii::t('app', 'Updated At'),
            'created_by' => Yii::t('app', 'Created By'),
            'updated_by' => Yii::t('app', 'Updated By'),
            'isDeleted' => Yii::t('app', 'Is Deleted'),
            'deleted_by' => Yii::t('app', 'Deleted By'),
        ];
    }

    /**
     * {@inheritdoc}
     * @return AutopackGroupsQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new AutopackGroupsQuery(get_called_class());
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getChildren()
    {
        $query = $this->hasMany(self::className(), ['parent_id' => 'id'])
            ->select(['autopack_groups.id', 'autopack_groups.title as text', 'autopack_groups.parent_id', 'autopack_groups.title', 'GROUP_CONCAT(b.title ORDER BY b.id) brands'])
//            ->where('autopack_groups.parent_id = 0')
            ->leftJoin('chip_brand b', 'FIND_IN_SET(b.id, autopack_groups.chip_brands) > 0');
        $query->with('settings');


           return $query->groupBy('autopack_groups.id')
            ->orderBy(['autopack_groups.title' => SORT_ASC])
            ->asArray();
//            ->all();





//        ->select('id, title as  text, description, parent_id')
//            ->orderBy(['title' => SORT_ASC])->asArray();
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getSettings()
    {
        $ecu_id = Yii::$app->request->get('ecu_id', 0);
        $query = $this->hasMany(AutopackSetting::class, ['group_id' => 'id'])->with('files', 'ecu');
        if (!empty($ecu_id)){
            $query->where(['ecu_id' => $ecu_id]);
        }
        return $query;

    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getSettRels()
    {
        return $this->hasMany(AutopackSettRel::class, ['id' => 'settings_id'])
            ->via('settings');
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFiles()
    {
        return $this->hasMany(AutopackSettFile::class, ['id' => 'sett_file_id'])
            ->via('settRels');
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getParentGroup()
    {
        return $this->hasOne(self::class, ['id' => 'parent_id']);
    }

}
