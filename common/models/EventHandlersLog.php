<?php

namespace common\models;

use Yii;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;

/**
 * Модель для таблицы "event_handlers_log".
 *
 * @property int $id
 * @property int $event_log_id ID записи события из таблицы event_logs
 * @property string $handler_class Класс обработчика события
 * @property string $status Статус обработки (success, failed)
 * @property string|null $error_message Сообщение об ошибке (если есть)
 * @property float|null $execution_time Время выполнения в секундах
 * @property string $executed_at Время выполнения обработчика
 *
 * @property EventLog $eventLog
 */
class EventHandlersLog extends ActiveRecord
{
    /**
     * Константы для статусов
     */
    const STATUS_SUCCESS = 'success';
    const STATUS_FAILED = 'failed';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'event_handlers_log';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['event_log_id', 'handler_class'], 'required'],
            [['event_log_id'], 'integer'],
            [['status'], 'string'],
            [['error_message'], 'string'],
            [['execution_time'], 'number'],
            [['executed_at'], 'safe'],
            [['handler_class'], 'string', 'max' => 255],
            [['status'], 'in', 'range' => [self::STATUS_SUCCESS, self::STATUS_FAILED]],
            [['event_log_id'], 'exist', 'skipOnError' => true, 'targetClass' => EventLog::class, 'targetAttribute' => ['event_log_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'event_log_id' => 'ID события',
            'handler_class' => 'Класс обработчика',
            'status' => 'Статус',
            'error_message' => 'Сообщение об ошибке',
            'execution_time' => 'Время выполнения',
            'executed_at' => 'Дата выполнения',
        ];
    }

    /**
     * Получить связанное событие.
     *
     * @return ActiveQuery
     */
    public function getEventLog()
    {
        return $this->hasOne(EventLog::class, ['id' => 'event_log_id']);
    }

    /**
     * Создать запись об успешной обработке события.
     *
     * @param EventLog $eventLog Событие
     * @param string $handlerClass Класс обработчика
     * @param float $executionTime Время выполнения
     * @return EventHandlersLog|null
     */
    public static function logSuccess(EventLog $eventLog, $handlerClass, $executionTime = null)
    {
        return self::createLog($eventLog, $handlerClass, self::STATUS_SUCCESS, null, $executionTime);
    }

    /**
     * Создать запись о неудачной обработке события.
     *
     * @param EventLog $eventLog Событие
     * @param string $handlerClass Класс обработчика
     * @param string $errorMessage Сообщение об ошибке
     * @param float $executionTime Время выполнения
     * @return EventHandlersLog|null
     */
    public static function logFailure(EventLog $eventLog, $handlerClass, $errorMessage, $executionTime = null)
    {
        return self::createLog($eventLog, $handlerClass, self::STATUS_FAILED, $errorMessage, $executionTime);
    }

    /**
     * Создать запись о обработке события.
     *
     * @param EventLog $eventLog Событие
     * @param string $handlerClass Класс обработчика
     * @param string $status Статус
     * @param string|null $errorMessage Сообщение об ошибке
     * @param float|null $executionTime Время выполнения
     * @return EventHandlersLog|null
     */
    private static function createLog(EventLog $eventLog, $handlerClass, $status, $errorMessage = null, $executionTime = null)
    {
        $log = new self();
        $log->event_log_id = $eventLog->id;
        $log->handler_class = $handlerClass;
        $log->status = $status;
        $log->error_message = $errorMessage;
        $log->execution_time = $executionTime;
        
        return $log->save() ? $log : null;
    }
}
