<?php

namespace common\models\autopack;

use common\helpers\AutopackHelper;
use common\models\AutopackGroup;
use common\models\AutopackSettFile;
use common\models\BaseChipModel;
use common\models\ChipEcuDict;
use common\models\query\AutopackSettingsQuery;
use Yii;

/**
 * This is the model class for table "autopack_config".
 *
 * @property int $id
 * @property int $brand_id
 * @property int $ecu_id
 * @property int $orig_file_id
 * @property int $mod_file_id
 * @property string $software_num
 * @property string $additions
 * @property string $script_path
 * @property string $created_at
 * @property string $updated_at
 * @property string $deleted_at
 * @property int $created_by
 * @property int $updated_by
 * @property int $isDeleted
 * @property int $deleted_by
 */
class AutopackConfig extends BaseChipModel
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'autopack_config';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['brand_id', 'ecu_id', 'orig_file_id', 'mod_file_id', 'created_by', 'updated_by', 'isDeleted', 'deleted_by'], 'integer'],
            [['software_num', 'additions', 'script_path',], 'string'],
            [['created_at', 'updated_at', 'deleted_at'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'file_type' => Yii::t('app', 'file_type'),
            'name' => Yii::t('app', 'name'),
            'path' => Yii::t('app', 'path'),
            'created_at' => Yii::t('app', 'Created At'),
            'updated_at' => Yii::t('app', 'Updated At'),
            'created_by' => Yii::t('app', 'Created By'),
            'updated_by' => Yii::t('app', 'Updated By'),
            'isDeleted' => Yii::t('app', 'Is Deleted'),
            'deleted_by' => Yii::t('app', 'Deleted By'),
        ];
    }

    public function beforeSave($insert)
    {
        if ($insert) {
            $this->created_at = time();
        }
        $this->updated_at = time();
        return parent::beforeSave($insert); // TODO: Change the autogenerated stub
    }

    public function getOriginalFile(): \yii\db\ActiveQuery
    {
        return $this->hasOne(AutopackConfigFile::class, ['id' => 'orig_file_id']);
    }

    public function getModifiedFile(): \yii\db\ActiveQuery
    {
        return $this->hasOne(AutopackConfigFile::class, ['id' => 'mod_file_id']);
    }
}