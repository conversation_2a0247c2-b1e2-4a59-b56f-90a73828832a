<?php

namespace common\models\autopack;

use common\models\BaseChipModel;
use Yii;

/**
 * This is the model class for table "autopack".
 *
 * @property int $id
 * @property int|null $project_id
 * @property int|null $project_file_id
 * @property int|null $config_id
 * @property int|null $check_processed
 * @property int|null $check_processed_status
 * @property int|null $check_processed_result
 * @property int|null $mod_processed
 * @property int|null $mod_processed_status
 * @property int|null $mod_processed_result
 *
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $isDeleted
 * @property int|null $deleted_by
 */
class AutopackProc extends BaseChipModel
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'autopack_proc';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['project_id', 'project_file_id', 'config_id', 'check_processed', 'check_processed_status', 'check_processed_result', 'mod_processed', 'mod_processed_status', 'mod_processed_result', 'created_by', 'updated_by', 'isDeleted', 'deleted_by'], 'integer'],
            [['created_at', 'updated_at'], 'safe'],
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getConfig()
    {
        return $this->hasOne(AutopackConfig::className(), ['id' => 'config_id']);
    }

}