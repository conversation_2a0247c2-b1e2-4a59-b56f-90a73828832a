<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "user_messages_users".
 *
 * @property int $id
 * @property int $message_id
 * @property int $user_id
 * @property int $readed
 * @property int $file_downloaded
 * @property string $downloaded_at
 * @property string $created_at
 * @property string $updated_at
 */
class UserMessagesUsers extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'user_messages_users';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['message_id', 'user_id', 'readed', 'file_downloaded'], 'required'],
            [['message_id', 'user_id', 'readed', 'file_downloaded'], 'integer'],
            [['downloaded_at', 'created_at', 'updated_at'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'message_id' => Yii::t('app', 'Message ID'),
            'user_id' => Yii::t('app', 'User ID'),
            'readed' => Yii::t('app', 'Readed'),
            'file_downloaded' => Yii::t('app', 'File Downloaded'),
            'downloaded_at' => Yii::t('app', 'Downloaded At'),
            'created_at' => Yii::t('app', 'Created At'),
            'updated_at' => Yii::t('app', 'Updated At'),
        ];
    }

    /**
     * {@inheritdoc}
     * @return UserMessagesUsersQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new UserMessagesUsersQuery(get_called_class());
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getMessage()
    {
        return $this->hasOne(UserMessages::class, ['id' => 'message_id']);
    }

}
