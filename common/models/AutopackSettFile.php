<?php

namespace common\models;

use common\models\query\AutopackSettFilesQuery;
use Yii;

/**
 * This is the model class for table "autopack_sett_files".
 *
 * @property int $id
 * @property string $soft_num
 * @property string $hard_num
 * @property string $check_sum
 * @property string $file_type
 * @property int $file_orig
 * @property string $file_name
 * @property string $file_path
 * @property string $created_at
 * @property string $updated_at
 * @property int $created_by
 * @property int $updated_by
 * @property int $isDeleted
 * @property int $deleted_by
 * @property int $stage_id
 * @property string $additions
 */
class AutopackSettFile extends BaseChipModel
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'autopack_sett_files';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id', 'file_orig', 'stage_id', 'created_by', 'updated_by', 'isDeleted', 'deleted_by'], 'integer'],
            [['created_at', 'updated_at', 'stage_id', 'soft_num', 'hard_num', 'check_sum', 'file_type','additions', 'file_name', 'file_path'], 'safe'],
            [['soft_num', 'hard_num', 'check_sum', 'file_type', 'additions', 'file_name', 'file_path'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'soft_num' => Yii::t('app', 'Soft Num'),
            'hard_num' => Yii::t('app', 'Hard Num'),
            'check_sum' => Yii::t('app', 'Check Sum'),
            'file_type' => Yii::t('app', 'File Type'),
            'file_orig' => Yii::t('app', 'File Orig'),
            'file_name' => Yii::t('app', 'File Name'),
            'file_path' => Yii::t('app', 'File Path'),
            'created_at' => Yii::t('app', 'Created At'),
            'updated_at' => Yii::t('app', 'Updated At'),
            'created_by' => Yii::t('app', 'Created By'),
            'updated_by' => Yii::t('app', 'Updated By'),
            'isDeleted' => Yii::t('app', 'Is Deleted'),
            'deleted_by' => Yii::t('app', 'Deleted By'),
        ];
    }

    /**
     * {@inheritdoc}
     * @return AutopackSettFilesQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new AutopackSettFilesQuery(get_called_class());
    }

    public function getSetting()
    {
        return $this->hasOne(AutopackSetting::class, ['id' => 'settings_id'])
            ->viaTable('autopack_sett_rel', ['sett_file_id' => 'id']);
    }

    public function getOriginalFile()
    {
        return $this->hasOne(self::class, ['id' => 'file_orig']);
    }


    public function getEcuId()
    {
        return $this->hasOne(AutopackSetting::class, ['id' => 'settings_id'])
            ->viaTable('autopack_sett_rel', ['sett_file_id' => 'id'])
            ->select('ecu_id')->column();
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getChildrenFiles()
    {
        return $this->hasMany(self::className(), ['file_orig' => 'id'])->where('isDeleted is NULL')->asArray();
    }

    public function beforeDelete()
    {
        try{
            unlink($this->file_path);
        } catch(\Exception $e) {

        }

        return parent::beforeDelete(); // TODO: Change the autogenerated stub
    }

}
