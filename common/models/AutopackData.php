<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "autopack_data".
 *
 * @property int $id
 * @property int|null $project_id
 * @property int|null $file_orig
 * @property int|null $file_mod
 * @property string|null $additions
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $isDeleted
 * @property int|null $deleted_by
 */
class AutopackData extends BaseChipModel
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'autopack_data';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['project_id', 'file_orig', 'file_mod', 'created_by', 'updated_by', 'isDeleted', 'deleted_by'], 'integer'],
            [['additions'], 'string'],
            [['created_at', 'updated_at'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'project_id' => Yii::t('app', 'Project ID'),
            'file_orig' => Yii::t('app', 'File Orig'),
            'file_mod' => Yii::t('app', 'File Mod'),
            'additions' => Yii::t('app', 'Additions'),
            'created_at' => Yii::t('app', 'Created At'),
            'updated_at' => Yii::t('app', 'Updated At'),
            'created_by' => Yii::t('app', 'Created By'),
            'updated_by' => Yii::t('app', 'Updated By'),
            'isDeleted' => Yii::t('app', 'Is Deleted'),
            'deleted_by' => Yii::t('app', 'Deleted By'),
        ];
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\AutopackDataQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\AutopackDataQuery(get_called_class());
    }
}
