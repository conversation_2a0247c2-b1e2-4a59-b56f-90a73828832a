<?php

namespace common\models;

use common\models\query\ProjectOptionsQuery;
use Yii;

/**
 * This is the model class for table "project_options".
 *
 * @property int $id
 * @property string $title
 * @property string $created_at Когда создан
 * @property string $updated_at Когда обновлен
 * @property string $deleted_at Когда удален
 * @property int $created_by Кем создан
 * @property int $updated_by Кем обновлен
 * @property int $deleted_by Кем удален
 * @property int $project_id Проект
 * @property int $addition_id Доп параметр
 * @property int $ecu_addition_id Доп параметр
 * @property int $enabled Включен
 * @property int $isDeleted
 */
class ProjectOptions extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'project_options';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['title', 'project_id'], 'required'],
            [['created_at', 'updated_at', 'deleted_at', 'downloaded_at', 'ecu_addition_id'], 'safe'],
            [['created_by', 'updated_by', 'deleted_by', 'project_id', 'addition_id', 'comment', 'isDeleted', 'ecu_addition_id'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'title' => Yii::t('app', 'Title'),
            'created_at' => Yii::t('app', 'Когда создан'),
            'updated_at' => Yii::t('app', 'Когда обновлен'),
            'deleted_at' => Yii::t('app', 'Когда удален'),
            'created_by' => Yii::t('app', 'Кем создан'),
            'updated_by' => Yii::t('app', 'Кем обновлен'),
            'deleted_by' => Yii::t('app', 'Кем удален'),
            'project_id' => Yii::t('app', 'Проект'),
            'path' => Yii::t('app', 'Путь'),
            'filename' => Yii::t('app', 'Имя файла'),
            'can_download' => Yii::t('app', 'Разрешено скачивать'),
            'downloaded' => Yii::t('app', 'Скачан'),
            'params' => Yii::t('app', 'Данные о файле'),
            'downloaded_by' => Yii::t('app', 'Кем скачан'),
            'downloaded_at' => Yii::t('app', 'Когда скачан'),
            'download_count' => Yii::t('app', 'Сколько раз скачан'),
            'file_history' => Yii::t('app', 'История файла'),
            'isDeleted' => Yii::t('app', 'Is Deleted'),
            'ecu_addition_id' => Yii::t('app', 'Ecu Addition'),
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getAddition()
    {
        return $this->hasOne(ChipAddition::className(), ['id' => 'addition_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getEcuAddition()
    {
        return $this->hasOne(ChipEcuAdditions::className(), ['id' => 'ecu_addition_id']);
//        return $q->createCommand()->getRawSql();
//;
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getCreator()
    {
        return $this->hasOne(User::className(), ['id' => 'created_by']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getProject()
    {
        return $this->hasOne(Projects::className(), ['id' => 'project_id']);
    }

    /**
     * {@inheritdoc}
     * @return ProjectOptionsQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new ProjectOptionsQuery(get_called_class());
    }
}
