<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "chip_price".
 *
 * @property int $id
 * @property int $vehicle_id
 * @property int $brand_id
 * @property int $model_id
 * @property int $generation_id
 * @property int $engine_id
 * @property int $user_id
 * @property int $tarif_id
 * @property int $year_from
 * @property int $year_to
 * @property string $additions
 * @property string $comment
 * @property int $price
 * @property string $created_at
 * @property string $updated_at
 * @property string $deleted_at
 * @property int $isDeleted
 * @property int $deleted_by
 */
class ChipPrice extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'chip_price';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['price'], 'required'],
            [['vehicle_id', 'brand_id', 'model_id', 'generation_id', 'engine_id', 'user_id', 'tarif_id', 'year_from', 'year_to', 'price', 'isDeleted', 'deleted_by'], 'integer'],
            [['additions', 'comment'], 'string'],
            [['created_at', 'updated_at', 'deleted_at'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'vehicle_id' => Yii::t('app', 'Vehicle ID'),
            'brand_id' => Yii::t('app', 'Brand ID'),
            'model_id' => Yii::t('app', 'Model ID'),
            'generation_id' => Yii::t('app', 'Generation ID'),
            'engine_id' => Yii::t('app', 'Engine ID'),
            'user_id' => Yii::t('app', 'User ID'),
            'tarif_id' => Yii::t('app', 'Tarif ID'),
            'year_from' => Yii::t('app', 'Year From'),
            'year_to' => Yii::t('app', 'Year To'),
            'additions' => Yii::t('app', 'Additions'),
            'comment' => Yii::t('app', 'Comment'),
            'price' => Yii::t('app', 'Price'),
            'created_at' => Yii::t('app', 'Created At'),
            'updated_at' => Yii::t('app', 'Updated At'),
            'deleted_at' => Yii::t('app', 'Deleted At'),
            'isDeleted' => Yii::t('app', 'Is Deleted'),
            'deleted_by' => Yii::t('app', 'Deleted By'),
        ];
    }

    /**
     * @return mixed
     */
    public function getAdditionsList() {
        return json_decode($this->additions);
    }

    /**
     * {@inheritdoc}
     * @return ChipPriceQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new ChipPriceQuery(get_called_class());
    }
}
