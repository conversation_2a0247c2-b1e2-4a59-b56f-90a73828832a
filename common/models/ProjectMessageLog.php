<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "project_message_log".
 * @property int $id
 * @property int $message_id Сообщение
 * @property int $message_send_id Сообщение
 * @property int $send_from отправитель
 * @property int $send_to получатель
 * @property string $send_method метод
 * @property int $is_successful отправлен
 * @property int $is_completed завершен
 */
class ProjectMessageLog extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'project_message_log';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['message_send_id', 'message_id', 'send_from', 'send_to', 'send_method'], 'required'],
            [['message_send_id', 'message_id', 'send_from', 'send_to', 'is_successful', 'is_completed'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('backend', 'ID'),
            'message_send_id' => Yii::t('backend', 'Message'),
            'message_id' => Yii::t('backend', 'Message'),
            'send_from' => Yii::t('backend', 'Sender'),
            'send_to' => Yii::t('backend', 'Recipient'),
            'send_method' => Yii::t('backend', 'Method'),
            'is_successful' => Yii::t('backend', 'Successful'),
            'is_completed' => Yii::t('backend', 'Completed'),
        ];
    }

}
