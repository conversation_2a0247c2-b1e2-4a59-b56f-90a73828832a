<?php

namespace common\models\search;

use backend\widgets\GridView;
use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\ChipModel;

/**
 * ChipModelSearch represents the model behind the search form about `common\models\ChipModel`.
 */
class ChipModelSearch extends ChipModel
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'brand_id'], 'integer'],
            [['title', 'slug', 'created_at', 'updated_at', 'deleted_at'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = ChipModel::find();

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'pagination' => [
                'params' => GridView::getMergedFilterStateParams(),
            ],
            'sort' => [
                'params' => GridView::getMergedFilterStateParams(),
            ],
        ]);
// Filter model
        $this->load(GridView::getMergedFilterStateParams());
        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        $query->andFilterWhere([
            'id' => $this->id,
            'brand_id' => $this->brand_id,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'deleted_at' => $this->deleted_at,
        ]);

        $query->andFilterWhere(['like', 'title', $this->title])
            ->andFilterWhere(['like', 'slug', $this->slug]);

        return $dataProvider;
    }
}
