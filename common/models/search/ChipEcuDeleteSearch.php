<?php

namespace common\models\search;

use backend\widgets\GridView;
use common\helpers\StageHelper;
use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use common\models\ChipEcu;
use yii\helpers\ArrayHelper;

/**
 * ChipEcuSearch represents the model behind the search form about `common\models\ChipEcu`.
 */
class ChipEcuDeleteSearch extends ChipEcu
{

    public $standard;
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id'], 'integer'],
            [['title', 'slug', 'created_at', 'updated_at', 'deleted_at', 'brand_id', 'model_id', 'generation_id', 'engine_id', 'ecu_id', 'standard', 'dinostend_file'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = ChipEcu::find()->with('brand', 'model', 'generation', 'engine', 'additions', 'ecu', 'stages');

        $query->filterDeleted(Yii::$app->request->getBodyParam('deleted'));

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'pagination' => [
                'params' => GridView::getMergedFilterStateParams(),
            ],
            'sort' => [
                'params' => GridView::getMergedFilterStateParams(),
            ],
        ]);

// Filter model
        $this->load(GridView::getMergedFilterStateParams());
        $params = ArrayHelper::merge(Yii::$app->request->get(), Yii::$app->request->post());
        $this->load(ArrayHelper::merge(GridView::getFilterStateParams(), $params));
        if (empty($this->model_id)) {
            $query->where('0=1');
            return $dataProvider;
        }
        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }
        if(!empty($this->brand_id)) {
            $dataProvider->pagination->pageSize = 10000;
            $query->andFilterWhere(['chip_ecu.brand_id' => $this->brand_id,]);
        }
        $query->andFilterWhere([
            'id' => $this->id,
            'chip_ecu.updated_at' => $this->updated_at,
        ]);
        if (!empty($this->model_id)) {
            $query->joinWith(['model' => function ($q) {
                $q->where(['chip_model.id' => $this->model_id]);
            }]);
        }
        if (!empty($this->generation_id)) {
            $query->joinWith(['generation' => function ($q) {
                $q->where(['like', 'chip_generation.title', $this->generation_id]);
            }]);
        }
        if (!empty($this->ecu_id)) {
            $query->joinWith(['ecu' => function ($q) {
                $q->where(['like', 'chip_ecu_dict.title', $this->ecu_id]);
            }]);
        }
        if (!empty($this->standard)) {
           $query->leftJoin('chip_stages_ecu','chip_stages_ecu.chip_ecu_id = chip_ecu.id');
            $query->andFilterWhere([
                'AND',[
                        'chip_stages_ecu.stage_id' => StageHelper::getDefaultStageId()
                    ], [
                    'OR',
                    ['like', 'chip_stages_ecu.inc_hp', $this->standard],
                    ['like', 'chip_stages_ecu.inc_tork', $this->standard],
                        ]
                ]
            );
        }
        if (!empty($this->engine_id)) {
            $query->joinWith(['engine' => function ($q) {
                $q->where(['like', 'chip_engine.title', $this->engine_id]);
            }]);
        }
        $query->andFilterWhere(['like', 'slug', $this->slug]);

        return $dataProvider;
    }

    public function getBrandFilterData()
    {
        $query = \common\models\ChipBrand::find()->orderBy('title');
        $result = $query->all();
        return ArrayHelper::map($result, 'id', 'title');
    }

    public function getModelFilterData(): array
    {
        $query = \common\models\ChipModel::find()->orderBy('title');
        if (!empty($this->brand_id)) {
            $query->where(['chip_model.brand_id' => $this->brand_id]);
        }
        $result = $query->all();
        return ArrayHelper::map($result, 'id', 'title');
    }

}