<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "chip_obd_errors".
 *
 * @property int $id
 * @property int $title
 * @property int $description
 * @property int $letter
 * @property string $created_at
 * @property string $deleted_at
 * @property int $deleted_by
 * @property int $isDeleted
 */
class ChipObdError extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'chip_obd_errors';
    }

    public static function findLetters()
    {
//        return self::find()->groupBy('letter')->select('letter')->column();
        return ['P', 'U', 'DF', 'B', 'C'];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['title', 'description', 'letter'], 'string'],
            [['created_at', 'deleted_at', 'description', 'letter'], 'safe'],
            [['deleted_by', 'isDeleted', ], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'title' => Yii::t('app', 'Title'),
            'description' => Yii::t('app', 'Description'),
            'letter' => Yii::t('app', 'Letter'),
            'created_at' => Yii::t('app', 'Created At'),
            'deleted_at' => Yii::t('app', 'Deleted At'),
            'deleted_by' => Yii::t('app', 'Deleted By'),
            'isDeleted' => Yii::t('app', 'Is Deleted'),
        ];
    }
}
