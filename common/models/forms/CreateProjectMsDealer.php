<?php

namespace common\models\forms;

class CreateProjectMsDealer extends CreateProject
{

    public function __construct()
    {
        $this->readonlyOptions = [
            'readonly' => 'readonly',
        ];
    }

    public function rules(): array {
        $rules = parent::rules();

        $rules = array_merge($rules, [
            [['order_1c', 'client_name', 'gearbox_id', 'year'], 'required'],
        ]);

        return $rules;
    }
}