<?php

namespace common\models\forms;

use common\chip\project\interfaces\CreateProjectFormInterface;
use common\models\User;

class CreateProject extends \yii\base\Model implements CreateProjectFormInterface
{

    public $vehicle_id;
    public $brand_id;
    public $model_id;
    public $generation_id;
    public $engine_id;
    public $timeframe;
    public $master;
    public $ecu_id;
    public $readmethod_id;
    public $tarif_id;
    public $gearbox_id;
    public $year;
    public $stage_id;
    public $on_dyno;
    public $vin_num;
    public $comments;
    public $client_name;
    public $registration_num;
    public $engine_hp;
    public $engine_kwt;
    public $client_1c;
    public $order_1c;
    public $created_by;
    public $soft_num;
    public $custom_ecu;
    public $hard_num;
    public $additions;
    public $file;
    public $toolsArray;
    public $readonlyOptions;
    public $requestAdditions;

//Projects[client_1c]:
//Projects[vehicle_id]: 1
//Projects[brand_id]: 55
//Projects[model_id]: 578
//Projects[generation_id]: 957
//Projects[engine_id]: 4346
//Projects[ecu_id]: 12007
//Projects[vin_num]: xcvcxbvcvcbnvbnvbbn
//Projects[registration_num]: vcbnvbnvb
//Projects[year]: 2013
//Projects[gearbox_id]: 3
//Projects[custom_ecu]: vcbnvbn
//Projects[order_1c]: смитмитмтим
//Projects[client_name]: митмитмит
//Projects[engine_hp]: 343
//Projects[engine_kwt]: 255
//Projects[stage_enabled]: 1
//Projects[additions][request][dtc]:
//Projects[additions][request][comment]: , ADBlue / SCR Removal , DPF / FAP Removal, EGR Removal
//, Flaps / Swirl Removal
//, MAF Removal
//, TVA Removal
//, Start-Stop Removal
//, CAT Removal
//, Speed Limiter Removal, Original file Request, DTC Removal, ExhFlap/LP EGR Removal
//additions[1][addition_id]: 1
//additions[2][addition_id]: 2
//additions[3][addition_id]: 3
//additions[4][addition_id]: 4
//additions[5][addition_id]: 5
//additions[6][addition_id]: 6
//additions[7][addition_id]: 7
//additions[8][addition_id]: 8
//additions[9][addition_id]: 9
//additions[10][addition_id]: 10
//additions[11][addition_id]: 11
//additions[12][addition_id]: 12
//Projects[master]: 1
//Projects[readmethod_id]: 60
//Projects[hard_num]: счиммсисмсим
//Projects[soft_num]: смисмисмчм
//Projects[timeframe]: 3
//Projects[on_dyno]: 0
//Projects[comments]: ми ми тмив прьтвепьрнеы тс тыав атп апт апт  ь пр с
//stage_id: 2
//Projects[file][]: YIazK8AbLgYl
//ajax: projectForm
    public function __construct()
    {
        $this->vehicle_id = null;
        $this->brand_id = null;
        $this->model_id = null;
        $this->generation_id = null;
        $this->engine_id = null;
        $this->ecu_id = null;
        $this->hard_num = null;
        $this->soft_num = null;
        $this->readmethod_id = null;
        $this->timeframe = null;
        $this->master = null;
        $this->gearbox_id = null;
        $this->year = null;
        $this->stage_id = null;
        $this->on_dyno = null;
        $this->vin_num = null;
        $this->comments = null;
        $this->client_name = null;
        $this->registration_num = null;
        $this->engine_hp = null;
        $this->engine_kwt = null;
        $this->client_1c = null;
        $this->order_1c = null;
        $this->custom_ecu = null;
        $this->toolsArray = null;
        $this->file = [];
        $this->additions = [];
        $this->readonlyOptions = [];
    }


    public function rules()
    {
        return [
            [['vehicle_id', 'brand_id', 'model_id', 'generation_id', 'engine_id', 'ecu_id', 'readmethod_id', 'timeframe', 'master'], 'required'],
            [['vehicle_id', 'brand_id', 'model_id', 'generation_id', 'engine_id', 'ecu_id', 'readmethod_id', 'tarif_id', 'gearbox_id', 'year', 'master'], 'integer'],
            [['tarif_id', 'stage_id',  'on_dyno', 'vin_num', 'comments', 'client_name', 'client_info', 'registration_num', 'status', 'engine_hp', 'engine_kwt',  'custom_ecu', 'hard_num', 'soft_num', 'client_1c', 'order_1c', 'file', 'toolsArray', 'additions', 'readonlyOptions', 'requestAdditions'], 'safe'],
            [['vin_num', 'hard_num', 'soft_num', 'registration_num', 'engine_hp', 'engine_kwt', 'custom_ecu'], 'string', 'max' => 255],
        ];
    }

    public function getRequestOptions(): array
    {
        return [
            'additions' => $this->additions,
            'requestAdditions' => $this->requestAdditions,
        ];
    }
}