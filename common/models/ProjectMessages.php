<?php

namespace common\models;

use common\helpers\ProjectHelper;
use common\models\query\ProjectMessagesQuery;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\CodeCoverage\Report\Xml\Project;
use Yii;

/**
 * This is the model class for table "project_messages".
 *
 * @property int $id
 * @property string $title
 * @property string $content
 * @property int $sys
 * @property int $type
 * @property int $send_to
 * @property string $created_at Когда создан
 * @property string $updated_at Когда обновлен
 * @property string $deleted_at Когда удален
 * @property int $created_by Кем создан
 * @property int $updated_by Кем обновлен
 * @property int $deleted_by Кем удален
 * @property int $project_id Проект
 * @property string $path Путь
 * @property string $filename Имя файла
 * @property int $can_download Разрешено скачивать
 * @property int $downloaded Скачан
 * @property string $params Данные о файле
 * @property int $downloaded_by <PERSON>е<PERSON> скачан
 * @property string $downloaded_at Когда скачан
 * @property int $download_count Сколько раз скачан
 * @property string $file_history История файла
 * @property int $isDeleted
 * @property int $is_shown
 * @property int $is_notified
 * @property string $comment Текст комментария
 * ALTER TABLE `project_messages` ADD `send_to` INT(11) NULL AFTER `project_id`;
 * ALTER TABLE `project_messages` CHANGE `send_to` `send_to` VARCHAR(255) NULL DEFAULT NULL;
 */
class ProjectMessages extends BaseChipModel
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'project_messages';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['title', 'project_id'], 'required'],
            [['created_at', 'updated_at', 'deleted_at', 'downloaded_at', 'comment', 'hash', 'is_shown', 'is_notified', 'sys', 'content', 'type', 'send_to', 'path', 'filename'], 'safe'],
            [['created_by', 'updated_by', 'deleted_by', 'project_id', 'can_download', 'downloaded', 'downloaded_by', 'download_count', 'isDeleted', 'is_shown', 'is_notified', 'sys', 'type'], 'integer'],
            [['params', 'file_history'], 'string'],
            [['title', 'path', 'filename'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'title' => Yii::t('app', 'Title'),
            'content' => Yii::t('app', 'Сообщение'),
            'sys' => Yii::t('app', 'Системное сообщение'),
            'created_at' => Yii::t('app', 'Когда создан'),
            'updated_at' => Yii::t('app', 'Когда обновлен'),
            'deleted_at' => Yii::t('app', 'Когда удален'),
            'created_by' => Yii::t('app', 'Кем создан'),
            'updated_by' => Yii::t('app', 'Кем обновлен'),
            'deleted_by' => Yii::t('app', 'Кем удален'),
            'project_id' => Yii::t('app', 'Проект'),
            'path' => Yii::t('app', 'Путь'),
            'filename' => Yii::t('app', 'Имя файла'),
            'can_download' => Yii::t('app', 'Разрешено скачивать'),
            'downloaded' => Yii::t('app', 'Скачан'),
            'params' => Yii::t('app', 'Данные о файле'),
            'downloaded_by' => Yii::t('app', 'Кем скачан'),
            'downloaded_at' => Yii::t('app', 'Когда скачан'),
            'download_count' => Yii::t('app', 'Сколько раз скачан'),
            'file_history' => Yii::t('app', 'История файла'),
            'isDeleted' => Yii::t('app', 'Is Deleted'),
            'hash' => Yii::t('app', 'Хэш'),
            'comment' => Yii::t('app', 'Комментарий'),
        ];
    }

    /**
     * {@inheritdoc}
     * @return ProjectMessagesQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new ProjectMessagesQuery(get_called_class());
    }


    /**
     * обновляем проект после добавления в него сообщения, не важно от кого сообщение, системы или пользователя
     * @param bool $insert
     * @param array $changedAttributes
     */
//    public function afterSave($insert, $changedAttributes)
//    {
//        $modelProject = Projects::findOne($this->project_id);
//        $modelProject->status = ProjectHelper::STATUS_OPEN;
//        $modelProject->save(false);

//        parent::afterSave($insert, $changedAttributes); // TODO: Change the autogenerated stub
//    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getCreator()
    {
        return $this->hasOne(User::className(), ['id' => 'created_by']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getProject()
    {
        return $this->hasOne(Projects::className(), ['id' => 'project_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getNotif()
    {
        return Yii::$app->controller->renderPartial('/partial/message_notif', ['message' => $this]);
    }

    /**
     * @return boolean
     */
    public function isFileUploadMessage()
    {
        return $this->title == 'File uploaded';
    }

    /**
     * @return boolean
     */
    public function isChangedStatus()
    {
        return $this->comment == 'New status: Changed';
//        return $this->project->status == ProjectHelper::STATUS_CHANGED;
    }
    /**
     * @return boolean
     */
    public function isNewProject()
    {
        return $this->title == 'Project created';
//        return $this->project->status == ProjectHelper::STATUS_NEW;
    }

    public function getShowRowClass()
    {
        return $this->created_by === 100 ? 'by-autopack' : '';
    }

}
