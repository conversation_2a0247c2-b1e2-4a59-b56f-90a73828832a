<?php

namespace common\models\query;

/**
 * This is the ActiveQuery class for [[\app\models\ProjectMessagesSend]].
 *
 * @see \app\models\ProjectMessagesSend
 */
class ProjectMessagesSendQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * {@inheritdoc}
     * @return \app\models\ProjectMessagesSend[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * {@inheritdoc}
     * @return \app\models\ProjectMessagesSend|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
