<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "autopack_sett_logs".
 *
 * @property int $id
 * @property int $autopack_sett_id Настройка автопака, которая обрабатывается в текущий момент
 * @property int $project_client_id Проект клиентский, который обрабатывается автопаком
 * @property int $file_sett_orig_id Оригинальный файл из настроек автопака по ЭБУ и параметрам тюнинга
 * @property int $file_sett_mod_id Модифицированный файл из настроек автопака по ЭБУ и параметрам тюнинга
 * @property int $file_client_orig_id Оригинальный файл клиента из текущего обрабатываемого проекта
 * @property int $file_client_mod_id Модифицированный файл, обработанный автопаком и добавленый в проект
 * @property int $processed Эта попытка уже обработана
 * @property int $solved Это правильное решение
 * @property int $stage_id Стейдж тюнинга, для памяти
 * @property string $additions Параметры тюнинга
 * @property string $autopack_result Результаты работы автопака
 * @property string $created_at
 * @property string $updated_at
 * @property int $created_by
 * @property int $updated_by
 * @property int $isDeleted
 * @property int $deleted_by
 */
class AutopackSettLog extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'autopack_sett_logs';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['autopack_sett_id', 'project_client_id', 'file_sett_orig_id', 'file_sett_mod_id', 'file_client_orig_id', 'file_client_mod_id', 'processed', 'solved', 'stage_id', 'created_by', 'updated_by', 'isDeleted', 'deleted_by'], 'integer'],
            [['additions', 'autopack_result'], 'string'],
            [['created_at', 'updated_at'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'autopack_sett_id' => Yii::t('app', 'Настройка автопака, которая обрабатывается в текущий момент'),
            'project_client_id' => Yii::t('app', 'Проект клиентский, который обрабатывается автопаком'),
            'file_sett_orig_id' => Yii::t('app', 'Оригинальный файл из настроек автопака по ЭБУ и параметрам тюнинга'),
            'file_sett_mod_id' => Yii::t('app', 'Модифицированный файл из настроек автопака по ЭБУ и параметрам тюнинга'),
            'file_client_orig_id' => Yii::t('app', 'Оригинальный файл клиента из текущего обрабатываемого проекта'),
            'file_client_mod_id' => Yii::t('app', 'Модифицированный файл, обработанный автопаком и добавленый в проект'),
            'processed' => Yii::t('app', 'Эта попытка уже обработана'),
            'solved' => Yii::t('app', 'Это правильное решение'),
            'stage_id' => Yii::t('app', 'Стейдж тюнинга, для памяти'),
            'additions' => Yii::t('app', 'Параметры тюнинга'),
            'autopack_result' => Yii::t('app', 'Результаты работы автопака'),
            'created_at' => Yii::t('app', 'Created At'),
            'updated_at' => Yii::t('app', 'Updated At'),
            'created_by' => Yii::t('app', 'Created By'),
            'updated_by' => Yii::t('app', 'Updated By'),
            'isDeleted' => Yii::t('app', 'Is Deleted'),
            'deleted_by' => Yii::t('app', 'Deleted By'),
        ];
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\AutopackSettLogsQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\AutopackSettLogsQuery(get_called_class());
    }
}
