<?php
namespace common\chip\autopack\entities\dto;

class AuthDto
{
    private string $authUrl;
    private string $apiUrl;
    private string $username;
    private string $password;


    public function __construct(string $apiUrl, string $username, string $password)
    {
        $this->apiUrl = $apiUrl;
        $this->username = $username;
        $this->password = $password;
    }

    /**
     * @return AuthDto
     */
    public static function fromEnv(): AuthDto
    {
        $dto =  new AuthDto(env('AUTOPACK_API_URL'), env('AUTOPACK_USERNAME'), env('AUTOPACK_PASSWORD'));
        $dto->setAuthUrl('/login');
        return $dto;
    }

    /**
     * @return string
     */
    public function getAuthUrl(): string
    {
        return $this->authUrl;
    }

    /**
     * @param string $authUrl
     */
    public function setAuthUrl(string $authUrl): void
    {
        $this->authUrl = $authUrl;
    }

    /**
     * @return mixed
     */
    public function getApiUrl(): mixed
    {
        return $this->apiUrl;
    }

    /**
     * @param mixed $apiUrl
     */
    public function setApiUrl(mixed $apiUrl): void
    {
        $this->apiUrl = $apiUrl;
    }

    /**
     * @return mixed
     */
    public function getUsername(): mixed
    {
        return $this->username;
    }

    /**
     * @param mixed $username
     */
    public function setUsername(mixed $username): void
    {
        $this->username = $username;
    }

    /**
     * @return mixed
     */
    public function getPassword(): mixed
    {
        return $this->password;
    }

    /**
     * @param mixed $password
     */
    public function setPassword(mixed $password): void
    {
        $this->password = $password;
    }

}