<?php
namespace common\chip\autopack\entities\dto;

class AutopackProcDto extends AutopackDto
{

    protected int $project_id;
    protected int $project_file_id;
    protected int $config_id;

    public function __construct(int $projectId = 0, int $projectFileId = 0, int $configId = 0)
    {
        $this->project_id = $projectId ?? 0;
        $this->project_file_id = $projectFileId ?? 0;
        $this->config_id = $configId ?? 0;
    }

    /**
     * @return int
     */
    public function getProjectId(): int
    {
        return $this->project_id;
    }

    /**
     * @param int $project_id
     */
    public function setProjectId(int $project_id): void
    {
        $this->project_id = $project_id;
    }

    /**
     * @return int
     */
    public function getProjectFileId(): int
    {
        return $this->project_file_id;
    }

    /**
     * @param int $project_file_id
     */
    public function setProjectFileId(int $project_file_id): void
    {
        $this->project_file_id = $project_file_id;
    }

    /**
     * @return int
     */
    public function getConfigId(): int
    {
        return $this->config_id;
    }

    /**
     * @param int $config_id
     */
    public function setConfigId(int $config_id): void
    {
        $this->config_id = $config_id;
    }

}