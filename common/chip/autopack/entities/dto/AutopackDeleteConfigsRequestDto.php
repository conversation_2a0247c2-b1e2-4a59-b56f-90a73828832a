<?php
namespace common\chip\autopack\entities\dto;

class AutopackDeleteConfigsRequestDto
{

    private array $configs;
    public function __construct(array $data)
    {
        $this->configs = $data['configs'] ?? [];
    }

    /**
     * @return array
     */
    public function getConfigs(): array
    {
        return $this->configs;
    }

    /**
     * @param array $configs
     */
    public function setConfigs(array $configs): void
    {
        $this->configs = $configs;
    }

}