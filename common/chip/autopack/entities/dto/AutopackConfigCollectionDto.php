<?php
namespace common\chip\autopack\entities\dto;

class AutopackConfigCollectionDto extends AutopackDto
{

    private array $items;

    /**
     * @param array $items
     */
    public function __construct(array $data)
    {
        $this->items = [];
        $this->buildItemsByData($data);
    }

    public function getItems(): array
    {
        return $this->items;
    }

    private function buildItemsByData(array $data)
    {
        if (empty($data)) {
            return;
        }
        if (!is_array($data)) {
            return;
        }
        if (count($data) == 0) {
            return;
        }
        foreach ($data as $element) {
            $this->addItem(new AutopackConfigDto($element));
        }
    }

    private function addItem(AutopackConfigDto $item)
    {
        $this->items[] = $item;
    }

    public function getItemsArray(): array
    {
        $result = [];
        foreach ($this->items as $item) {
            $result[] = $item->toArray();
        }
        return $result;
    }
}