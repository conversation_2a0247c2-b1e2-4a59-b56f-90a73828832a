<?php
namespace common\chip\autopack\entities\dto;

class AutopackTestModResponseDto
{

    private string $path;
    /**
     * @var string|mixed
     */
    private string $name;
    /**
     * @var string|string
     */
    private string $type;
    /**
     * @var string|mixed
     */

    /**
     * @param int $ecu_id
     */
    public function __construct(array $data)
    {
        $this->path = $data['path'] ?? 0;
        $this->name = $data['name'] ?? 0;
        $this->type = $data['type'] ?? 'test';
    }

    /**
     * @return string
     */
    public function getPath(): string
    {
        return $this->path;
    }

    /**
     * @param string $path
     */
    public function setPath(string $path): void
    {
        $this->path = $path;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @param string $name
     */
    public function setName(string $name): void
    {
        $this->name = $name;
    }

    /**
     * @return string
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * @param string $type
     */
    public function setType(string $type): void
    {
        $this->type = $type;
    }


}