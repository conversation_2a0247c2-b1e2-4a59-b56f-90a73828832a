<?php
namespace common\chip\autopack\entities\dto;

class AutopackDeleteConfigRequestDto
{

    private int $config_id;
    /**
     * @var int|mixed
     */

    /**
     * @param int $ecu_id
     */
    public function __construct(array $data)
    {
        $this->config_id = $data['config_id'] ?? 0;
    }

    /**
     * @return int
     */
    public function getConfigId(): int
    {
        return $this->config_id;
    }

    /**
     * @param int $config_id
     */
    public function setConfigId(int $config_id): void
    {
        $this->config_id = $config_id;
    }

}