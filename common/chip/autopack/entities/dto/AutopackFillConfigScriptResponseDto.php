<?php
namespace common\chip\autopack\entities\dto;

class AutopackFillConfigScriptResponseDto extends AutopackDto
{
    /**
     * @var int|mixed
     */
    protected $config_id;
    /**
     * @var mixed|string
     */
    protected $status;
    /**
     * @var mixed|string
     */
    protected $script_size;

    /**
     * @param int $ecu_id
     */
    public function __construct($data)
    {
        $this->config_id = $data->config_id ?? 0;
        $this->status = $data->status ?? '';
        $this->script_size = $data->script_size ?? '';
    }

    /**
     * @return int|mixed
     */
    public function getConfigId(): mixed
    {
        return $this->config_id;
    }

    /**
     * @param int|mixed $config_id
     */
    public function setConfigId(mixed $config_id): void
    {
        $this->config_id = $config_id;
    }

    /**
     * @return mixed|string
     */
    public function getStatus(): mixed
    {
        return $this->status;
    }

    /**
     * @param mixed|string $status
     */
    public function setStatus(mixed $status): void
    {
        $this->status = $status;
    }

    /**
     * @return mixed|string
     */
    public function getScriptSize(): mixed
    {
        return $this->script_size;
    }

    /**
     * @param mixed|string $script_size
     */
    public function setScriptSize(mixed $script_size): void
    {
        $this->script_size = $script_size;
    }



}