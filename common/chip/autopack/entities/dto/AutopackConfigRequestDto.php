<?php
namespace common\chip\autopack\entities\dto;

class AutopackConfigRequestDto
{

    private int $ecu_id;
    /**
     * @var int|mixed
     */
    private array $brands;
    /**
     * @var mixed|string
     */
    private string $software_num;
    /**
     * @var mixed|string
     */
    private array $additions;
    /**
     * @var mixed|string
     */
    private string $orig_file;
    /**
     * @var mixed|string
     */
    private string $mod_file;
    /**
     * @var array|mixed
     */
    private array $stages;

    /**
     * @param int $ecu_id
     */
    public function __construct(array $data)
    {
        $this->ecu_id = $data['ecu_id'] ?? 0;
        $this->stages = $data['stages'] ?? [];
        $this->brands = $data['brands'] ?? [];
        $this->orig_file = $data['orig_file'] ?? 0;
        $this->mod_file = $data['mod_file'] ?? 0;
        $this->software_num = $data['software_number'] ?? '';
        $this->additions = $data['additions'] ?? '';
    }


    /**
     * @return int
     */
    public function getEcuId(): int
    {
        return $this->ecu_id ?? 0;
    }

    /**
     * @return array
     */
    public function getBrands(): array
    {
        return $this->brands ?? [];
    }

    /**
     * @return string
     */
    public function getSoftwareNum(): string
    {
        return $this->software_num ?? '';
    }

    /**
     * @return array
     */
    public function getAdditions(): array
    {
        return $this->additions ?? [];
    }

    /**
     * @return int
     */
    public function getStage(): int
    {
        if (count($this->stages) > 0) {
            return $this->stages[0];
        }
        return 0;
    }

    public function getOrigFile(): string
    {
        return $this->orig_file;
    }

    public function setOrigFile(string $orig_file): void
    {
        $this->orig_file = $orig_file;
    }

    public function getModFile(): string
    {
        return $this->mod_file;
    }

    public function setModFile(string $mod_file): void
    {
        $this->mod_file = $mod_file;
    }

}