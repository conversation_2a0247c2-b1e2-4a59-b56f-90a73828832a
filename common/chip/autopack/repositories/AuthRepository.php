<?php
namespace common\chip\autopack\repositories;

use common\chip\autopack\entities\dto\AuthDto;

class AuthRepository
{
    private AuthDto $dto;
    private ?string $token;

    /**
     * AuthRepository constructor.
     */
    public function __construct()
    {
        $this->dto = AuthDto::fromEnv();
        $this->token = null;
    }

    /**
     * @return AuthDto
     */
    public function getDto(): AuthDto
    {
        return $this->dto;
    }

    /**
     * @return string|null
     */
    public function getToken(): ?string
    {
        return $this->token;
    }

    public function getAuthUrl()
    {
        return $this->dto->getAuthUrl();
    }

}