<?php

namespace common\chip\event;

use common\chip\event\core\EventInterface;

/**
 * Интерфейс обработчика событий
 */
interface EventHandler
{
    /**
     * Обрабатывает событие
     * 
     * @param EventInterface $event Событие для обработки
     * @return void
     */
    public function handle(EventInterface $event): void;
    
    /**
     * Проверяет, может ли этот обработчик обработать данное событие
     * 
     * @param EventInterface $event Событие для проверки
     * @return bool
     */
    public function canHandle(EventInterface $event): bool;
}
