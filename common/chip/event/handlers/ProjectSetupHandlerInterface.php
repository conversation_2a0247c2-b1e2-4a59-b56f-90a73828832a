<?php

namespace common\chip\event\handlers;

use common\chip\event\core\EventInterface;
use common\chip\event\EventHandler;
use common\chip\event\project\ProjectSetupRequestedEvent;

/**
 * Обработчик для выполнения начальной настройки проекта
 */
class ProjectSetupHandler implements EventHandler
{
    /**
     * {@inheritdoc}
     */
    public function canHandle(EventInterface $event): bool
    {
        return $event instanceof ProjectSetupRequestedEvent;
    }
    
    /**
     * {@inheritdoc}
     */
    public function handle(EventInterface $event): void
    {
        if (!$this->canHandle($event)) {
            return;
        }
        
        /** @var ProjectSetupRequestedEvent $event */
        $projectId = $event->getProjectId();
        $configuration = $event->getConfiguration();
        
        // Здесь мы бы выполняли реальную настройку проекта
        // Для примера, просто выводим лог
        $this->log("Выполняется настройка проекта #{$projectId} с параметрами: " . json_encode($configuration));
        
        // Симуляция долгой операции
        sleep(1);
        
        $this->log("Настройка проекта #{$projectId} успешно завершена");
    }
    
    /**
     * Логирует сообщение с использованием доступных средств
     * 
     * @param string $message Сообщение для логирования
     * @return void
     */
    private function log(string $message): void
    {
        if (class_exists('\Yii') && \Yii::$app !== null) {
            \Yii::info($message, 'project.setup');
        }
    }
}
