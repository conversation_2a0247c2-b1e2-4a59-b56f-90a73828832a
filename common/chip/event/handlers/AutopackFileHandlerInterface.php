<?php

namespace common\chip\event\handlers;

use common\chip\autopack\jobs\AutopackStartModificationJob;
use common\chip\event\core\BaseEvent;
use common\chip\event\core\EventInterface;
use common\chip\event\core\FilePackedEvent;
use common\chip\event\core\FileProcessedEvent;
use common\chip\event\core\FileProcessingErrorEvent;
use common\chip\event\core\FileUnpackedEvent;
use common\chip\event\core\FileUploadedEvent;
use common\chip\event\EventHandler;

use common\chip\event\EventDispatcher;
use common\chip\notification\events\SystemErrorEvent;
use common\models\ProjectFiles;
use common\models\Projects;
use Yii;

/**
 * Обработчик файловых событий
 */
class AutopackFileHandler implements EventHandler
{
    /**
     * @var EventDispatcher Диспетчер событий
     */
    private EventDispatcher $eventDispatcher;

    /**
     * @var array Конфигурация обработчика
     */
    private array $config;

    /**
     * Конструктор
     *
     * @param EventDispatcher|null $eventDispatcher Диспетчер событий
     * @param array $config Конфигурация обработчика
     */
    public function __construct(EventDispatcher $eventDispatcher = null, array $config = [])
    {
        $this->eventDispatcher = $eventDispatcher ?: Yii::$container->get(EventDispatcher::class);
        $this->config = array_merge([
            'enabled' => true,
            'logEvents' => true,
            'errorNotificationsEnabled' => true,
        ], $config);
    }

    /**
     * Обработать событие
     *
     * @param BaseEvent $event Событие
     * @return void
     */
    public function handle(EventInterface $event): void
    {
        // Проверяем, включен ли обработчик
        if (!$this->config['enabled']) {
            return;
        }

        // Логируем событие, если включено
        if ($this->config['logEvents']) {
            $this->logEvent($event);
        }

        // Обрабатываем разные типы событий
        if ($event instanceof FileUploadedEvent) {
            $this->handleFileUploaded($event);
        } elseif ($event instanceof FileUnpackedEvent) {
            $this->handleFileUnpacked($event);
        } elseif ($event instanceof FilePackedEvent) {
            $this->handleFilePacked($event);
        } elseif ($event instanceof FileProcessedEvent) {
            $this->handleFileProcessed($event);
        } elseif ($event instanceof FileProcessingErrorEvent) {
            // Обрабатываем ошибки только если включено в конфигурации
            if ($this->config['errorNotificationsEnabled']) {
                $this->handleFileProcessingError($event);
            }
        }
    }

    /**
     * Обработать событие загрузки файла
     *
     * @param FileUploadedEvent $event Событие
     * @return void
     */
    private function handleFileUploaded(FileUploadedEvent $event): void
    {
        dump($event);
        $project = Projects::findOne($event->getProjectId());
        $file = ProjectFiles::findOne($event->getFileId());
        if ($project->readMethod->master){
            Yii::$app->queue->push(new AutopackStartModificationJob(['file' => $file]));
        }
    }

    /**
     * Обработать событие распаковки файла
     *
     * @param FileUnpackedEvent $event Событие
     * @return void
     */
    private function handleFileUnpacked(FileUnpackedEvent $event): void
    {
        // Событие уже будет обработано UniversalNotificationHandler автоматически
        // Дополнительная логика для Autopack, если нужна
    }

    /**
     * Обработать событие запаковки файлов
     *
     * @param FilePackedEvent $event Событие
     * @return void
     */
    private function handleFilePacked(FilePackedEvent $event): void
    {
        // Событие уже будет обработано UniversalNotificationHandler автоматически
        // Дополнительная логика для Autopack, если нужна
    }

    /**
     * Обработать событие обработки файла
     *
     * @param FileProcessedEvent $event Событие
     * @return void
     */
    private function handleFileProcessed(FileProcessedEvent $event): void
    {
        // Событие уже будет обработано UniversalNotificationHandler автоматически
        // Дополнительная логика для Autopack, если нужна
    }

    /**
     * Обработать событие ошибки обработки файла
     *
     * @param FileProcessingErrorEvent $event Событие
     * @return void
     */
    private function handleFileProcessingError(FileProcessingErrorEvent $event): void
    {
        // Логируем ошибку
        \Yii::error('Autopack file processing error: ' . $event->getErrorMessage(), 'autopack');

        // Создаем системное событие ошибки для уведомлений
        $systemErrorEvent = new SystemErrorEvent(
            errorType: 'autopack_file_processing',
            service: 'autopack',
            errorMessage: $event->getErrorMessage(),
            severity: 'high',
            affectedProjects: [$event->getProjectId()],
            errorContext: [
                'file_id' => $event->getFileId(),
                'operation' => $event->getOperation(),
                'original_event' => get_class($event)
            ]
        );

        // Отправляем событие асинхронно
        $this->eventDispatcher->dispatchAsync(
            $systemErrorEvent,
            'autopack',
            $event->getFileId(),
            0
        );
    }

    /**
     * Логировать событие
     *
     * @param BaseEvent $event Событие
     * @return void
     */
    private function logEvent(BaseEvent $event): void
    {
        // Логирование события
        $eventType = get_class($event);
        $context = [
            'event_type' => $eventType,
            'source_id' => $event->getSourceId(),
            'context_data' => $event->getContextData(),
        ];

        // Добавляем дополнительную информацию в зависимости от типа события
        if ($event instanceof FileUploadedEvent) {
            $context['file_id'] = $event->getFileId();
            $context['user_id'] = $event->getUserId();
        } elseif ($event instanceof FileProcessingErrorEvent) {
            $context['file_id'] = $event->getFileId();
            $context['operation'] = $event->getOperation();
            $context['error_message'] = $event->getErrorMessage();
        }

        // Используем Yii2 логирование
        \Yii::info('File event received: ' . $eventType, 'notification');
        \Yii::debug($context, 'notification');
    }

    public function canHandle(EventInterface $event): bool
    {
        return true;
    }
}
