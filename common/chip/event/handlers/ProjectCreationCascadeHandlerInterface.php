<?php

namespace common\chip\event\handlers;

use common\chip\event\core\EventInterface;
use common\chip\event\core\ProjectCreatedEvent;
use common\chip\event\EventDispatcher;
use common\chip\event\EventHandler;
use common\chip\event\EventSystemBootstrap;
use common\chip\event\project\ProjectEventFactory;
use common\helpers\MessageHelper;

/**
 * Обработчик для генерации цепочки событий при создании проекта
 */
class ProjectCreationCascadeHandler implements EventHandler
{
    private EventDispatcher $eventDispatcher;
    private ProjectEventFactory $eventFactory;
    
    /**
     * @param EventDispatcher|null $eventDispatcher Диспетчер событий
     * @param ProjectEventFactory|null $eventFactory Фабрика событий
     */
    public function __construct(
        ?EventDispatcher $eventDispatcher = null,
        ?ProjectEventFactory $eventFactory = null
    ) {
        $this->eventDispatcher = $eventDispatcher ?? EventSystemBootstrap::getDispatcher();
        $this->eventFactory = $eventFactory ?? new ProjectEventFactory();
    }
    
    /**
     * {@inheritdoc}
     */
    public function canHandle(EventInterface $event): bool
    {
        return false;
    }
    
    /**
     * {@inheritdoc}
     */
    public function handle(EventInterface $event): void
    {
        if (!$this->canHandle($event)) {
            return;
        }
        
        /** @var ProjectCreatedEvent $event */
        $projectId = $event->getSourceId();
        $userId = $event->getUserId();

        // 1. Генерируем событие для настройки проекта
        $setupEvent = $this->eventFactory->createProjectSetupRequestedEvent(
            $projectId, 
            [
                'initial_setup' => true,
                'created_by' => $userId,
            ]
        );
        
        // Асинхронно отправляем событие настройки проекта
        $this->eventDispatcher->dispatchAsync($setupEvent);
        
        // 2. Генерируем событие для отправки уведомления клиенту
        $notifyEvent = $this->eventFactory->createNotifyClientRequestedEvent(
            $projectId,
            $userId,
            MessageHelper::TYPE_NOTE,
            'Проект успешно создан',
            "Ваш проект  был успешно создан и отправлен на обработку. " .
            "Наши специалисты приступят к работе в ближайшее время."
        );
        
        // Асинхронно отправляем событие уведомления клиента
        $this->eventDispatcher->dispatchAsync($notifyEvent);
    }
}
