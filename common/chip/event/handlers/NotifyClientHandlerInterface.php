<?php

namespace common\chip\event\handlers;

use common\chip\event\core\EventInterface;
use common\chip\event\EventHandler;
use common\chip\event\project\NotifyClientRequestedEvent;
use common\chip\project\entities\dto\ProjectMessageTelegramDto;
use common\chip\project\services\MessageService;
use common\helpers\MessageHelper;

/**
 * Обработчик для отправки уведомлений клиенту
 */
class NotifyClientHandler implements EventHandler
{
    private MessageService $messageService;
    
    /**
     * @param MessageService $messageService Сервис сообщений
     */
    public function __construct(MessageService $messageService)
    {
        $this->messageService = $messageService;
    }
    
    /**
     * {@inheritdoc}
     */
    public function canHandle(EventInterface $event): bool
    {
        return $event instanceof NotifyClientRequestedEvent;
    }
    
    /**
     * {@inheritdoc}
     */
    public function handle(EventInterface $event): void
    {
        if (!$this->canHandle($event)) {
            return;
        }
        
        /** @var NotifyClientRequestedEvent $event */
        try {
            $messageDto = new ProjectMessageTelegramDto([
                'project_id' => $event->getSourceId(),
                'title' => $event->getMessageTitle(),
                'content' => $event->getMessageContent(),
                'comment' => $event->getMessageContent(),
                'type' => $event->getMessageType(),
                'send_to' => $event->getUserId(),
                'created_by' => MessageHelper::ADMIN_USER_ID,
            ]);
            
            $this->messageService->startSendMessage($messageDto);
            
            $this->log("Уведомление для пользователя #{$event->getUserId()} отправлено");
        } catch (\Throwable $e) {
            $this->log("Ошибка при отправке уведомления: {$e->getMessage()}", 'error');
        }
    }
    
    /**
     * Логирует сообщение с использованием доступных средств
     * 
     * @param string $message Сообщение для логирования
     * @param string $level Уровень логирования
     * @return void
     */
    private function log(string $message, string $level = 'info'): void
    {
        if (class_exists('\Yii') && \Yii::$app !== null) {
            if ($level === 'error') {
                \Yii::error($message, 'project.notification');
            } else {
                \Yii::info($message, 'project.notification');
            }
        }
    }
}
