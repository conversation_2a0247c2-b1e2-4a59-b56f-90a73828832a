<?php

namespace common\chip\event\project;

use common\chip\event\core\OptionsRequestEvent;
use common\chip\event\core\ProjectCreatedEvent;
use common\chip\project\interfaces\CreateProjectFormInterface;
use common\models\Projects;

/**
 * Фабрика для создания событий, связанных с проектами
 */
class ProjectEventFactory
{

    public function createProjectCreatedEvent(CreateProjectFormInterface $modelForm): ProjectCreatedEvent
    {
        return new ProjectCreatedEvent('', ['modelForm' => $modelForm]);
    }

    public function createOptionsRequestEvent(Projects $project): OptionsRequestEvent
    {
        return new OptionsRequestEvent($project->id, []);
    }
    
    /**
     * Создает событие для запуска настройки проекта
     * 
     * @param int $projectId ID проекта
     * @param array $configuration Конфигурационные параметры
     * @return ProjectSetupRequestedEvent
     */
    public function createProjectSetupRequestedEvent(int $projectId, array $configuration = []): ProjectSetupRequestedEvent
    {
        return new ProjectSetupRequestedEvent($projectId, $configuration);
    }
    
    /**
     * Создает событие для отправки уведомления клиенту
     * 
     * @param int $projectId ID проекта
     * @param int $userId ID пользователя
     * @param string $messageType Тип сообщения
     * @param string $messageTitle Заголовок сообщения
     * @param string $messageContent Содержание сообщения
     * @return NotifyClientRequestedEvent
     */
    public function createNotifyClientRequestedEvent(
        int $projectId, 
        int $userId, 
        string $messageType, 
        string $messageTitle, 
        string $messageContent
    ): NotifyClientRequestedEvent {
        return new NotifyClientRequestedEvent(
            $projectId, 
            $userId, 
            $messageType, 
            $messageTitle, 
            $messageContent
        );
    }
}
