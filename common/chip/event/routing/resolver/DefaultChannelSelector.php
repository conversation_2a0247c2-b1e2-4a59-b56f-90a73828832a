<?php

namespace common\chip\event\routing\resolver;

use common\chip\notification\channel\NotificationChannelInterface;
use common\models\notification\Notification;


/**
 * Реализация селектора каналов доставки уведомлений
 */
class DefaultChannelSelector implements ChannelSelectorInterface
{
    private array $channels = [];
    private array $channelConfig = [];

    public function __construct(
        array $channelConfig = []
    ) {
        $this->channelConfig = $channelConfig;
    }

    /**
     * Регистрирует канал доставки
     *
     * @param NotificationChannelInterface $channel Канал доставки
     */
    public function registerChannel(NotificationChannelInterface $channel): void
    {
        $this->channels[$channel->getId()] = $channel;
    }

    /**
     * {@inheritdoc}
     */
    public function selectChannels(Notification $notification, $recipient, string $role): array
    {
        $eventType = $notification->getEventType();
        $priority = $notification->getMetadata('priority') ?? 'normal';

        // Получаем каналы по приоритету:
        // 1. Каналы для конкретного получателя (высший приоритет)
        // 2. Каналы для роли получателя
        // 3. Общие каналы из context/source_context уведомления
        // 4. Каналы по умолчанию для типа события
        $selectedChannelIds = $this->getChannelsWithPriority($notification, $recipient, $role, $eventType);

        \Yii::info([
            'message' => 'Выбраны каналы для уведомления',
            'eventType' => $eventType,
            'selectedChannels' => $selectedChannelIds,
            'priority' => $priority,
            'role' => $role,
            'recipient' => is_object($recipient) && method_exists($recipient, 'getId') ? $recipient->getId() : $recipient
        ], 'notification');

        // Фильтруем каналы по активности, поддержке приоритета и типа события
        $selectedChannels = [];
        foreach ($selectedChannelIds as $channelId) {
            if (!isset($this->channels[$channelId])) {
                continue;
            }

            $channel = $this->channels[$channelId];

            // Проверяем, активен ли канал
            if (!$channel->isActive()) {
                continue;
            }

            // Проверяем, поддерживает ли канал данный приоритет
            if (!$channel->supportsPriority($priority)) {
                // Для высокого приоритета выбираем все каналы независимо от поддержки приоритета
                if ($priority !== 'high') {
                    continue;
                }
            }

            // Проверяем, поддерживает ли канал данный тип события
            if (!$channel->supportsEventType($eventType)) {
                continue;
            }

            $selectedChannels[] = $channel;
        }

        // Если для высокого приоритета не выбран ни один канал, добавляем канал UI по умолчанию
        if ($priority === 'high' && empty($selectedChannels) && isset($this->channels['ui_popup'])) {
            $selectedChannels[] = $this->channels['ui_popup'];
        }

        return $selectedChannels;
    }

    /**
     * {@inheritdoc}
     */
    public function getAllChannels(): array
    {
        return $this->channels;
    }

    /**
     * {@inheritdoc}
     */
    public function getChannelsForRole(string $role): array
    {
        $roleChannels = [];

        // Ищем каналы, доступные для данной роли
        foreach ($this->channels as $channel) {
            if ($this->isChannelAllowedForRole($channel->getId(), $role)) {
                $roleChannels[] = $channel;
            }
        }

        return $roleChannels;
    }

    /**
     * {@inheritdoc}
     */
    public function getChannelsForUser($user): array
    {
        $userId = $user->getId();

        $userChannels = [];

        // Если у пользователя есть настройки по умолчанию
        if (isset($userPreferences['default'])) {
            foreach ($userPreferences['default'] as $channelId) {
                if (isset($this->channels[$channelId]) && $this->channels[$channelId]->isActive()) {
                    $userChannels[] = $this->channels[$channelId];
                }
            }
            return $userChannels;
        }

        // Если нет настроек, возвращаем все активные каналы
        foreach ($this->channels as $channel) {
            if ($channel->isActive()) {
                $userChannels[] = $channel;
            }
        }

        return $userChannels;
    }

    /**
     * Получает каналы с учетом приоритета: получатель > роль > общие > по умолчанию
     *
     * @param Notification $notification Уведомление
     * @param mixed $recipient Получатель
     * @param string $role Роль получателя
     * @param string $eventType Тип события
     * @return array Массив ID каналов
     */
    private function getChannelsWithPriority(Notification $notification, $recipient, string $role, string $eventType): array
    {
        // 1. Высший приоритет: каналы для конкретного получателя
        $recipientChannels = $this->getChannelsForRecipientFromNotification($notification, $recipient);
        if ($recipientChannels !== null) {
            \Yii::info([
                'message' => 'Каналы получены для конкретного получателя',
                'channels' => $recipientChannels,
                'source' => 'recipient_specific',
                'recipient' => is_object($recipient) && method_exists($recipient, 'getId') ? $recipient->getId() : $recipient
            ], 'notification');

            return $recipientChannels;
        }

        // 2. Приоритет: каналы для роли получателя
        $roleChannels = $this->getChannelsForRoleFromNotification($notification, $role);
        if ($roleChannels !== null) {
            \Yii::info([
                'message' => 'Каналы получены для роли получателя',
                'channels' => $roleChannels,
                'source' => 'role_specific',
                'role' => $role
            ], 'notification');

            return $roleChannels;
        }

        // 3. Fallback: общие каналы из контекста уведомления
        return $this->getChannelsFromNotification($notification, $eventType);
    }

    /**
     * Получает каналы из контекста уведомления с приоритетом
     *
     * @param Notification $notification Уведомление
     * @param string $eventType Тип события
     * @return array Массив ID каналов
     */
    private function getChannelsFromNotification(Notification $notification, string $eventType): array
    {
        // 1. Приоритет: каналы из context уведомления
        if ($notification->hasMetadata('channels')) {
            $contextChannels = $notification->getMetadata('channels');
            if (is_array($contextChannels) && !empty($contextChannels)) {
                \Yii::info([
                    'message' => 'Каналы получены из context уведомления',
                    'channels' => $contextChannels,
                    'source' => 'notification.context'
                ], 'notification');

                return $contextChannels;
            }
        }

        // 2. Приоритет: каналы из source_context уведомления
        $data = $notification->getData();
        if (isset($data['channels']) && is_array($data['channels']) && !empty($data['channels'])) {
            \Yii::info([
                'message' => 'Каналы получены из source_context уведомления',
                'channels' => $data['channels'],
                'source' => 'notification.source_context'
            ], 'notification');

            return $data['channels'];
        }

        // 3. Fallback: каналы по умолчанию для типа события
        $defaultChannels = $this->getDefaultChannelsForEvent($eventType);
        $defaultChannelIds = array_keys($defaultChannels);

        \Yii::info([
            'message' => 'Каналы получены из конфигурации по умолчанию',
            'channels' => $defaultChannelIds,
            'source' => 'default_config',
            'eventType' => $eventType
        ], 'notification');

        return $defaultChannelIds;
    }

    /**
     * Получает каналы по умолчанию для типа события
     *
     * @param string $eventType Тип события
     * @return array Массив каналов по умолчанию
     */
    private function getDefaultChannelsForEvent(string $eventType): array
    {
        // Сначала ищем каналы для конкретного типа события
        if (isset($this->channelConfig[$eventType])) {
            return $this->filterAvailableChannels($this->channelConfig[$eventType]);
        }

        // Если не найдено, ищем каналы для группы типов событий
        foreach ($this->channelConfig as $pattern => $channels) {
            if (strpos($pattern, '*') !== false) {
                $regexPattern = '/^' . str_replace(['*', '\\'], ['.*', '\\\\'], $pattern) . '$/';
                if (preg_match($regexPattern, $eventType)) {
                    return $this->filterAvailableChannels($channels);
                }
            }
        }

        // Если не найдено, используем настройки по умолчанию
        if (isset($this->channelConfig['default'])) {
            return $this->filterAvailableChannels($this->channelConfig['default']);
        }

        // Если ничего не найдено, используем все активные каналы
        $defaultChannels = [];
        foreach ($this->channels as $channelId => $channel) {
            if ($channel->isActive()) {
                $defaultChannels[$channelId] = $channel;
            }
        }

        return $defaultChannels;
    }

    /**
     * Фильтрует доступные каналы по их идентификаторам
     *
     * @param array $channelIds Массив идентификаторов каналов
     * @return array Массив доступных каналов
     */
    private function filterAvailableChannels(array $channelIds): array
    {
        $availableChannels = [];

        foreach ($channelIds as $channelId) {
            if (isset($this->channels[$channelId]) && $this->channels[$channelId]->isActive()) {
                $availableChannels[$channelId] = $this->channels[$channelId];
            }
        }

        return $availableChannels;
    }

    /**
     * Проверяет, доступен ли канал для указанной роли
     *
     * @param string $channelId Идентификатор канала
     * @param string $role Роль пользователя
     * @return bool Результат проверки
     */
    private function isChannelAllowedForRole(string $channelId, string $role): bool
    {
        // Проверяем, есть ли ограничения по ролям для канала
        if (isset($this->channelConfig['role_restrictions'][$channelId])) {
            $allowedRoles = $this->channelConfig['role_restrictions'][$channelId];
            return in_array($role, $allowedRoles);
        }

        // Если ограничений нет, канал доступен всем ролям
        return true;
    }

    /**
     * Получает каналы для конкретной роли из контекста уведомления
     *
     * @param Notification $notification Уведомление
     * @param string $role Роль пользователя
     * @return array|null Массив каналов для роли или null, если не найдено
     */
    public function getChannelsForRoleFromNotification(Notification $notification, string $role): ?array
    {
        // Проверяем каналы по ролям в context
        if ($notification->hasMetadata('channels_by_role')) {
            $channelsByRole = $notification->getMetadata('channels_by_role');
            if (is_array($channelsByRole) && isset($channelsByRole[$role])) {
                return $channelsByRole[$role];
            }
        }

        // Проверяем каналы по ролям в source_context
        $data = $notification->getData();
        if (isset($data['channels_by_role']) && is_array($data['channels_by_role']) && isset($data['channels_by_role'][$role])) {
            return $data['channels_by_role'][$role];
        }

        return null;
    }

    /**
     * Получает каналы для конкретного получателя из контекста уведомления
     *
     * @param Notification $notification Уведомление
     * @param mixed $recipient Получатель
     * @return array|null Массив каналов для получателя или null, если не найдено
     */
    public function getChannelsForRecipientFromNotification(Notification $notification, $recipient): ?array
    {
        $recipientId = is_object($recipient) && method_exists($recipient, 'getId')
            ? $recipient->getId()
            : (string)$recipient;

        // Проверяем каналы по получателям в context
        if ($notification->hasMetadata('channels_by_recipient')) {
            $channelsByRecipient = $notification->getMetadata('channels_by_recipient');
            if (is_array($channelsByRecipient) && isset($channelsByRecipient[$recipientId])) {
                return $channelsByRecipient[$recipientId];
            }
        }

        // Проверяем каналы по получателям в source_context
        $data = $notification->getData();
        if (isset($data['channels_by_recipient']) && is_array($data['channels_by_recipient']) && isset($data['channels_by_recipient'][$recipientId])) {
            return $data['channels_by_recipient'][$recipientId];
        }

        return null;
    }

    /**
     * Устанавливает каналы в контекст уведомления
     *
     * @param Notification $notification Уведомление
     * @param array $channels Массив каналов
     * @param string $type Тип установки ('general', 'by_role', 'by_recipient')
     * @param string|null $key Ключ для типов by_role или by_recipient
     * @return void
     */
    public function setChannelsInNotification(Notification $notification, array $channels, string $type = 'general', ?string $key = null): void
    {
        switch ($type) {
            case 'general':
                $notification->setMetadata('channels', $channels);
                break;

            case 'by_role':
                if ($key !== null) {
                    $channelsByRole = $notification->getMetadata('channels_by_role', []);
                    $channelsByRole[$key] = $channels;
                    $notification->setMetadata('channels_by_role', $channelsByRole);
                }
                break;

            case 'by_recipient':
                if ($key !== null) {
                    $channelsByRecipient = $notification->getMetadata('channels_by_recipient', []);
                    $channelsByRecipient[$key] = $channels;
                    $notification->setMetadata('channels_by_recipient', $channelsByRecipient);
                }
                break;
        }
    }
}
