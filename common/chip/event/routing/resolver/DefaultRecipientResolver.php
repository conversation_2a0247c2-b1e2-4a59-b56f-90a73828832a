<?php

namespace common\chip\event\routing\resolver;

use common\models\notification\Notification;
use Yii;

/**
 * Тестовый резолвер получателей уведомлений.
 *
 * Поддерживает отправку уведомлений как конкретным пользователям, так и группам по ролям.
 */
class DefaultRecipientResolver implements RecipientResolverInterface
{
    /**
     * @var RoleResolverInterface Резолвер ролей
     */
    private $roleResolver;

    /**
     * @var array Дополнительные получатели для всех уведомлений
     */
    private $additionalRecipients = [];

    /**
     * @var array Фиксированные списки получателей для конкретных типов событий
     */
    private $eventTypeRecipients = [];

    /**
     * @var array Роли, которым отправлять все уведомления
     */
    private $alwaysNotifyRoles = [RoleResolverInterface::ROLE_ADMIN];

    /**
     * Конструктор
     *
     * @param RoleResolverInterface $roleResolver Резолвер ролей
     */
    public function __construct(DefaultRoleResolver $roleResolver)
    {
        $this->roleResolver = $roleResolver;
    }

    /**
     * {@inheritdoc}
     */
    public function resolveForNotification(Notification $notification): array
    {
        $recipients = [];
        $eventType = $notification->getEventType();
        $data = $notification->getData();

        // 1. Получатели из метаданных уведомления
        if ($notification->hasMetadata('recipients')) {
            $metadataRecipients = $notification->getMetadata('recipients');
            if (is_array($metadataRecipients)) {
                $recipients = array_merge($recipients, $this->resolveRecipientsList($metadataRecipients));
            }
        }

        // 2. Получатели из данных уведомления
        if (isset($data['recipients'])) {
            $dataRecipients = $data['recipients'];
            if (is_array($dataRecipients)) {
                $recipients = array_merge($recipients, $this->resolveRecipientsList($dataRecipients));
            }
        }

        // 3. Роли из метаданных уведомления
        if ($notification->hasMetadata('recipient_roles')) {
            $metadataRoles = $notification->getMetadata('recipient_roles');
            if (is_array($metadataRoles)) {
                $recipients = array_merge($recipients, $this->resolveRecipientsByRoles($metadataRoles));
            }
        }

        // 4. Роли из данных уведомления
        if (isset($data['recipient_roles'])) {
            $dataRoles = $data['recipient_roles'];
            if (is_array($dataRoles)) {
                $recipients = array_merge($recipients, $this->resolveRecipientsByRoles($dataRoles));
            }
        }

        // 5. Фиксированные получатели для типа события
        if (isset($this->eventTypeRecipients[$eventType])) {
            $recipients = array_merge($recipients, $this->resolveRecipientsList($this->eventTypeRecipients[$eventType]));
        }

        // 6. Дополнительные получатели для всех уведомлений
        $recipients = array_merge($recipients, $this->resolveRecipientsList($this->additionalRecipients));

        // 7. Отправка администраторам и другим ролям в списке alwaysNotifyRoles
        $recipients = array_merge($recipients, $this->resolveRecipientsByRoles($this->alwaysNotifyRoles));

        // 8. В тестовом режиме - для проектов также добавляем участников проекта
        if (strpos($eventType, 'Project') !== false && isset($data['project_id'])) {
            $projectRecipients = $this->resolveForProject($data['project_id']);
            $recipients = array_merge($recipients, $projectRecipients);
        }

        // Удаляем дубликаты (один пользователь может быть в нескольких списках)
        return $this->deduplicateRecipients($recipients);
    }

    /**
     * {@inheritdoc}
     */
    public function resolveForProject(int $projectId): array
    {
        $recipients = [];

        // 1. Получаем автора проекта
        try {
            // Пытаемся получить проект из API или репозитория
            $project = null;

            // Пробуем через ProjectRepository, если он существует
            if (Yii::$app->has('projectRepository')) {
                $project = Yii::$app->projectRepository->findById($projectId);
                if ($project && isset($project->author_id)) {
                    $recipients[] = $project->author_id;
                }
            }
            // Пробуем через модель Project, если предыдущий подход не сработал
            else if (class_exists('\\common\\models\\Project')) {
                $project = \common\models\Projects::findOne($projectId);
                if ($project && isset($project->author_id)) {
                    $recipients[] = $project->author_id;
                }
            }
        } catch (\Exception $e) {
            // Логируем ошибку, но продолжаем
            Yii::warning('Ошибка при получении участников проекта: ' . $e->getMessage(), 'notification');
        }

        // 4. В тестовом режиме добавляем всех администраторов
        $recipients = array_merge($recipients, $this->resolveRecipientsByRoles($this->alwaysNotifyRoles));

        return $this->deduplicateRecipients($recipients);
    }

    /**
     * Преобразовать список получателей в объекты пользователей
     *
     * Поддерживает как ID пользователей, так и объекты пользователей.
     *
     * @param array $recipients Список получателей
     * @return array Массив объектов/ID пользователей
     */
    protected function resolveRecipientsList(array $recipients): array
    {
        return $recipients;
    }

    /**
     * Получить всех пользователей с указанными ролями
     *
     * @param array $roles Списки ролей
     * @return array Массив объектов/ID пользователей
     */
    protected function resolveRecipientsByRoles(array $roles): array
    {
        $recipients = [];

        try {
            // Используем методы резолвера для получения пользователей по ролям
            if (method_exists($this->roleResolver, 'getUsersByRoles')) {
                $userIds = $this->roleResolver->getUsersByRoles($roles);
                $recipients = array_merge($recipients, $userIds);

                $resolverClass = get_class($this->roleResolver);
                Yii::info([
                    'message' => "Найдены пользователи для ролей через {$resolverClass}",
                    'roles' => $roles,
                    'count' => count($userIds),
                    'userIds' => $userIds
                ], 'notification');
            } else {
                // Fallback: используем прямое обращение к authManager
                $recipients = $this->resolveRecipientsByRolesAuthManager($roles);
            }
        } catch (\Exception $e) {
            // Логируем ошибку и используем fallback
            Yii::error('Ошибка при получении пользователей по ролям: ' . $e->getMessage(), 'notification');
            $recipients = $this->resolveRecipientsByRolesFallback($roles);
        }

        return $recipients;
    }

    /**
     * Получить пользователей по ролям через authManager (прямое обращение)
     *
     * @param array $roles Списки ролей
     * @return array Массив ID пользователей
     */
    protected function resolveRecipientsByRolesAuthManager(array $roles): array
    {
        $recipients = [];

        try {
            // Используем Yii::$app->authManager для получения пользователей по ролям
            if (Yii::$app->has('authManager')) {
                $authManager = Yii::$app->authManager;

                foreach ($roles as $roleName) {
                    // Получаем роль из authManager
                    $role = $authManager->getRole($roleName);

                    if ($role !== null) {
                        // Получаем всех пользователей с этой ролью
                        $userIds = $authManager->getUserIdsByRole($roleName);

                        if (!empty($userIds)) {
                            $recipients = array_merge($recipients, $userIds);
                        }

                        Yii::info([
                            'message' => 'Найдены пользователи для роли через authManager',
                            'role' => $roleName,
                            'count' => count($userIds),
                            'userIds' => $userIds
                        ], 'notification');
                    } else {
                        Yii::warning("Роль '{$roleName}' не найдена в authManager", 'notification');
                    }
                }
            } else {
                // Fallback: используем старый метод через модель User
                Yii::warning('authManager не доступен, используем fallback метод', 'notification');
                $recipients = $this->resolveRecipientsByRolesFallback($roles);
            }
        } catch (\Exception $e) {
            // Логируем ошибку и используем fallback
            Yii::error('Ошибка при получении пользователей по ролям через authManager: ' . $e->getMessage(), 'notification');
            $recipients = $this->resolveRecipientsByRolesFallback($roles);
        }

        return $recipients;
    }

    /**
     * Fallback метод для получения пользователей по ролям через модель User
     *
     * @param array $roles Списки ролей
     * @return array Массив ID пользователей
     */
    protected function resolveRecipientsByRolesFallback(array $roles): array
    {
        $recipients = [];

        try {
            // Получаем всех пользователей из БД
            $userClass = class_exists('\\common\\models\\User') ? 'common\\models\\User' : null;

            if ($userClass !== null) {
                foreach ($roles as $role) {
                    $users = $userClass::find()
                        ->where(['role' => $role])
                        ->select(['id'])
                        ->column();

                    if (!empty($users)) {
                        $recipients = array_merge($recipients, $users);
                    }
                }
            }
        } catch (\Exception $e) {
            // Логируем ошибку, но продолжаем
            Yii::warning('Ошибка при получении пользователей по ролям (fallback): ' . $e->getMessage(), 'notification');
        }

        return $recipients;
    }

    /**
     * Удалить дубликаты получателей
     *
     * @param array $recipients Список получателей
     * @return array Список получателей без дубликатов
     */
    protected function deduplicateRecipients(array $recipients): array
    {
        $result = [];
        $ids = [];

        foreach ($recipients as $recipient) {
            $id = is_object($recipient) && method_exists($recipient, 'getId') ? $recipient->getId() : (int)$recipient;

            if (!in_array($id, $ids)) {
                $ids[] = $id;
                $result[] = $recipient;
            }
        }

        return $result;
    }

    /**
     * Установить дополнительных получателей для всех уведомлений
     *
     * @param array $recipients Список получателей
     * @return $this
     */
    public function setAdditionalRecipients(array $recipients): self
    {
        $this->additionalRecipients = $recipients;
        return $this;
    }

    /**
     * Установить получателей для конкретного типа событий
     *
     * @param string $eventType Тип события
     * @param array $recipients Список получателей
     * @return $this
     */
    public function setEventTypeRecipients(string $eventType, array $recipients): self
    {
        $this->eventTypeRecipients[$eventType] = $recipients;
        return $this;
    }

    /**
     * Установить роли, которым всегда отправлять уведомления
     *
     * @param array $roles Списки ролей
     * @return $this
     */
    public function setAlwaysNotifyRoles(array $roles): self
    {
        $this->alwaysNotifyRoles = $roles;
        return $this;
    }
    /**
     * Определяет получателей для файла с указанными ролями
     *
     * @param int $fileId ID файла
     * @param array $roles Массив ролей
     * @return array Массив объектов получателей
     */
    private function resolveForFile(int $fileId, array $roles): array
    {
        $recipients = [];

        // Получаем ID проекта, к которому относится файл
        $projectId = $this->getProjectIdByFile($fileId);

        if ($projectId) {
            foreach ($roles as $role) {
                $usersWithRole = $this->userRepository->findByProjectRole($projectId, $role);
                $recipients = array_merge($recipients, $usersWithRole);
            }
        }

        // Добавляем пользователей, связанных непосредственно с файлом
        foreach ($roles as $role) {
            $usersWithRole = $this->userRepository->findByFileRole($fileId, $role);
            $recipients = array_merge($recipients, $usersWithRole);
        }

        return $this->uniqueRecipients($recipients);
    }

    /**
     * Получает ID проекта по ID файла
     *
     * @param int $fileId ID файла
     * @return int|null ID проекта или null, если не найден
     */
    private function getProjectIdByFile(int $fileId): ?int
    {
        // Здесь должна быть логика получения ID проекта из файла
        // Пример:
        $file = $this->userRepository->getFileById($fileId);
        return $file ? $file->getProjectId() : null;
    }

}
