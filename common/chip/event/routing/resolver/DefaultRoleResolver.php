<?php

namespace common\chip\event\routing\resolver;

use common\models\notification\Notification;
use Yii;

/**
 * Реализация определения ролей получателей по умолчанию
 */
class DefaultRoleResolver implements RoleResolverInterface
{
    private array $roleConfig;

    /**
     * @var array Кэш ролей пользователей
     */
    private array $userRolesCache = [];

    /**
     * @var array Карта ролей authManager на роли системы уведомлений
     */
    private array $roleMap = [
        'administrator' => self::ROLE_ADMIN,
        'admin' => self::ROLE_ADMIN,
        'manager' => self::ROLE_MANAGER,
        'webmaster' => self::ROLE_WEBMASTER,
        'user' => self::ROLE_CLIENT,
        'client' => self::ROLE_CLIENT,
    ];

    public function __construct(array $roleConfig = [])
    {
        $this->roleConfig = $roleConfig;
    }

    /**
     * {@inheritdoc}
     */
    public function resolveRole($user): string
    {
        // Если передан не объект пользователя, а ID
        if (is_numeric($user)) {
            $userId = (int)$user;
            return $this->resolveRoleByUserId($userId);
        }

        // Получаем ID пользователя
        $userId = null;
        if (method_exists($user, 'getId')) {
            $userId = $user->getId();
        } elseif (method_exists($user, 'id')) {
            $userId = $user->id;
        } elseif (property_exists($user, 'id')) {
            $userId = $user->id;
        }

        if ($userId !== null) {
            return $this->resolveRoleByUserId($userId);
        }

        // Fallback: получаем роль из свойства пользователя
        return $this->resolveRoleFallback($user);
    }

    /**
     * Определяет роль пользователя по ID через authManager
     *
     * @param int $userId ID пользователя
     * @return string Роль пользователя
     */
    private function resolveRoleByUserId(int $userId): string
    {
        // Проверяем кэш
        if (isset($this->userRolesCache[$userId])) {
            return $this->userRolesCache[$userId];
        }

        try {
            // Используем authManager для получения ролей
            if (Yii::$app->has('authManager')) {
                $authManager = Yii::$app->authManager;
                $roles = $authManager->getRolesByUser($userId);

                if (!empty($roles)) {
                    // Ищем роль по приоритету
                    foreach (['administrator', 'admin', 'manager', 'webmaster', 'user', 'client'] as $roleKey) {
                        if (isset($roles[$roleKey])) {
                            $notificationRole = $this->roleMap[$roleKey] ?? self::ROLE_CLIENT;
                            $this->userRolesCache[$userId] = $notificationRole;

                            Yii::info([
                                'message' => 'Роль пользователя определена через authManager',
                                'userId' => $userId,
                                'authRole' => $roleKey,
                                'notificationRole' => $notificationRole
                            ], 'notification');

                            return $notificationRole;
                        }
                    }

                    // Если не нашли роль из карты, берем первую доступную
                    $role = reset($roles);
                    $roleName = $role->name;
                    $notificationRole = $this->roleMap[$roleName] ?? self::ROLE_CLIENT;
                    $this->userRolesCache[$userId] = $notificationRole;

                    return $notificationRole;
                }
            }
        } catch (\Exception $e) {
            Yii::error('Ошибка при получении роли пользователя через authManager: ' . $e->getMessage(), 'notification');
        }

        // Fallback: используем роль по умолчанию
        $this->userRolesCache[$userId] = self::ROLE_CLIENT;
        return self::ROLE_CLIENT;
    }

    /**
     * Fallback метод для определения роли через свойства объекта пользователя
     *
     * @param mixed $user Объект пользователя
     * @return string Роль пользователя
     */
    private function resolveRoleFallback($user): string
    {
        // Получаем роль из свойства пользователя
        if (method_exists($user, 'getRole')) {
            $role = $user->getRole();
            return $this->roleMap[$role] ?? $role ?? self::ROLE_CLIENT;
        }

        // Если нет метода getRole, ищем свойство роли
        if (property_exists($user, 'role')) {
            $role = $user->role;
            return $this->roleMap[$role] ?? $role ?? self::ROLE_CLIENT;
        }

        // Если нет явного указания роли, используем client
        return self::ROLE_CLIENT;
    }

    /**
     * {@inheritdoc}
     */
    public function hasRole($user, string $role): bool
    {
        return $this->resolveRole($user) === $role;
    }

    /**
     * {@inheritdoc}
     */
    public function getAllRoles(): array
    {
        return [
            self::ROLE_CLIENT,
            self::ROLE_MANAGER,
            self::ROLE_ADMIN,
            self::ROLE_WEBMASTER,
            self::ROLE_SYSTEM
        ];
    }

    /**
     * Получает роли, которые должны получить уведомление
     *
     * @param Notification $notification Уведомление
     * @return array Массив ролей
     */
    public function getRolesForNotification(Notification $notification): array
    {
        $eventType = $notification->getEventType();
        $sourceType = $this->extractSourceType($eventType);

        // Проверяем конфигурацию для данного типа события
        if (isset($this->roleConfig[$sourceType][$eventType])) {
            return $this->roleConfig[$sourceType][$eventType];
        }

        // Проверяем конфигурацию для группы событий
        if (isset($this->roleConfig[$sourceType]['*'])) {
            return $this->roleConfig[$sourceType]['*'];
        }

        // Возвращаем роли по умолчанию для типа ресурса
        return $this->roleConfig[$sourceType]['default'] ?? [self::ROLE_CLIENT, self::ROLE_MANAGER, self::ROLE_ADMIN];
    }

    /**
     * Получает роли для проекта
     *
     * @param int $projectId ID проекта
     * @return array Массив ролей
     */
    public function getRolesForProject(int $projectId): array
    {
        // Проверяем, есть ли специальная конфигурация для этого проекта
        if (isset($this->roleConfig['projects'][$projectId])) {
            return $this->roleConfig['projects'][$projectId];
        }

        // Возвращаем роли по умолчанию для проектов
        return $this->roleConfig['project']['default'] ?? [self::ROLE_CLIENT, self::ROLE_MANAGER];
    }

    /**
     * Извлекает тип ресурса из типа события
     *
     * @param string $eventType Тип события
     * @return string Тип ресурса
     */
    private function extractSourceType(string $eventType): string
    {
        // ProjectCreatedEvent -> project
        // FileUploadedEvent -> file
        if (strpos($eventType, 'Project') !== false) {
            return 'project';
        }

        if (strpos($eventType, 'File') !== false) {
            return 'file';
        }

        if (strpos($eventType, 'User') !== false) {
            return 'user';
        }

        return 'system';
    }

    /**
     * Получить всех пользователей с указанной ролью
     *
     * @param string $roleName Название роли
     * @return array Массив ID пользователей
     */
    public function getUsersByRole(string $roleName): array
    {
        try {
            if (!Yii::$app->has('authManager')) {
                Yii::warning('authManager не доступен в DefaultRoleResolver', 'notification');
                return [];
            }

            $authManager = Yii::$app->authManager;

            // Получаем роль
            $role = $authManager->getRole($roleName);
            if ($role === null) {
                Yii::warning("Роль '{$roleName}' не найдена в authManager (DefaultRoleResolver)", 'notification');
                return [];
            }

            // Получаем всех пользователей с этой ролью
            $userIds = $authManager->getUserIdsByRole($roleName);

            Yii::info([
                'message' => 'Получены пользователи для роли через DefaultRoleResolver',
                'role' => $roleName,
                'count' => count($userIds),
                'userIds' => $userIds
            ], 'notification');

            return $userIds;
        } catch (\Exception $e) {
            Yii::error('Ошибка при получении пользователей по роли в DefaultRoleResolver: ' . $e->getMessage(), 'notification');
            return [];
        }
    }

    /**
     * Получить всех пользователей с указанными ролями
     *
     * @param array $roleNames Массив названий ролей
     * @return array Массив ID пользователей (без дубликатов)
     */
    public function getUsersByRoles(array $roleNames): array
    {
        $allUserIds = [];

        foreach ($roleNames as $roleName) {
            $userIds = $this->getUsersByRole($roleName);
            $allUserIds = array_merge($allUserIds, $userIds);
        }

        // Удаляем дубликаты и возвращаем
        return array_unique($allUserIds);
    }

    /**
     * Установить карту ролей
     *
     * @param array $roleMap Карта ролей
     * @return $this
     */
    public function setRoleMap(array $roleMap): self
    {
        $this->roleMap = array_merge($this->roleMap, $roleMap);
        return $this;
    }

    /**
     * Получить карту ролей
     *
     * @return array
     */
    public function getRoleMap(): array
    {
        return $this->roleMap;
    }

    /**
     * Очистить кэш ролей
     *
     * @return void
     */
    public function clearCache(): void
    {
        $this->userRolesCache = [];
    }
}
