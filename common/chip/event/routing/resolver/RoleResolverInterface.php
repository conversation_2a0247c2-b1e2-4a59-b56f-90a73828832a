<?php

namespace common\chip\event\routing\resolver;

/**
 * Интерфейс для определения ролей получателей
 */
interface RoleResolverInterface
{
    /**
     * Список возможных ролей
     */
    const ROLE_CLIENT = 'client';
    const ROLE_MANAGER = 'manager';
    const ROLE_ADMIN = 'administrator';
    const ROLE_WEBMASTER = 'webmaster';
    const ROLE_SYSTEM = 'system';
    
    /**
     * Определить роль пользователя
     *
     * @param mixed $user Объект пользователя
     * @return string Роль пользователя
     */
    public function resolveRole($user): string;
    
    /**
     * Проверить, имеет ли пользователь указанную роль
     *
     * @param mixed $user Объект пользователя
     * @param string $role Роль
     * @return bool
     */
    public function hasRole($user, string $role): bool;
    
    /**
     * Получить все возможные роли
     *
     * @return array
     */
    public function getAllRoles(): array;
}
