<?php

namespace common\chip\event\routing\resolver;


use common\models\notification\Notification;

/**
 * Интерфейс для определения получателей уведомлений
 */
interface RecipientResolverInterface
{
    /**
     * Определить получателей для уведомления
     * 
     * @param Notification $notification Уведомление
     * @return array Массив получателей (объекты пользователей или ID)
     */
    public function resolveForNotification(Notification $notification): array;
    
    /**
     * Определить получателей для проекта
     * 
     * @param int $projectId ID проекта
     * @return array Массив получателей (объекты пользователей или ID)
     */
    public function resolveForProject(int $projectId): array;
}
