<?php

namespace common\chip\event\routing;

use common\chip\event\routing\resolver\DefaultChannelSelector;
use common\chip\event\routing\resolver\DefaultRecipientResolver;
use common\chip\event\routing\resolver\DefaultRoleResolver;
use common\chip\event\routing\resolver\RoleResolverInterface;
use common\chip\event\service\AsyncEventService;
use common\chip\externalIntegrations\kess3\Application\Event\DecodingCompletedEvent;
use common\chip\externalIntegrations\kess3\Application\Event\DecodingStartedEvent;
use common\chip\externalIntegrations\kess3\Application\Event\EncodingCompletedEvent;
use common\chip\externalIntegrations\kess3\Application\Event\EncodingStartedEvent;
use common\chip\notification\channel\NotificationChannelInterface;
use common\models\notification\Notification;
use common\models\notification\NotificationDelivery;
use common\models\notification\NotificationChannel;
use common\models\notification\NotificationLog;
use common\chip\event\core\EventInterface;
use common\chip\event\core\OptionsRequestEvent;
use common\chip\notification\events\NoteAddedEvent;
use common\chip\notification\events\ProjectStatusChangedEvent;
use common\chip\notification\events\SystemErrorEvent;
use common\chip\event\core\FileUploadedEvent;
use common\chip\event\core\FileProcessedEvent;
use common\chip\event\core\FileProcessingErrorEvent;
use common\chip\event\core\ProjectCreatedEvent;
use common\chip\event\core\ProjectNoteAddedEvent;


/**
 * Маршрутизатор уведомлений с поддержкой асинхронной доставки
 */
class NotificationRouter
{
    /**
     * Обработчики для типов уведомлений
     *
     * @var array<string, NotificationChannelInterface>
     */
    private array $handlers = [];

    /**
     * @param DefaultRecipientResolver $recipientResolver Сервис для определения получателей
     * @param DefaultRoleResolver $roleResolver Сервис для определения ролей получателей
     * @param DefaultChannelSelector $channelSelector Сервис для выбора каналов доставки
     * @param AsyncEventService|null $asyncService Сервис для асинхронной доставки
     */
    public function __construct(
        private readonly DefaultRecipientResolver $recipientResolver,
        private readonly DefaultRoleResolver $roleResolver,
        private readonly DefaultChannelSelector $channelSelector,
        private readonly ?AsyncEventService $asyncService = null
    ) {
    }

    /**
     * Обрабатывает событие и создает уведомления
     *
     * @param EventInterface $event Событие для обработки
     * @return void
     * @throws \Exception
     */
    public function handleEvent(EventInterface $event): void
    {
        // Создаем запись уведомления в БД
        $notification = $this->createNotificationFromEvent($event);

        // Логируем создание
        $this->logNotificationEvent($notification, 'create');

        // Выполняем маршрутизацию
        $this->route($notification);
    }

    /**
     * Выполняет маршрутизацию уведомления
     *
     * @param Notification $notification Уведомление для маршрутизации
     * @return void
     */
    public function route(Notification $notification): void
    {
        // Определяем получателей уведомления
        $recipients = $this->recipientResolver->resolveForNotification($notification);

        // Для каждого получателя
        foreach ($recipients as $recipient) {
            // Определяем роль получателя
            $role = $this->roleResolver->resolveRole($recipient);

            // Выбираем подходящие каналы доставки
            $channels = $this->channelSelector->selectChannels($notification, $recipient, $role);

            // Для каждого канала
            foreach ($channels as $channel) {

                // Создаем запись доставки в БД
                $deliveryId = $this->createDeliveryRecord($notification, $recipient, $channel->getId());

                // Проверяем, нужно ли доставлять асинхронно
                $shouldDeliverAsync = $this->shouldDeliverAsync($notification, $channel->getId());

                if ($shouldDeliverAsync && $this->asyncService !== null) {
                    // Асинхронная доставка через очередь
                    $job = new \common\chip\notification\Job\ChannelDeliveryJob([
                        'deliveryId' => $deliveryId
                    ]);

                    $priority = $this->getPriorityByImportance($notification->importance);
                    $delay = $this->getDelayByImportance($notification->importance);

                    \Yii::$app->queue
                        ->priority($priority)
                        ->delay($delay)
                        ->push($job);

                    \Yii::info([
                        'message' => 'Уведомление поставлено в очередь для асинхронной доставки',
                        'channel' => $channel->getId(),
                        'recipientId' => is_object($recipient) ? $recipient->id : $recipient,
                        'notificationType' => $notification->type,
                        'deliveryId' => $deliveryId,
                    ], 'notification');
                } else {
                    // Синхронная доставка
                    $adaptedNotification = $channel->adaptNotification($notification, $recipient, $role);
                    if ($adaptedNotification === null) {
                        // Если канал не подходит для этого уведомления, пропускаем его
                        continue;
                    }
                    $channel->send($adaptedNotification, $recipient);

                    // Обновляем статус доставки
                    $this->updateDeliveryStatus($deliveryId, NotificationDelivery::STATUS_DELIVERED);
                }
            }
        }
    }

    /**
     * Проверяет, должно ли уведомление доставляться асинхронно через указанный канал
     *
     * @param Notification $notification Уведомление
     * @param string $channelId Идентификатор канала
     * @return bool true, если уведомление должно доставляться асинхронно
     */
    private function shouldDeliverAsync(Notification $notification, string $channelId): bool
    {
        // Если сервис асинхронной доставки не доступен, всегда доставляем синхронно
        if ($this->asyncService === null) {
            return false;
        }

        return $this->asyncService->shouldDeliverAsync($notification, $channelId);
    }

    /**
     * Регистрирует обработчик для типа уведомлений
     *
     * @param string $eventType Тип события
     * @param NotificationChannelInterface $handler Обработчик
     * @return void
     */
    public function registerHandler(string $eventType, NotificationChannelInterface $handler): void
    {
        $this->handlers[$eventType] = $handler;
    }

    /**
     * Получает канал доставки по идентификатору
     *
     * @param string $channelId Идентификатор канала
     * @return NotificationChannelInterface|null Канал доставки или null, если не найден
     */
    private function getChannelById(string $channelId): ?NotificationChannelInterface
    {
        return \Yii::$container->get("notificationChannel.{$channelId}", false);
    }

    /**
     * Создает запись уведомления в БД из события
     *
     * @param EventInterface $event Событие
     * @return Notification Созданное уведомление
     * @throws \Exception
     */
    private function createNotificationFromEvent(EventInterface $event): Notification
    {
        $notification = new Notification();
        $notification->type = $this->getNotificationType($event);
        $notification->subject = $this->buildSubject($event);
        $notification->content = $this->buildContent($event);
        $notification->context = $event->getContextData();
        $notification->source_type = $this->getSourceType($event);
        $notification->source_id = $this->getSourceId($event);
        $notification->source_context = $this->buildSourceContext($event);
        $notification->importance = $this->getImportance($event);

        if (!$notification->save()) {
            throw new \Exception('Failed to save notification: ' . json_encode($notification->getErrors()));
        }

        return $notification;
    }

    /**
     * Логирует событие уведомления
     *
     * @param Notification $notification Уведомление
     * @param string $eventType Тип события
     * @return void
     */
    private function logNotificationEvent(Notification $notification, string $eventType): void
    {
        // Используем метод addLog из модели для правильного заполнения всех полей
        $notification->addLog($eventType, [
            'router' => 'NotificationRouter',
            'timestamp' => date('Y-m-d H:i:s'),
            'source_type' => $notification->source_type,
            'source_id' => $notification->source_id
        ]);
    }

    /**
     * Создает запись доставки в БД
     *
     * @param Notification $notification Уведомление
     * @param mixed $recipient Получатель
     * @param string $channelId ID канала
     * @return int ID созданной записи доставки
     */
    private function createDeliveryRecord(Notification $notification, $recipient, string $channelId): int
    {
        $channelModel = NotificationChannel::findByCode($channelId);
        if (!$channelModel || !$channelModel->is_active) {
            throw new \Exception("Channel not found or inactive: {$channelId}");
        }

        $delivery = new NotificationDelivery();
        $delivery->notification_id = $notification->id;
        $delivery->channel_id = $channelModel->id;
        $delivery->recipient_type = $this->getRecipientType($recipient);
        $delivery->recipient_id = $this->getRecipientId($recipient);
        $delivery->status = NotificationDelivery::STATUS_PENDING;

        if (!$delivery->save()) {
            throw new \Exception('Failed to save notification delivery: ' . json_encode($delivery->getErrors()));
        }

        return $delivery->id;
    }

    /**
     * Обновляет статус доставки
     *
     * @param int $deliveryId ID записи доставки
     * @param string $status Новый статус
     * @return void
     */
    private function updateDeliveryStatus(int $deliveryId, string $status): void
    {
        $delivery = NotificationDelivery::findOne($deliveryId);
        if ($delivery) {
            $delivery->status = $status;
            if ($status === NotificationDelivery::STATUS_DELIVERED) {
                $delivery->delivered_at = (new \DateTime())->format('Y-m-d H:i:s');
            }
            $delivery->save();
        }
    }

    /**
     * Получает тип получателя
     *
     * @param mixed $recipient Получатель
     * @return string Тип получателя
     */
    private function getRecipientType($recipient): string
    {
        if (is_array($recipient) && isset($recipient['type'])) {
            return $recipient['type'];
        }

        return NotificationDelivery::RECIPIENT_TYPE_USER;
    }

    /**
     * Получает ID получателя
     *
     * @param mixed $recipient Получатель
     * @return string ID получателя
     */
    private function getRecipientId($recipient): string
    {
        if (is_array($recipient) && isset($recipient['id'])) {
            return (string)$recipient['id'];
        }

        if (is_object($recipient) && method_exists($recipient, 'getId')) {
            return (string)$recipient->getId();
        }

        return (string)$recipient;
    }

    /**
     * Получает приоритет по важности
     *
     * @param string $importance Важность
     * @return int Приоритет
     */
    private function getPriorityByImportance(string $importance): int
    {
        return match($importance) {
            Notification::IMPORTANCE_HIGH => 100,
            Notification::IMPORTANCE_NORMAL => 50,
            Notification::IMPORTANCE_LOW => 10,
            default => 50
        };
    }

    /**
     * Получает задержку по важности
     *
     * @param string $importance Важность
     * @return int Задержка в секундах
     */
    private function getDelayByImportance(string $importance): int
    {
        return match($importance) {
            Notification::IMPORTANCE_HIGH => 0,      // Немедленно
            Notification::IMPORTANCE_NORMAL => 30,   // 30 секунд
            Notification::IMPORTANCE_LOW => 300,     // 5 минут
            default => 30
        };
    }

    /**
     * Определяет тип уведомления по событию
     *
     * @param EventInterface $event Событие
     * @return string Тип уведомления
     */
    private function getNotificationType(EventInterface $event): string
    {
        return match(get_class($event)) {
            OptionsRequestEvent::class => 'options_requested',
            NoteAddedEvent::class => 'note_added',
            ProjectStatusChangedEvent::class => 'project_status_changed',
            SystemErrorEvent::class => 'system_error',
            FileUploadedEvent::class => 'file_uploaded',
            FileProcessedEvent::class => 'file_processed',
            FileProcessingErrorEvent::class => 'file_processing_error',
            ProjectCreatedEvent::class => 'project_created',
            ProjectNoteAddedEvent::class => 'project_note_added',
            DecodingStartedEvent::class => 'decoding_started',
            EncodingStartedEvent::class => 'encoding_started',
            DecodingCompletedEvent::class => 'decoding_completed',
            EncodingCompletedEvent::class => 'encoding_completed',
            default => 'notification'
        };
    }

    /**
     * Строит заголовок уведомления
     *
     * @param EventInterface $event Событие
     * @return string Заголовок
     */
    private function buildSubject(EventInterface $event): string
    {
        return match(get_class($event)) {
            OptionsRequestEvent::class => $event->getNoteTitle(),
            NoteAddedEvent::class => "Новая заметка к проекту #{$event->getProjectId()}",
            ProjectStatusChangedEvent::class => "Статус проекта #{$event->getProjectId()} изменен",
            SystemErrorEvent::class => "Системная ошибка: {$event->getErrorType()}",
            FileUploadedEvent::class => "Файл загружен для проекта #{$event->getProjectId()}",
            FileProcessedEvent::class => "Файл обработан для проекта #{$event->getProjectId()}",
            FileProcessingErrorEvent::class => "Ошибка обработки файла для проекта #{$event->getProjectId()}",
            ProjectCreatedEvent::class => $event->getNoticeTitle(),
            ProjectNoteAddedEvent::class => "Добавлена заметка к проекту #{$event->getProjectId()}",
            default => 'Уведомление'
        };
    }

    /**
     * Строит содержимое уведомления
     *
     * @param EventInterface $event Событие
     * @return string Содержимое
     */
    private function buildContent(EventInterface $event): string
    {
        return match(get_class($event)) {
            OptionsRequestEvent::class => $event->getNoteContent(),
            NoteAddedEvent::class => $event->getNoteContent(),
            ProjectStatusChangedEvent::class => "Статус изменен с '{$event->getOldStatus()}' на '{$event->getNewStatus()}'",
            SystemErrorEvent::class => $event->getErrorMessage(),
            FileUploadedEvent::class => "Файл {$event->getFileName()} успешно загружен",
            FileProcessedEvent::class => "Файл успешно обработан",
            FileProcessingErrorEvent::class => "Ошибка при обработке файла: {$event->getErrorMessage()}",
            ProjectCreatedEvent::class => $event->getNoticeContent(),
            ProjectNoteAddedEvent::class => "К проекту добавлена новая заметка",
            default => 'Уведомление'
        };
    }

    /**
     * Определяет тип источника события
     *
     * @param EventInterface $event Событие
     * @return string Тип источника
     */
    private function getSourceType(EventInterface $event): string
    {
        return match(get_class($event)) {
            NoteAddedEvent::class => Notification::SOURCE_TYPE_USER,
            OptionsRequestEvent::class => Notification::SOURCE_TYPE_USER,
            ProjectStatusChangedEvent::class => Notification::SOURCE_TYPE_USER,
            SystemErrorEvent::class => Notification::SOURCE_TYPE_SERVICE,
            FileUploadedEvent::class => Notification::SOURCE_TYPE_USER,
            FileProcessedEvent::class => Notification::SOURCE_TYPE_SERVICE,
            FileProcessingErrorEvent::class => Notification::SOURCE_TYPE_SERVICE,
            ProjectCreatedEvent::class => Notification::SOURCE_TYPE_USER,
            ProjectNoteAddedEvent::class => Notification::SOURCE_TYPE_USER,
            default => Notification::SOURCE_TYPE_SYSTEM
        };
    }

    /**
     * Получает ID источника события
     *
     * @param EventInterface $event Событие
     * @return string|null ID источника
     */
    private function getSourceId(EventInterface $event): ?string
    {
        return match(get_class($event)) {
            OptionsRequestEvent::class => (string)$event->getSourceId(),
            FileUploadedEvent::class => (string)$event->getSourceId(),
            NoteAddedEvent::class => (string)$event->getSourceId(),
            ProjectStatusChangedEvent::class => (string)$event->getChangedBy(),
            SystemErrorEvent::class => $event->getService(),
            FileProcessedEvent::class => 'file_processor',
            FileProcessingErrorEvent::class => 'file_processor',
            ProjectCreatedEvent::class => (string)$event->getSourceId(),
            ProjectNoteAddedEvent::class => (string)$event->getSourceId(),
            default => null
        };
    }

    /**
     * Определяет важность уведомления
     *
     * @param EventInterface $event Событие
     * @return string Важность
     */
    private function getImportance(EventInterface $event): string
    {
        return match(get_class($event)) {
            NoteAddedEvent::class => Notification::IMPORTANCE_NORMAL,
            OptionsRequestEvent::class => Notification::IMPORTANCE_NORMAL,
            ProjectStatusChangedEvent::class => Notification::IMPORTANCE_HIGH,
            SystemErrorEvent::class => $event->getSeverity() === 'critical'
                ? Notification::IMPORTANCE_HIGH
                : Notification::IMPORTANCE_NORMAL,
            FileUploadedEvent::class => Notification::IMPORTANCE_NORMAL,
            FileProcessedEvent::class => Notification::IMPORTANCE_HIGH,
            FileProcessingErrorEvent::class => Notification::IMPORTANCE_HIGH,
            ProjectCreatedEvent::class => Notification::IMPORTANCE_HIGH,
            ProjectNoteAddedEvent::class => Notification::IMPORTANCE_NORMAL,
            default => Notification::IMPORTANCE_NORMAL
        };
    }

    /**
     * Строит контекст источника для роутинга
     *
     * @param EventInterface $event Событие
     * @return array Контекст источника
     */
    private function buildSourceContext(EventInterface $event): array
    {
        $context = $event->getContextData();
        $sourceContext = [];

        // Копируем основные данные события
        $sourceContext = array_merge($sourceContext, $context);

        // Добавляем специфичные для роутинга данные
        $sourceContext = array_merge($sourceContext, $this->extractRoutingData($event));

        return $sourceContext;
    }

    /**
     * Извлекает данные для роутинга из события
     *
     * @param EventInterface $event Событие
     * @return array Данные для роутинга
     */
    private function extractRoutingData(EventInterface $event): array
    {
        $routingData = [];
        $eventClass = get_class($event);
        $contextData = $event->getContextData();

        // Определяем получателей и каналы по типу события
        switch ($eventClass) {
            case ProjectCreatedEvent::class:
                $routingData['project_id'] = $event->getSourceId();
                $routingData['recipient_roles'] = ['admin', 'manager'];
                $routingData['channels'] = ['ui_popup', 'telegram'];

                // Если в контексте есть конкретные получатели, используем их
                if (isset($contextData['recipients'])) {
                    $routingData['recipients'] = $contextData['recipients'];
                }
                break;

            case FileUploadedEvent::class:
                $routingData['project_id'] = $event->getProjectId();
                $routingData['file_id'] = $event->getFileId();
                $routingData['recipient_roles'] = ['admin', 'manager'];
                $routingData['channels'] = ['ui_popup', 'telegram'];

                // Добавляем автора проекта как получателя
                if (isset($contextData['project_author_id'])) {
                    $routingData['recipients'] = [$contextData['project_author_id']];
                }
                break;

            case ProjectNoteAddedEvent::class:
                $routingData['project_id'] = $event->getProjectId();
                $routingData['note_id'] = $event->getNoteId();

                // Определяем получателя по роли пользователя
                if ($event->getUserRole() === 'manager' && $event->getProjectAuthorId()) {
                    $routingData['recipients'] = [$event->getProjectAuthorId()];
                    $routingData['channels'] = ['ui_popup', 'telegram'];
                } else {
                    $routingData['recipient_roles'] = ['admin'];
                    $routingData['channels'] = ['ui_popup'];
                }
                break;

            case NoteAddedEvent::class:
                $routingData['project_id'] = $event->getProjectId();
                $routingData['recipient_roles'] = ['admin', 'manager'];
                $routingData['channels'] = ['ui_popup', 'telegram'];
                break;

            case ProjectStatusChangedEvent::class:
                $routingData['project_id'] = $event->getProjectId();
                $routingData['recipient_roles'] = ['admin', 'manager'];
                $routingData['channels'] = ['ui_popup', 'telegram', 'email'];

                // Добавляем автора проекта
                if (isset($contextData['project_author_id'])) {
                    $routingData['recipients'] = [$contextData['project_author_id']];
                }
                break;

            case SystemErrorEvent::class:
                $routingData['recipient_roles'] = ['admin'];
                $routingData['channels'] = ['ui_popup', 'telegram', 'email'];
                $routingData['error_type'] = $event->getErrorType();
                $routingData['service'] = $event->getService();
                break;

            case FileProcessedEvent::class:
            case FileProcessingErrorEvent::class:
                if (isset($contextData['project_id'])) {
                    $routingData['project_id'] = $contextData['project_id'];
                }
                $routingData['recipient_roles'] = [RoleResolverInterface::ROLE_ADMIN, RoleResolverInterface::ROLE_MANAGER];
                $routingData['channels'] = ['ui_popup', 'telegram'];
                break;

            case OptionsRequestEvent::class:
                $routingData['project_id'] = $event->getSourceId();
                $routingData['recipient_roles'] = [RoleResolverInterface::ROLE_ADMIN, RoleResolverInterface::ROLE_MANAGER];
                $routingData['channels'] = ['ui_popup', 'telegram'];
                break;

            default:
                // Для неизвестных событий используем базовые настройки
                $routingData['recipient_roles'] = [RoleResolverInterface::ROLE_ADMIN];
                $routingData['channels'] = ['ui_popup'];
                break;
        }

        return $routingData;
    }
}
