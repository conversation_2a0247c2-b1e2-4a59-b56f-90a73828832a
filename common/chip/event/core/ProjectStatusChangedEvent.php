<?php

namespace common\chip\event\core;

use common\chip\notification\core\event\BaseEvent;

/**
 * Класс события изменения статуса проекта
 */
class ProjectStatusChangedEvent extends BaseEvent
{
    /**
     * @var string Название события
     */
    const EVENT_NAME = 'project.status_changed';

    /**
     * @var int Предыдущий статус
     */
    private $oldStatus;

    /**
     * @var int Новый статус
     */
    private $newStatus;

    /**
     * Конструктор
     *
     * @param int $projectId Идентификатор проекта
     * @param int $oldStatus Предыдущий статус
     * @param int $newStatus Новый статус
     */
    public function __construct(int $projectId, int $oldStatus, int $newStatus)
    {
        parent::__construct($projectId);
        $this->oldStatus = $oldStatus;
        $this->newStatus = $newStatus;

        $this->setContextData('old_status', $oldStatus);
        $this->setContextData('new_status', $newStatus);
    }

    /**
     * Получить предыдущий статус
     *
     * @return int
     */
    public function getOldStatus(): int
    {
        return $this->oldStatus;
    }

    /**
     * Получить новый статус
     *
     * @return int
     */
    public function getNewStatus(): int
    {
        return $this->newStatus;
    }
}
