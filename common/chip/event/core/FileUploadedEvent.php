<?php

namespace common\chip\event\core;


/**
 * Событие загрузки файла
 */
class FileUploadedEvent extends BaseEvent
{
    /**
     * @var int ID проекта
     */
    private $projectId;
    
    /**
     * @var int ID файла
     */
    private $fileId;
    
    /**
     * @var int|null ID пользователя, загрузившего файл
     */
    private $userId;
    
    /**
     * @var string Имя файла
     */
    private $fileName;
    
    /**
     * @var string|null Роль пользователя
     */
    private $userRole;
    
    /**
     * @var int|null ID автора проекта
     */
    private $projectAuthorId;
    
    /**
     * Конструктор
     * 
     * @param int $projectId ID проекта
     * @param int $fileId ID файла
     * @param int|null $userId ID пользователя
     * @param string $fileName Имя файла
     * @param string|null $userRole Роль пользователя
     * @param int|null $projectAuthorId ID автора проекта
     * @param array $contextData Дополнительные данные
     */
    public function __construct(
        int $projectId,
        int $fileId,
        ?int $userId,
        string $fileName,
        ?string $userRole = null,
        ?int $projectAuthorId = null,
        array $contextData = []
    ) {
        parent::__construct((string)$fileId, $contextData);
        
        $this->projectId = $projectId;
        $this->fileId = $fileId;
        $this->userId = $userId;
        $this->fileName = $fileName;
        $this->userRole = $userRole;
        $this->projectAuthorId = $projectAuthorId;
        
        // Добавляем данные в контекст
        $this->setContextData('project_id', $projectId);
        $this->setContextData('file_id', $fileId);
        $this->setContextData('file_name', $fileName);
        
        if ($userId !== null) {
            $this->setContextData('user_id', $userId);
        }
        
        if ($userRole !== null) {
            $this->setContextData('user_role', $userRole);
        }
        
        if ($projectAuthorId !== null) {
            $this->setContextData('project_author_id', $projectAuthorId);
        }
        foreach ($contextData as $key => $value) {
            $this->setContextData($key, $value);
        }

    }
    
    /**
     * Получить ID проекта
     * 
     * @return int
     */
    public function getProjectId(): int
    {
        return $this->projectId;
    }
    
    /**
     * Получить ID файла
     * 
     * @return int
     */
    public function getFileId(): int
    {
        return $this->fileId;
    }
    
    /**
     * Получить ID пользователя
     * 
     * @return int|null
     */
    public function getUserId(): ?int
    {
        return $this->userId;
    }
    
    /**
     * Получить имя файла
     * 
     * @return string
     */
    public function getFileName(): string
    {
        return $this->fileName;
    }
    
    /**
     * Получить роль пользователя
     * 
     * @return string|null
     */
    public function getUserRole(): ?string
    {
        return $this->userRole;
    }
    
    /**
     * Получить ID автора проекта
     * 
     * @return int|null
     */
    public function getProjectAuthorId(): ?int
    {
        return $this->projectAuthorId;
    }
    
    /**
     * {@inheritdoc}
     */
    public function getDescription(): string
    {
        return "Загрузка файла \"{$this->fileName}\" в проект #{$this->projectId}";
    }

    public function getType(): string
    {
        return 'file.uploaded';
    }
}
