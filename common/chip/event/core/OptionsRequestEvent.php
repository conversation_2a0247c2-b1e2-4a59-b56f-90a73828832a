<?php

namespace common\chip\event\core;

use common\models\ChipAddition;
use common\models\Projects;

/**
 * Событие для запроса настроек проекта
 */
class OptionsRequestEvent extends BaseEvent
{
    /**
     * {@inheritdoc}
     */
    public function getType(): string
    {
        return 'options.requested';
    }
    
    public function getDescription(): string
    {
        return "Запрос настроек проекта #{$this->sourceId}";
    }

    public function getNoteTitle()
    {
        return 'Options request: ';
    }

    public function getNoteContent()
    {
        $projectId = $this->getSourceId();

        $project = Projects::findOne($projectId);

        $optionsRequest = !empty($project->options_request) ? json_decode($project->options_request, true) : [];
        $additionsCatalog = ChipAddition::find()->select('id,title')->indexBy('id')->asArray()->all();
        $additions = $optionsRequest['additions'] ?? [];

        $request = $additions['request'] ?? [];

        if (empty($request)) {
            return '';
        }

        $requestCommentParts = [];

        if (!empty($optionsRequest['requestAdditions'])) {
            foreach ($optionsRequest['requestAdditions'] as $requestAddition) {
                $requestCommentParts[] = trim($additionsCatalog[$requestAddition['addition_id']]['title']);
            }
        }

        if (!empty($request['dtc'])) {
            $requestCommentParts[] = "Request DTC:" . $request['dtc'];
        }

        return implode(', ', $requestCommentParts);
    }

}
