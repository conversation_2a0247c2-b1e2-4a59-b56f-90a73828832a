<?php

namespace common\chip\event\core;

use common\chip\notification\core\event\BaseEvent;

/**
 * Класс события обработки файла Autopack
 */
class FileProcessedEvent extends BaseEvent
{
    /**
     * @var string Название события
     */
    const EVENT_NAME = 'file.processed';

    /**
     * @var int ID исходного файла
     */
    private $sourceFileId;

    /**
     * @var int ID обработанного файла
     */
    private $processedFileId;

    /**
     * Конструктор
     *
     * @param int $projectId Идентификатор проекта
     * @param int $sourceFileId Идентификатор исходного файла
     * @param int $processedFileId Идентификатор обработанного файла
     * @param array $processInfo Информация о процессе обработки
     */
    public function __construct(int $projectId, int $sourceFileId, int $processedFileId, array $processInfo = [])
    {
        parent::__construct($projectId);
        $this->sourceFileId = $sourceFileId;
        $this->processedFileId = $processedFileId;

        $this->setContextData('source_file_id', $sourceFileId);
        $this->setContextData('processed_file_id', $processedFileId);

        if (!empty($processInfo)) {
            foreach ($processInfo as $key => $value) {
                $this->setContextData($key, $value);
            }
        }
    }

    /**
     * Получить ID исходного файла
     *
     * @return int
     */
    public function getSourceFileId(): int
    {
        return $this->sourceFileId;
    }

    /**
     * Получить ID обработанного файла
     *
     * @return int
     */
    public function getProcessedFileId(): int
    {
        return $this->processedFileId;
    }
}