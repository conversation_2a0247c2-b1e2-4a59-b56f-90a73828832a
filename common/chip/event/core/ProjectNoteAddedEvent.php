<?php

namespace common\chip\event\core;

use common\chip\event\routing\resolver\RoleResolverInterface;
use common\models\notification\NotificationChannel;
use common\models\notification\NotificationDelivery;

/**
 * Класс события добавления заметки к проекту
 */
class ProjectNoteAddedEvent extends BaseEvent
{
    /**
     * @var string Название события
     */
    const EVENT_NAME = 'project.note_added';

    /**
     * @var int ID заметки
     */
    private $noteId;

    /**
     * @var int ID пользователя
     */
    private $userId;

    /**
     * @var string Заголовок заметки
     */
    private $title;

    /**
     * @var string Роль пользователя
     */
    private $userRole;

    /**
     * @var int ID автора проекта
     */
    private $projectAuthorId;

    /**
     * Конструктор
     *
     * @param int $projectId Идентификатор проекта
     * @param int $noteId Идентификатор заметки
     * @param int $userId Идентификатор пользователя
     * @param string $title Заголовок заметки
     * @param string $userRole Роль пользователя
     * @param int|null $projectAuthorId ID автора проекта
     * @param array $noteData Данные заметки
     */
    public function __construct(
        int $projectId,
        int $noteId,
        int $userId,
        string $title = '',
        string $userRole = '',
        ?int $projectAuthorId = null,
        array $noteData = []
    ) {
        parent::__construct($projectId);
        $this->noteId = $noteId;
        $this->userId = $userId;
        $this->title = $title;
        $this->userRole = $userRole;
        $this->projectAuthorId = $projectAuthorId;

        // Добавляем данные в контекст события
        $this->setContextData('note_id', $noteId);
        $this->setContextData('user_id', $userId);
        $this->setContextData('title', $title);
        $this->setContextData('user_role', $userRole);

        if ($projectAuthorId !== null) {
            $this->setContextData('project_author_id', $projectAuthorId);
        }

        $this->setContextData('created_at', date('Y-m-d H:i:s'));

        // Добавляем дополнительные данные в контекст
        if (!empty($noteData)) {
            foreach ($noteData as $key => $value) {
                if (!in_array($key, ['note_id', 'user_id', 'title', 'user_role', 'project_author_id', 'created_at'])) {
                    $this->setContextData($key, $value);
                }
            }
        }

        // Автоматически настраиваем получателей
//        $this->setupRecipients();
    }

    /**
     * Получить ID заметки
     *
     * @return int
     */
    public function getNoteId(): int
    {
        return $this->noteId;
    }

    /**
     * Получить ID пользователя
     *
     * @return int
     */
    public function getUserId(): int
    {
        return $this->userId;
    }

    /**
     * Получить заголовок заметки
     *
     * @return string
     */
    public function getTitle(): string
    {
        return $this->title;
    }

    /**
     * Получить роль пользователя
     *
     * @return string
     */
    public function getUserRole(): string
    {
        return $this->userRole;
    }

    /**
     * Получить ID автора проекта
     *
     * @return int|null
     */
    public function getProjectAuthorId(): ?int
    {
        return $this->projectAuthorId;
    }

    /**
     * {@inheritdoc}
     */
    protected function setupRecipients(): void
    {
        // Определяем получателя в зависимости от роли пользователя
        $recipientId = $this->userRole === RoleResolverInterface::ROLE_MANAGER && $this->projectAuthorId !== null
            ? $this->projectAuthorId
            : RoleResolverInterface::ROLE_ADMIN;

        // 1. UI-уведомление для получателя
        $this->addRecipient(
            NotificationChannel::CHANNEL_UI_POPUP,
            $recipientId === RoleResolverInterface::ROLE_ADMIN ? NotificationDelivery::RECIPIENT_TYPE_ROLE : NotificationDelivery::RECIPIENT_TYPE_USER,
            $recipientId,
            [
                'display_location' => 'top-right',
                'show_until_clicked' => false,
            ]
        );

        // 2. Telegram-уведомление для получателя
        if ($recipientId !== RoleResolverInterface::ROLE_ADMIN) {
            $this->addRecipient(
                NotificationChannel::CHANNEL_TELEGRAM,
                NotificationDelivery::RECIPIENT_TYPE_USER,
                $recipientId
            );
        }

        // 3. Нотис проекта
        $this->addRecipient(
            NotificationChannel::CHANNEL_PROJECT_NOTES,
            NotificationDelivery::RECIPIENT_TYPE_PROJECT,
            $this->sourceId,
            [
                'display_location' => 'project_timeline',
            ]
        );
    }

    public function getDescription(): string
    {
        return "Заметка добавлена к проекту #{$this->sourceId}";
    }

    public function getType(): string
    {
        return 'project.note_added';
    }
}
