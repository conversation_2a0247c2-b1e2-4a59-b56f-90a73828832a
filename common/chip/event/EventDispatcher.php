<?php

namespace common\chip\event;

use common\chip\event\core\EventInterface;
use common\chip\event\repositories\EventRepository;
use common\models\notification\Notification;
use Yii;

/**
 * Диспетчер событий - центральный компонент для регистрации и отправки событий
 */
class EventDispatcher
{
    /**
     * @var EventHandler[] Зарегистрированные обработчики событий
     */
    private array $handlers = [];

    /**
     * @var EventRepository|null Репозиторий для сохранения событий
     */
    private ?EventRepository $eventRepository;

    /**
     * @var callable|null Функция для логирования
     */
    private $logger;

    /**
     * @var callable|null Функция для отправки в очередь
     */
    private $queuePusher;

    /**
     * @param EventRepository|null $eventRepository Репозиторий событий
     * @param callable|null $logger Функция для логирования (fn($message, $category) => void)
     * @param callable|null $queuePusher Функция для отправки в очередь (fn($job) => void)
     */
    public function __construct(?EventRepository $eventRepository = null, ?callable $logger = null, ?callable $queuePusher = null)
    {
        $this->eventRepository = $eventRepository;
        
        $this->logger = $logger ?? function ($message, $category) {
            // По умолчанию используем стандартную функцию логирования Yii, если она доступна
            if (class_exists('\Yii') && Yii::$app !== null) {
                Yii::info($message, $category);
            }
        };

        $this->queuePusher = $queuePusher ?? function ($job) {
            // По умолчанию используем стандартную очередь Yii, если она доступна
            if (class_exists('\Yii') && Yii::$app !== null && isset(Yii::$app->queue)) {
                return Yii::$app->queue->push($job);
            }

            // Если очередь недоступна, выполняем задачу сразу
            if (method_exists($job, 'execute')) {
                $job->execute(null);
            }

            return null;
        };
    }

    /**
     * Регистрирует обработчик событий
     * 
     * @param EventHandler $handler Обработчик для регистрации
     * @return self
     */
    public function registerHandler(EventHandler $handler): self
    {
        $this->handlers[] = $handler;
        return $this;
    }

    /**
     * Отправляет событие всем подходящим обработчикам
     * 
     * @param EventInterface $event Событие для отправки
     * @param string|null $entityType Тип сущности (для логирования)
     * @param int|null $entityId ID сущности (для логирования)
     * @param int|null $createdBy ID создателя события
     * @return void
     */
    public function dispatch(EventInterface $event, ?string $entityType = null, ?int $entityId = null, ?int $createdBy = null): void
    {
        call_user_func($this->logger, 
            "Dispatching event: {$event->getType()} with context: " . json_encode($event->getContextData()),
            'event'
        );
        
        // Логируем событие в БД, если указаны тип сущности и ID
        $eventLog = null;
        if ($entityType !== null && $entityId !== null && $this->eventRepository !== null) {
            $eventLog = $this->eventRepository->logEvent($event, $entityType, $entityId, $createdBy);
        }

        foreach ($this->handlers as $handler) {
            if ($handler->canHandle($event)) {
                try {
                    $startTime = microtime(true);
                    
                    // Вызываем обработчик
                    $result = $handler->handle($event);
                    
                    $executionTime = microtime(true) - $startTime;
                    
                    // Логируем успешное выполнение
                    if ($eventLog !== null && $this->eventRepository !== null) {
                        $this->eventRepository->logSuccessfulHandling(
                            $eventLog,
                            get_class($handler),
                            $executionTime
                        );
                        
                        // Если обработчик вернул уведомление, связываем его с событием
                        if ($result instanceof Notification) {
                            $this->eventRepository->linkEventToNotification($eventLog, $result);
                        }
                    }
                } catch (\Throwable $e) {
                    $executionTime = microtime(true) - $startTime;
                    
                    // Логируем ошибку
                    call_user_func($this->logger, 
                        "Error handling event {$event->getType()} by " . get_class($handler) . ": {$e->getMessage()}",
                        'event.error'
                    );
                    
                    // Логируем неудачное выполнение
                    if ($eventLog !== null && $this->eventRepository !== null) {
                        $this->eventRepository->logFailedHandling(
                            $eventLog,
                            get_class($handler),
                            $e->getMessage(),
                            $executionTime
                        );
                    }
                }
            }
        }
        
        // Помечаем событие как обработанное
        if ($eventLog !== null && $this->eventRepository !== null) {
            $this->eventRepository->markEventAsProcessed($eventLog);
        }
    }

    /**
     * Асинхронно отправляет событие через очередь задач
     * 
     * @param EventInterface $event Событие для асинхронной отправки
     * @param string|null $entityType Тип сущности (для логирования)
     * @param int|null $entityId ID сущности (для логирования)
     * @param int|null $createdBy ID создателя события
     * @return mixed Результат постановки в очередь
     */
    public function dispatchAsync(EventInterface $event, ?string $entityType = null, ?int $entityId = null, ?int $createdBy = null)
    {
        // Если указаны тип сущности и ID, сначала сохраняем событие в БД
        $eventLogId = null;
        if ($entityType !== null && $entityId !== null && $this->eventRepository !== null) {
            $eventLog = $this->eventRepository->logEvent($event, $entityType, $entityId, $createdBy);
            if ($eventLog) {
                $eventLogId = $eventLog->id;
            }
        }
        
        $job = new DispatchEventJob([
            'event' => serialize($event),
            'eventLogId' => $eventLogId,
            'entityType' => $entityType,
            'entityId' => $entityId,
            'createdBy' => $createdBy
        ]);
        
        return call_user_func($this->queuePusher, $job);
    }

    /**
     * Устанавливает функцию логирования
     * 
     * @param callable $logger Функция для логирования (fn($message, $category) => void)
     * @return self
     */
    public function setLogger(callable $logger): self
    {
        $this->logger = $logger;
        return $this;
    }

    /**
     * Устанавливает функцию для отправки в очередь
     * 
     * @param callable $queuePusher Функция для отправки в очередь (fn($job) => void)
     * @return self
     */
    public function setQueuePusher(callable $queuePusher): self
    {
        $this->queuePusher = $queuePusher;
        return $this;
    }
    
    /**
     * Устанавливает репозиторий событий
     * 
     * @param EventRepository $eventRepository Репозиторий событий
     * @return self
     */
    public function setEventRepository(EventRepository $eventRepository): self
    {
        $this->eventRepository = $eventRepository;
        return $this;
    }
}
