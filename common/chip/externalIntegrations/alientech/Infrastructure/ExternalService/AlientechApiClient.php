<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Infrastructure\ExternalService;

use common\chip\externalIntegrations\alientech\Application\Decoding\Handler\AlientechApiClientInterface;

/**
 * Клиент для работы с Alientech API
 * Здесь будут HTTP запросы к внешнему сервису
 */
final class AlientechApiClient implements AlientechApiClientInterface
{
    public function __construct(
        private readonly string $apiUrl,
        private readonly string $apiKey
    ) {
    }

    public function startDecoding(string $filePath, string $callbackUrl, array $userInfo): array
    {
        // TODO: Реализовать HTTP запрос для запуска декодирования
        // Пример:
        // $response = $this->httpClient->post($this->apiUrl . '/decode', [
        //     'file_path' => $filePath,
        //     'callback_url' => $callbackUrl,
        //     'user_info' => $userInfo
        // ]);
        
        return [
            'guid' => 'temp_guid_' . uniqid(),
            'slotGUID' => 'temp_slot_' . uniqid(),
        ];
    }

    public function startEncoding(array $filePaths, string $callbackUrl, array $userInfo): array
    {
        // TODO: Реализовать HTTP запрос для запуска кодирования
        // Пример:
        // $response = $this->httpClient->post($this->apiUrl . '/encode', [
        //     'file_paths' => $filePaths,
        //     'callback_url' => $callbackUrl,
        //     'user_info' => $userInfo
        // ]);
        
        return [
            'guid' => 'temp_guid_' . uniqid(),
            'slotGUID' => 'temp_slot_' . uniqid(),
        ];
    }

    public function getOperationStatus(string $externalOperationId): array
    {
        // TODO: Реализовать получение статуса операции
        // Пример:
        // $response = $this->httpClient->get($this->apiUrl . '/status/' . $externalOperationId);
        
        return [
            'status' => 'in_progress',
            'progress' => 50,
        ];
    }

    public function cancelOperation(string $externalOperationId): bool
    {
        // TODO: Реализовать отмену операции
        // Пример:
        // $response = $this->httpClient->delete($this->apiUrl . '/operation/' . $externalOperationId);
        
        return true;
    }
}
