<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Infrastructure\ExternalService;

use common\chip\externalIntegrations\alientech\Application\Decoding\Handler\AlientechApiClientInterface;
use common\chip\alientech\services\AlientechService;
use Yii;

/**
 * Клиент для работы с Alientech API на основе существующего сервиса
 */
final class AlientechApiClient implements AlientechApiClientInterface
{
    public function __construct(
        private readonly AlientechService $alientechService
    ) {
    }

    public function startDecoding(string $filePath, string $callbackUrl, array $userInfo): array
    {
        try {
            Yii::info("AlientechApiClient: Starting decoding for file: {$filePath}", 'alientech.api');
            
            // Используем существующий сервис для запуска декодирования
            $result = $this->alientechService->startDecoding($filePath, $callbackUrl, $userInfo);
            
            Yii::info("AlientechApiClient: Decoding started successfully", 'alientech.api');
            
            return $result;
        } catch (\Exception $e) {
            Yii::error("AlientechApiClient: Failed to start decoding: {$e->getMessage()}", 'alientech.api');
            throw new \RuntimeException("Failed to start decoding: {$e->getMessage()}", 0, $e);
        }
    }

    public function startEncoding(array $filePaths, string $callbackUrl, array $userInfo): array
    {
        try {
            Yii::info("AlientechApiClient: Starting encoding for files: " . implode(', ', $filePaths), 'alientech.api');
            
            // Используем существующий сервис для запуска кодирования
            $result = $this->alientechService->startEncoding($filePaths, $callbackUrl, $userInfo);
            
            Yii::info("AlientechApiClient: Encoding started successfully", 'alientech.api');
            
            return $result;
        } catch (\Exception $e) {
            Yii::error("AlientechApiClient: Failed to start encoding: {$e->getMessage()}", 'alientech.api');
            throw new \RuntimeException("Failed to start encoding: {$e->getMessage()}", 0, $e);
        }
    }

    public function getOperationStatus(string $externalOperationId): array
    {
        try {
            Yii::info("AlientechApiClient: Getting status for operation: {$externalOperationId}", 'alientech.api');
            
            // Используем существующий сервис для получения статуса
            $result = $this->alientechService->getOperationStatus($externalOperationId);
            
            return $result;
        } catch (\Exception $e) {
            Yii::error("AlientechApiClient: Failed to get operation status: {$e->getMessage()}", 'alientech.api');
            throw new \RuntimeException("Failed to get operation status: {$e->getMessage()}", 0, $e);
        }
    }

    public function cancelOperation(string $externalOperationId): bool
    {
        try {
            Yii::info("AlientechApiClient: Cancelling operation: {$externalOperationId}", 'alientech.api');
            
            // Используем существующий сервис для отмены операции
            return $this->alientechService->cancelOperation($externalOperationId);
        } catch (\Exception $e) {
            Yii::error("AlientechApiClient: Failed to cancel operation: {$e->getMessage()}", 'alientech.api');
            return false;
        }
    }
}
