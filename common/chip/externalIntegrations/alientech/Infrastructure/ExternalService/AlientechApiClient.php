<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Infrastructure\ExternalService;

use common\chip\externalIntegrations\kess3\Infrastructure\Http\HttpClientInterface;
use common\chip\externalIntegrations\kess3\Infrastructure\Configuration\ConfigProviderInterface;
use common\chip\externalIntegrations\kess3\Infrastructure\SlotManagement\SlotManagerInterface;
use common\chip\externalIntegrations\kess3\Infrastructure\Dto\ApiRequest;
use common\chip\externalIntegrations\kess3\Domain\Exception\ApiException;
use common\chip\externalIntegrations\kess3\Domain\Exception\SlotLimitException;
use common\chip\alientech\entities\dto\Kess3FileDto;
use Psr\Log\LoggerInterface;
use Yii;

/**
 * Рефакторинг клиента Alientech API с разделением ответственности
 */
final readonly class AlientechApiClient implements AlientechApiClientInterface
{
    private const TOO_MANY_OPEN_KESS3_FILE_SLOTS = '429';

    public function __construct(
        private HttpClientInterface $httpClient,
        private ConfigProviderInterface $config,
        private SlotManagerInterface $slotManager,
        private LoggerInterface $logger
    ) {
        $this->logger->info('AlientechApiClient: Initialized with new architecture');
    }

    public function startDecoding(string $filePath, string $callbackUrl, array $userInfo = []): array
    {
        $this->logger->info('AlientechApiClient: Starting decoding', [
            'filePath' => $filePath,
            'callbackUrl' => $callbackUrl,
            'hasUserInfo' => !empty($userInfo)
        ]);

        try {
            // Проверяем доступность слотов
            if (!$this->slotManager->hasAvailableSlots()) {
                $this->logger->info('AlientechApiClient: No available slots, closing all');
                $this->handleSlotLimitError();
            }

            if (YII_DEBUG) {
                $result = file_get_contents(Yii::getAlias('@storage') . '/payload/alientechOperation5Started.json');
                $responseData = json_decode($result);
            } else {
                // Создаем запрос с новой архитектурой
                $endpoint = $this->config->getApiEndpoint('decode');
                $url = $endpoint . '?callbackURL=' . urlencode($callbackUrl);

                $request = new ApiRequest('POST', $url);
                $request->addFile('readFile', $filePath)
                       ->addData('userInfo', json_encode($userInfo))
                       ->setTimeout($this->config->getTimeout('upload'));

                $response = $this->httpClient->sendRequest($request);

                if (!$response->isSuccessful()) {
                    // Обработка случая превышения лимита слотов
                    if ($response->getStatusCode() === 429) {
                        $this->logger->info('AlientechApiClient: Slot limit exceeded (429), handling');
                        $this->handleSlotLimitError();
                        return $this->startDecoding($filePath, $callbackUrl, $userInfo);
                    }

                    throw new ApiException(
                        'Failed to start decoding: HTTP ' . $response->getStatusCode(),
                        $response->getStatusCode()
                    );
                }

                $responseData = $response->getData();
            }

            if (empty($responseData) || !isset($responseData->guid)) {
                throw new ApiException('Invalid response from Alientech API: missing operation GUID');
            }

            $result = [
                'guid' => $responseData->guid,
                'slotGUID' => $responseData->slotGUID ?? '',
                'clientApplicationGUID' => $responseData->clientApplicationGUID ?? '',
                'asyncOperationType' => $responseData->asyncOperationType ?? 5, // KESS3 Decoding
                'status' => $responseData->status ?? 0, // In Progress
                'isCompleted' => $responseData->isCompleted ?? false,
                'recommendedPollingInterval' => $responseData->recommendedPollingInterval ?? 10,
                'startedOn' => $responseData->startedOn ?? date('Y-m-d\TH:i:s.v\Z'),
            ];

            $this->logger->info('AlientechApiClient: Decoding started successfully', [
                'operationGuid' => $result['guid'],
                'slotGuid' => $result['slotGUID']
            ]);

            return $result;

        } catch (ApiException $e) {
            $this->logger->error('AlientechApiClient: API error starting decoding', [
                'error' => $e->getMessage(),
                'context' => $e->getContext()
            ]);
            throw $e;
        } catch (\Exception $e) {
            $this->logger->error('AlientechApiClient: Unexpected error starting decoding', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new \RuntimeException(
                message: "Failed to start decoding: {$e->getMessage()}",
                previous: $e
            );
        }
    }

    public function getOperationStatus(string $operationGuid): array
    {
        $this->logger->info('AlientechApiClient: Getting operation status', [
            'operationGuid' => $operationGuid
        ]);

        try {
            $endpoint = $this->config->getApiEndpoint('status');
            $url = str_replace('{guid}', $operationGuid, $endpoint);

            $request = new ApiRequest('GET', $url);
            $request->setTimeout($this->config->getTimeout('default'));

            $response = $this->httpClient->sendRequest($request);

            if (!$response->isSuccessful()) {
                throw new ApiException(
                    'Failed to get operation status: HTTP ' . $response->getStatusCode(),
                    $response->getStatusCode()
                );
            }

            $responseData = $response->getData();

            if (empty($responseData)) {
                throw new ApiException('Empty response from Alientech API');
            }

            $result = [
                'guid' => $responseData->guid ?? $operationGuid,
                'status' => $responseData->status ?? 0,
                'isCompleted' => $responseData->isCompleted ?? false,
                'isSuccessful' => $responseData->isSuccessful ?? false,
                'hasFailed' => $responseData->hasFailed ?? false,
                'result' => $responseData->result ?? null,
                'error' => $responseData->error ?? null,
                'completedOn' => $responseData->completedOn ?? null,
                'duration' => $responseData->duration ?? null,
            ];

            $this->logger->info('AlientechApiClient: Operation status retrieved', [
                'operationGuid' => $operationGuid,
                'status' => $result['status'],
                'isCompleted' => $result['isCompleted']
            ]);

            return $result;

        } catch (ApiException $e) {
            $this->logger->error('AlientechApiClient: API error getting operation status', [
                'operationGuid' => $operationGuid,
                'error' => $e->getMessage(),
                'context' => $e->getContext()
            ]);
            throw $e;
        } catch (\Exception $e) {
            $this->logger->error('AlientechApiClient: Unexpected error getting operation status', [
                'operationGuid' => $operationGuid,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new \RuntimeException(
                message: "Failed to get operation status: {$e->getMessage()}",
                previous: $e
            );
        }
    }

    public function downloadFile(string $fileUrl): array
    {
        $this->logger->info('AlientechApiClient: Downloading file', [
            'fileUrl' => $fileUrl
        ]);

        try {
            if (empty($fileUrl)) {
                throw new \InvalidArgumentException('File URL cannot be empty');
            }

            $request = new ApiRequest('GET', $fileUrl);
            $request->setTimeout($this->config->getTimeout('download'));

            $response = $this->httpClient->sendRequest($request);

            // Обработка случая превышения лимита слотов
            if ($response->getStatusCode() === 429) {
                $this->logger->info('AlientechApiClient: Slot limit exceeded during download, handling');
                $this->handleSlotLimitError();
                return $this->downloadFile($fileUrl);
            }

            if (!$response->isSuccessful()) {
                throw new ApiException(
                    'Failed to download file: HTTP ' . $response->getStatusCode(),
                    $response->getStatusCode()
                );
            }

            $responseData = $response->getData();

            if (empty($responseData)) {
                throw new ApiException('Empty response when downloading file');
            }

            $fileDto = new Kess3FileDto($responseData);

            $result = [
                'guid' => $fileDto->getGuid(),
                'fileType' => $fileDto->getFileType(),
                'name' => $fileDto->getName(),
                'data' => $fileDto->getData(),
                'fileSize' => $fileDto->getFileSize(),
                'length' => $fileDto->getLength(),
            ];

            $this->logger->info('AlientechApiClient: File downloaded successfully', [
                'fileGuid' => $result['guid'],
                'fileSize' => $result['fileSize']
            ]);

            return $result;

        } catch (ApiException $e) {
            $this->logger->error('AlientechApiClient: API error downloading file', [
                'fileUrl' => $fileUrl,
                'error' => $e->getMessage(),
                'context' => $e->getContext()
            ]);
            throw $e;
        } catch (\Exception $e) {
            $this->logger->error('AlientechApiClient: Unexpected error downloading file', [
                'fileUrl' => $fileUrl,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new \RuntimeException(
                message: "Failed to download file: {$e->getMessage()}",
                previous: $e
            );
        }
    }

    public function isAvailable(): bool
    {
        $this->logger->info('AlientechApiClient: Checking API availability');

        try {
            // Простая проверка доступности через получение списка слотов
            $request = new ApiRequest('GET', '/api/kess3/file-slots');
            $request->setTimeout($this->config->getTimeout('default'));

            $response = $this->httpClient->sendRequest($request);

            $isAvailable = $response->isSuccessful();

            $this->logger->info('AlientechApiClient: API availability check result', [
                'isAvailable' => $isAvailable,
                'statusCode' => $response->getStatusCode()
            ]);

            return $isAvailable;
        } catch (\Exception $e) {
            $this->logger->warning('AlientechApiClient: API availability check failed', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Обрабатывает ошибку превышения лимита слотов
     */
    private function handleSlotLimitError(): void
    {
        $this->logger->info('AlientechApiClient: Handling slot limit error');

        try {
            $result = $this->slotManager->closeAllSlots();

            $this->logger->info('AlientechApiClient: Slot limit handled', [
                'closedSlots' => $result['closed'],
                'failedSlots' => $result['failed']
            ]);

            if ($result['failed'] > 0) {
                $this->logger->warning('AlientechApiClient: Some slots failed to close', [
                    'failedCount' => $result['failed'],
                    'details' => $result['details']
                ]);
            }

        } catch (\Throwable $e) {
            $this->logger->error('AlientechApiClient: Failed to handle slot limit error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new SlotLimitException(
                $this->slotManager->getMaxSlots(),
                $this->slotManager->getOpenSlotsCount(),
                'Failed to handle slot limit: ' . $e->getMessage()
            );
        }
    }
}
