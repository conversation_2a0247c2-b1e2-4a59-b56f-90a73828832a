<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Infrastructure\ExternalService;

use common\chip\externalIntegrations\alientech\Application\Decoding\Handler\AlientechApiClientInterface;
use common\chip\alientech\services\AlientechService;

/**
 * Клиент для работы с Alientech API на основе существующего сервиса
 */
final class AlientechApiClient implements AlientechApiClientInterface
{
    public function __construct(
        private readonly AlientechService $alientechService
    ) {
    }

    public function startDecoding(string $filePath, string $callbackUrl, array $userInfo): array
    {
        try {
            // Используем существующий сервис для запуска декодирования
            $result = $this->alientechService->startDecoding($filePath, $callbackUrl, $userInfo);
            
            return $result;
        } catch (\Exception $e) {
            throw new \RuntimeException("Failed to start decoding: {$e->getMessage()}", 0, $e);
        }
    }

    public function startEncoding(array $filePaths, string $callbackUrl, array $userInfo): array
    {
        try {
            // Используем существующий сервис для запуска кодирования
            $result = $this->alientechService->startEncoding($filePaths, $callbackUrl, $userInfo);
            
            return $result;
        } catch (\Exception $e) {
            throw new \RuntimeException("Failed to start encoding: {$e->getMessage()}", 0, $e);
        }
    }

    public function getOperationStatus(string $externalOperationId): array
    {
        try {
            // Используем существующий сервис для получения статуса
            $result = $this->alientechService->getOperationStatus($externalOperationId);
            
            return $result;
        } catch (\Exception $e) {
            throw new \RuntimeException("Failed to get operation status: {$e->getMessage()}", 0, $e);
        }
    }

    public function cancelOperation(string $externalOperationId): bool
    {
        try {
            // Используем существующий сервис для отмены операции
            return $this->alientechService->cancelOperation($externalOperationId);
        } catch (\Exception $e) {
            return false;
        }
    }
}
