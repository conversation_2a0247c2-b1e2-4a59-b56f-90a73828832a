<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Infrastructure\Dto;

/**
 * DTO для HTTP ответа от Alientech API
 */
final readonly class ApiResponse
{
    /**
     * @param int $statusCode HTTP статус код
     * @param array<string, string> $headers HTTP заголовки ответа
     * @param mixed $data Декодированные данные ответа
     * @param string $rawContent Сырое содержимое ответа
     */
    public function __construct(
        private int $statusCode,
        private array $headers,
        private mixed $data,
        private string $rawContent
    ) {}
    
    public function getStatusCode(): int
    {
        return $this->statusCode;
    }
    
    public function getHeaders(): array
    {
        return $this->headers;
    }
    
    public function getData(): mixed
    {
        return $this->data;
    }
    
    public function getRawContent(): string
    {
        return $this->rawContent;
    }
    
    /**
     * Проверяет, успешен ли ответ (2xx статус)
     */
    public function isSuccessful(): bool
    {
        return $this->statusCode >= 200 && $this->statusCode < 300;
    }
    
    /**
     * Проверяет, является ли ответ ошибкой клиента (4xx статус)
     */
    public function isClientError(): bool
    {
        return $this->statusCode >= 400 && $this->statusCode < 500;
    }
    
    /**
     * Проверяет, является ли ответ ошибкой сервера (5xx статус)
     */
    public function isServerError(): bool
    {
        return $this->statusCode >= 500;
    }
    
    /**
     * Проверяет, является ли ответ ошибкой аутентификации
     */
    public function isAuthError(): bool
    {
        return $this->statusCode === 401;
    }
    
    /**
     * Возвращает конкретный заголовок
     */
    public function getHeader(string $key): ?string
    {
        return $this->headers[$key] ?? null;
    }
    
    /**
     * Возвращает данные как массив
     */
    public function getDataAsArray(): array
    {
        if (is_array($this->data)) {
            return $this->data;
        }
        
        if (is_object($this->data)) {
            return (array) $this->data;
        }
        
        return [];
    }
    
    /**
     * Возвращает значение из данных по ключу
     */
    public function getDataValue(string $key, mixed $default = null): mixed
    {
        $data = $this->getDataAsArray();
        return $data[$key] ?? $default;
    }
}
