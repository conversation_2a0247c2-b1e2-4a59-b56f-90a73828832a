<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Infrastructure\Dto;

/**
 * DTO для информации о файловом слоте Alientech API
 */
final readonly class SlotInfo
{
    public function __construct(
        private string $guid,
        private bool $isClosed,
        private \DateTimeInterface $createdOn
    ) {}
    
    public function getGuid(): string
    {
        return $this->guid;
    }
    
    public function isClosed(): bool
    {
        return $this->isClosed;
    }
    
    public function isOpen(): bool
    {
        return !$this->isClosed;
    }
    
    public function getCreatedOn(): \DateTimeInterface
    {
        return $this->createdOn;
    }
    
    /**
     * Проверяет, истек ли слот по времени
     */
    public function isExpired(int $minutes): bool
    {
        $now = new \DateTimeImmutable();
        $diff = $now->getTimestamp() - $this->createdOn->getTimestamp();
        return $diff > ($minutes * 60);
    }
    
    /**
     * Возвращает возраст слота в минутах
     */
    public function getAgeInMinutes(): int
    {
        $now = new \DateTimeImmutable();
        $diff = $now->getTimestamp() - $this->createdOn->getTimestamp();
        return (int) ($diff / 60);
    }
    
    /**
     * Возвращает возраст слота в секундах
     */
    public function getAgeInSeconds(): int
    {
        $now = new \DateTimeImmutable();
        return $now->getTimestamp() - $this->createdOn->getTimestamp();
    }
    
    /**
     * Создает SlotInfo из массива данных API
     */
    public static function fromApiData(array $data): self
    {
        $createdOn = isset($data['createdOn']) 
            ? new \DateTimeImmutable($data['createdOn'])
            : new \DateTimeImmutable();
            
        return new self(
            guid: $data['guid'] ?? '',
            isClosed: (bool) ($data['isClosed'] ?? false),
            createdOn: $createdOn
        );
    }
    
    /**
     * Возвращает информацию о слоте в виде массива
     */
    public function toArray(): array
    {
        return [
            'guid' => $this->guid,
            'isClosed' => $this->isClosed,
            'createdOn' => $this->createdOn->format('c'),
            'ageInMinutes' => $this->getAgeInMinutes(),
        ];
    }
}
