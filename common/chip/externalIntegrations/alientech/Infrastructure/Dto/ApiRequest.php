<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Infrastructure\Dto;

/**
 * DTO для HTTP запроса к Alientech API
 */
final class ApiRequest
{
    /**
     * @param string $method HTTP метод (GET, POST, PUT, DELETE)
     * @param string $url URL запроса
     * @param array<string, string> $headers HTTP заголовки
     * @param array<string, mixed> $data Данные запроса
     * @param array<string, string> $files Файлы для загрузки (key => file_path)
     * @param int $timeout Таймаут в секундах
     */
    public function __construct(
        private string $method,
        private string $url,
        private array $headers = [],
        private array $data = [],
        private array $files = [],
        private int $timeout = 30
    ) {}
    
    public function getMethod(): string
    {
        return $this->method;
    }
    
    public function getUrl(): string
    {
        return $this->url;
    }
    
    public function getHeaders(): array
    {
        return $this->headers;
    }
    
    public function getData(): array
    {
        return $this->data;
    }
    
    public function getFiles(): array
    {
        return $this->files;
    }
    
    public function getTimeout(): int
    {
        return $this->timeout;
    }
    
    /**
     * Добавляет HTTP заголовок
     */
    public function addHeader(string $key, string $value): self
    {
        $this->headers[$key] = $value;
        return $this;
    }
    
    /**
     * Добавляет данные к запросу
     */
    public function addData(string $key, mixed $value): self
    {
        $this->data[$key] = $value;
        return $this;
    }
    
    /**
     * Добавляет файл для загрузки
     */
    public function addFile(string $key, string $filePath): self
    {
        $this->files[$key] = $filePath;
        return $this;
    }
    
    /**
     * Устанавливает таймаут
     */
    public function setTimeout(int $timeout): self
    {
        $this->timeout = $timeout;
        return $this;
    }
    
    /**
     * Проверяет, есть ли файлы в запросе
     */
    public function hasFiles(): bool
    {
        return !empty($this->files);
    }
    
    /**
     * Возвращает Content-Type заголовок
     */
    public function getContentType(): string
    {
        return $this->headers['Content-Type'] ?? ($this->hasFiles() ? 'multipart/form-data' : 'application/json');
    }
}
