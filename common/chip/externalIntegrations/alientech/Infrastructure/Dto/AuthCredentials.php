<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Infrastructure\Dto;

/**
 * DTO для учетных данных аутентификации Alientech API
 */
final readonly class AuthCredentials
{
    public function __construct(
        private string $clientApplicationGUID,
        private string $secretKey
    ) {}
    
    public function getClientApplicationGUID(): string
    {
        return $this->clientApplicationGUID;
    }
    
    public function getSecretKey(): string
    {
        return $this->secretKey;
    }
    
    /**
     * Возвращает учетные данные в виде массива для API запроса
     */
    public function toArray(): array
    {
        return [
            'clientApplicationGUID' => $this->clientApplicationGUID,
            'secretKey' => $this->secretKey,
        ];
    }
    
    /**
     * Проверяет валидность учетных данных
     */
    public function isValid(): bool
    {
        return !empty($this->clientApplicationGUID) && !empty($this->secretKey);
    }
    
    /**
     * Возвращает маскированную версию для логирования
     */
    public function toMaskedArray(): array
    {
        return [
            'clientApplicationGUID' => $this->maskGuid($this->clientApplicationGUID),
            'secretKey' => $this->maskSecret($this->secretKey),
        ];
    }
    
    /**
     * Маскирует GUID для безопасного логирования
     */
    private function maskGuid(string $guid): string
    {
        if (strlen($guid) < 8) {
            return str_repeat('*', strlen($guid));
        }
        
        return substr($guid, 0, 4) . str_repeat('*', strlen($guid) - 8) . substr($guid, -4);
    }
    
    /**
     * Маскирует секретный ключ для безопасного логирования
     */
    private function maskSecret(string $secret): string
    {
        if (strlen($secret) < 6) {
            return str_repeat('*', strlen($secret));
        }
        
        return substr($secret, 0, 3) . str_repeat('*', strlen($secret) - 6) . substr($secret, -3);
    }
}
