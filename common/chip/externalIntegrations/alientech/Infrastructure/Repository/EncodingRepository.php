<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Infrastructure\Repository;

use common\chip\externalIntegrations\alientech\Domain\Encoding\Entity\EncodingOperation;
use common\chip\externalIntegrations\alientech\Domain\Encoding\Repository\EncodingRepositoryInterface;
use common\chip\externalIntegrations\alientech\Domain\Shared\FileId;
use common\chip\externalIntegrations\alientech\Domain\Shared\OperationId;
use common\chip\externalIntegrations\alientech\Domain\Shared\ProjectId;
use common\models\AlientechAsyncOperation;

/**
 * Реализация репозитория операций кодирования на основе существующей модели
 */
final class EncodingRepository implements EncodingRepositoryInterface
{
    public function save(EncodingOperation $operation): void
    {
        try {
            $model = $this->findModelByOperationId($operation->getOperationId());
            
            if ($model === null) {
                $model = new AlientechAsyncOperation();
                $model->guid = $operation->getOperationId()->getValue();
                $model->project_id = $operation->getProjectId()->getValue();
                $model->file_id = $operation->getFileId()->getValue();
            }

            $this->updateModelFromEntity($model, $operation);

            if (!$model->save()) {
                throw new \RuntimeException(
                    'Failed to save encoding operation: ' . json_encode($model->errors)
                );
            }
        } catch (\Exception $e) {
            throw $e;
        }
    }

    public function findById(OperationId $operationId): ?EncodingOperation
    {
        $model = $this->findModelByOperationId($operationId);
        return $model ? $this->createEntityFromModel($model) : null;
    }

    public function findByExternalOperationId(string $externalOperationId): ?EncodingOperation
    {
        $model = AlientechAsyncOperation::find()
            ->where(['external_guid' => $externalOperationId])
            ->one();

        return $model ? $this->createEntityFromModel($model) : null;
    }

    public function findByProjectId(ProjectId $projectId): array
    {
        $models = AlientechAsyncOperation::find()
            ->where(['project_id' => $projectId->getValue()])
            ->orderBy(['created_at' => SORT_DESC])
            ->all();

        return array_map([$this, 'createEntityFromModel'], $models);
    }

    public function findByFileId(FileId $fileId): array
    {
        $models = AlientechAsyncOperation::find()
            ->where(['file_id' => $fileId->getValue()])
            ->orderBy(['created_at' => SORT_DESC])
            ->all();

        return array_map([$this, 'createEntityFromModel'], $models);
    }

    public function findInProgress(): array
    {
        $models = AlientechAsyncOperation::find()
            ->where(['status' => 'in_progress'])
            ->orWhere(['isCompleted' => 0])
            ->orderBy(['created_at' => SORT_ASC])
            ->all();

        return array_map([$this, 'createEntityFromModel'], $models);
    }

    public function delete(OperationId $operationId): void
    {
        $model = $this->findModelByOperationId($operationId);
        if ($model !== null) {
            $model->delete();
        }
    }

    private function findModelByOperationId(OperationId $operationId): ?AlientechAsyncOperation
    {
        return AlientechAsyncOperation::find()
            ->where(['guid' => $operationId->getValue()])
            ->one();
    }

    private function createEntityFromModel(AlientechAsyncOperation $model): EncodingOperation
    {
        $filePaths = $model->file_path ? [$model->file_path] : [];
        
        $operation = EncodingOperation::create(
            operationId: OperationId::fromString($model->guid),
            projectId: ProjectId::fromInt($model->project_id),
            fileId: FileId::fromInt($model->file_id),
            filePaths: $filePaths,
            callbackUrl: $model->callback_url ?? '',
            userInfo: $model->userInfo ? json_decode($model->userInfo, true) : []
        );

        // Восстанавливаем состояние операции
        if (!empty($model->external_guid)) {
            $slotGuid = $model->slotGUID ?? '';
            $operation->start($model->external_guid, $slotGuid);
        }

        // Устанавливаем статус и результат
        if ($model->isCompleted) {
            if ($model->isSuccessful && !$model->hasFailed) {
                $result = $model->result ?? [];
                $operation->complete($result ?: []);
            } else {
                $error = $model->error ? json_decode($model->error, true) : [];
                $operation->fail($error ?: ['message' => 'Unknown error']);
            }
        }

        return $operation;
    }

    private function updateModelFromEntity(AlientechAsyncOperation $model, EncodingOperation $operation): void
    {
        $model->guid = $operation->getOperationId()->getValue();
        $model->project_id = $operation->getProjectId()->getValue();
        $model->file_id = $operation->getFileId()->getValue();
        $model->file_path = implode(',', $operation->getFilePaths());
        $model->callback_url = $operation->getCallbackUrl();
        
        $model->external_guid = $operation->getExternalOperationId();
        $model->slotGUID = $operation->getSlotGuid();
        
        $model->status = $operation->getStatus()->getValue();
        $model->isCompleted = (int) $operation->isFinished();
        $model->isSuccessful = (int) $operation->isCompleted();
        $model->hasFailed = (int) $operation->isFailed();

        $model->result = $operation->getResult() ? json_encode($operation->getResult()) : null;
        $model->error = $operation->getError() ? json_encode($operation->getError()) : null;
        $model->userInfo = $operation->getUserInfo() ? json_encode($operation->getUserInfo()) : null;

        if ($operation->getStartedAt()) {
            $model->startedOn = $operation->getStartedAt()->format('Y-m-d H:i:s');
        }
        
        if ($operation->getCompletedAt()) {
            $model->completedOn = $operation->getCompletedAt()->format('Y-m-d H:i:s');
        }

        // Устанавливаем asyncOperationType для Alientech Encoding
        $model->asyncOperationType = 6; // TYPE_KESS3_ENCODING из AsyncOperationDto
    }
}
