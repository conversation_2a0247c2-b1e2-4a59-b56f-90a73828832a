<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Infrastructure\Repository;

use common\chip\externalIntegrations\alientech\Domain\Encoding\Entity\EncodingOperation;
use common\chip\externalIntegrations\alientech\Domain\Encoding\Repository\EncodingRepositoryInterface;
use common\chip\externalIntegrations\alientech\Domain\Shared\FileId;
use common\chip\externalIntegrations\alientech\Domain\Shared\OperationId;
use common\chip\externalIntegrations\alientech\Domain\Shared\ProjectId;

/**
 * Реализация репозитория для операций кодирования
 * Здесь будет работа с базой данных
 */
final class EncodingRepository implements EncodingRepositoryInterface
{
    public function save(EncodingOperation $operation): void
    {
        // TODO: Реализовать сохранение в базу данных
        // Пример: INSERT/UPDATE в таблицу alientech_encoding_operations
    }

    public function findById(OperationId $operationId): ?EncodingOperation
    {
        // TODO: Реализовать поиск по ID
        // Пример: SELECT * FROM alientech_encoding_operations WHERE operation_id = ?
        return null;
    }

    public function findByFileId(FileId $fileId): array
    {
        // TODO: Реализовать поиск по file_id
        // Пример: SELECT * FROM alientech_encoding_operations WHERE file_id = ?
        return [];
    }

    public function findByProjectId(ProjectId $projectId): array
    {
        // TODO: Реализовать поиск по project_id
        // Пример: SELECT * FROM alientech_encoding_operations WHERE project_id = ?
        return [];
    }

    public function findInProgress(): array
    {
        // TODO: Реализовать поиск операций в процессе
        // Пример: SELECT * FROM alientech_encoding_operations WHERE status = 'in_progress'
        return [];
    }

    public function findByExternalOperationId(string $externalOperationId): ?EncodingOperation
    {
        // TODO: Реализовать поиск по external_operation_id
        // Пример: SELECT * FROM alientech_encoding_operations WHERE external_operation_id = ?
        return null;
    }

    public function delete(OperationId $operationId): void
    {
        // TODO: Реализовать удаление
        // Пример: DELETE FROM alientech_encoding_operations WHERE operation_id = ?
    }
}
