<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Infrastructure\Http;

use common\chip\externalIntegrations\alientech\Infrastructure\Dto\ApiRequest;
use common\chip\externalIntegrations\alientech\Infrastructure\Dto\ApiResponse;

/**
 * Интерфейс для HTTP клиента Alientech API
 */
interface HttpClientInterface
{
    /**
     * Отправляет HTTP запрос
     * 
     * @param ApiRequest $request Объект запроса
     * @return ApiResponse Объект ответа
     * 
     * @throws \common\chip\externalIntegrations\alientech\Domain\Exception\ApiException При ошибке API
     * @throws \common\chip\externalIntegrations\alientech\Domain\Exception\AuthenticationException При ошибке аутентификации
     */
    public function sendRequest(ApiRequest $request): ApiResponse;
    
    /**
     * Проверяет, аутентифицирован ли клиент
     * 
     * @return bool
     */
    public function isAuthenticated(): bool;
    
    /**
     * Устанавливает провайдер аутентификации
     * 
     * @param AuthProviderInterface $authProvider
     */
    public function setAuthProvider(AuthProviderInterface $authProvider): void;
    
    /**
     * Устанавливает базовый URL API
     * 
     * @param string $baseUrl
     */
    public function setBaseUrl(string $baseUrl): void;
    
    /**
     * Устанавливает таймаут по умолчанию
     * 
     * @param int $seconds
     */
    public function setDefaultTimeout(int $seconds): void;
}
