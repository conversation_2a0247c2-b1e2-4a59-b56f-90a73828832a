<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Infrastructure\Http;

use common\chip\externalIntegrations\alientech\Infrastructure\Dto\AuthCredentials;

/**
 * Интерфейс для провайдера аутентификации Alientech API
 */
interface AuthProviderInterface
{
    /**
     * Получает действующий токен доступа
     * 
     * @return string Токен доступа
     * 
     * @throws \common\chip\externalIntegrations\alientech\Domain\Exception\AuthenticationException При ошибке получения токена
     */
    public function getToken(): string;
    
    /**
     * Обновляет токен доступа
     * 
     * @return bool Успешность обновления
     * 
     * @throws \common\chip\externalIntegrations\alientech\Domain\Exception\AuthenticationException При ошибке обновления
     */
    public function refreshToken(): bool;
    
    /**
     * Проверяет валидность текущего токена
     * 
     * @return bool
     */
    public function isValid(): bool;
    
    /**
     * Выполняет аутентификацию с учетными данными
     * 
     * @param AuthCredentials $credentials Учетные данные
     * @return bool Успешность аутентификации
     * 
     * @throws \common\chip\externalIntegrations\alientech\Domain\Exception\AuthenticationException При ошибке аутентификации
     */
    public function authenticate(AuthCredentials $credentials): bool;
    
    /**
     * Очищает сохраненный токен
     */
    public function clearToken(): void;
    
    /**
     * Возвращает время истечения токена
     * 
     * @return \DateTimeInterface|null
     */
    public function getTokenExpiry(): ?\DateTimeInterface;
}
