<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Infrastructure\Facade;

use common\chip\externalIntegrations\alientech\Application\Decoding\Command\StartDecodingCommand;
use common\chip\externalIntegrations\alientech\Application\Decoding\Handler\StartDecodingHandler;
use common\chip\externalIntegrations\alientech\Domain\Decoding\Entity\DecodingOperation;

/**
 * Фасад для операций декодирования
 */
final readonly class DecodingFacade
{
    public function __construct(
        private StartDecodingHandler $startHandler
    ) {
    }

    public function startDecoding(int $projectId, int $fileId, string $filePath, ?string $callbackUrl = null): DecodingOperation
    {
        $command = new StartDecodingCommand(
            projectId: $projectId,
            fileId: $fileId,
            filePath: $filePath,
            callbackUrl: $callbackUrl
        );

        return $this->startHandler->handle($command);
    }
}
