<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Infrastructure\Facade;

use common\chip\externalIntegrations\alientech\Application\Encoding\Command\StartEncodingCommand;
use common\chip\externalIntegrations\alientech\Application\Encoding\Handler\StartEncodingHandler;
use common\chip\externalIntegrations\alientech\Domain\Encoding\Entity\EncodingOperation;

/**
 * Фасад для операций кодирования
 */
final readonly class EncodingFacade
{
    public function __construct(
        private StartEncodingHandler $startHandler
    ) {
    }

    public function startEncoding(int $projectId, int $fileId, array $filePaths, ?string $callbackUrl = null): EncodingOperation
    {
        $command = new StartEncodingCommand(
            projectId: $projectId,
            fileId: $fileId,
            filePaths: $filePaths,
            callbackUrl: $callbackUrl
        );

        return $this->startHandler->handle($command);
    }
}
