<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Infrastructure\SlotManagement;

use common\chip\externalIntegrations\alientech\Infrastructure\Dto\SlotInfo;

/**
 * Интерфейс для управления файловыми слотами Alientech API
 */
interface SlotManagerInterface
{
    /**
     * Проверяет наличие доступных слотов
     * 
     * @return bool
     * 
     * @throws \common\chip\externalIntegrations\alientech\Domain\Exception\ApiException При ошибке API
     */
    public function hasAvailableSlots(): bool;
    
    /**
     * Возвращает информацию о всех слотах
     * 
     * @return SlotInfo[] Массив информации о слотах
     * 
     * @throws \common\chip\externalIntegrations\alientech\Domain\Exception\ApiException При ошибке API
     */
    public function getSlots(): array;
    
    /**
     * Закрывает конкретный слот
     * 
     * @param string $slotGuid GUID слота
     * @return bool Успешность закрытия
     * 
     * @throws \common\chip\externalIntegrations\alientech\Domain\Exception\ApiException При ошибке API
     */
    public function closeSlot(string $slotGuid): bool;
    
    /**
     * Закрывает все открытые слоты
     * 
     * @return array{closed: int, failed: int} Результат закрытия
     * 
     * @throws \common\chip\externalIntegrations\alientech\Domain\Exception\ApiException При ошибке API
     */
    public function closeAllSlots(): array;
    
    /**
     * Переоткрывает закрытый слот
     * 
     * @param string $slotGuid GUID слота
     * @return bool Успешность переоткрытия
     * 
     * @throws \common\chip\externalIntegrations\alientech\Domain\Exception\ApiException При ошибке API
     */
    public function reopenSlot(string $slotGuid): bool;
    
    /**
     * Возвращает максимальное количество слотов
     * 
     * @return int
     */
    public function getMaxSlots(): int;
    
    /**
     * Возвращает количество открытых слотов
     * 
     * @return int
     * 
     * @throws \common\chip\externalIntegrations\alientech\Domain\Exception\ApiException При ошибке API
     */
    public function getOpenSlotsCount(): int;
    
    /**
     * Очищает кеш слотов
     */
    public function clearCache(): void;
}
