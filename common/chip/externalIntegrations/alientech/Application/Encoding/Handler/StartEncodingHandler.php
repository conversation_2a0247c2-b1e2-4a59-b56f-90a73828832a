<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Application\Encoding\Handler;

use common\chip\externalIntegrations\alientech\Application\Encoding\Command\StartEncodingCommand;
use common\chip\externalIntegrations\alientech\Application\Decoding\Handler\AlientechApiClientInterface;
use common\chip\externalIntegrations\alientech\Application\Decoding\Handler\EventDispatcherInterface;
use common\chip\externalIntegrations\alientech\Application\Decoding\Handler\LoggerInterface;
use common\chip\externalIntegrations\alientech\Application\Decoding\Handler\UrlGeneratorInterface;
use common\chip\externalIntegrations\alientech\Domain\Encoding\Entity\EncodingOperation;
use common\chip\externalIntegrations\alientech\Domain\Encoding\Repository\EncodingRepositoryInterface;
use common\chip\externalIntegrations\alientech\Domain\Encoding\Service\EncodingDomainService;
use common\chip\externalIntegrations\alientech\Domain\Shared\FileId;
use common\chip\externalIntegrations\alientech\Domain\Shared\ProjectId;

/**
 * Обработчик команды запуска кодирования
 */
final readonly class StartEncodingHandler
{
    public function __construct(
        private EncodingDomainService $domainService,
        private EncodingRepositoryInterface $repository,
        private AlientechApiClientInterface $apiClient,
        private EventDispatcherInterface $eventDispatcher,
        private LoggerInterface $logger,
        private UrlGeneratorInterface $urlGenerator
    ) {
    }

    public function handle(StartEncodingCommand $command): EncodingOperation
    {
        try {
            // Создаем операцию кодирования
            $operation = $this->domainService->createOperation(
                projectId: ProjectId::fromInt($command->projectId),
                fileId: FileId::fromInt($command->fileId),
                filePaths: $command->filePaths,
                callbackUrl: $command->callbackUrl ?: $this->urlGenerator->generateCallbackUrl()
            );

            // Сохраняем операцию
            $this->repository->save($operation);

            $userInfo = [
                'projectId' => $command->projectId,
                'fileId' => $command->fileId,
                'operationId' => $operation->getOperationId()->getValue(),
            ];

            // Отправляем запрос в Alientech API
            $externalResponse = $this->apiClient->startEncoding(
                filePaths: $command->filePaths,
                callbackUrl: $operation->getCallbackUrl(),
                userInfo: $userInfo
            );

            // Обновляем операцию с данными от внешнего API
            $operation->start(
                externalOperationId: $externalResponse['guid'] ?? '',
                slotGuid: $externalResponse['slotGUID'] ?? ''
            );

            $operation->setUserInfo($userInfo);

            // Сохраняем обновленную операцию
            $this->repository->save($operation);

            // Отправляем событие о запуске кодирования
            $event = new EncodingStartedEvent(
                operationId: $operation->getOperationId()->getValue(),
                projectId: $command->projectId,
                fileId: $command->fileId,
                externalOperationId: $operation->getExternalOperationId() ?? '',
                slotGuid: $operation->getSlotGuid() ?? ''
            );

            $this->eventDispatcher->dispatch(
                event: $event,
                entityType: 'alientech_encoding',
                entityId: $command->fileId
            );

            $this->logger->info(
                message: "Encoding started successfully for operation {$operation->getOperationId()}",
                category: 'alientech.encoding'
            );

            return $operation;
        } catch (\Exception $e) {
            $this->logger->error(
                message: "Failed to start encoding: {$e->getMessage()}",
                category: 'alientech.encoding'
            );

            throw new \RuntimeException(
                message: "Failed to start encoding operation: {$e->getMessage()}",
                previous: $e
            );
        }
    }
}

// Event класс
class EncodingStartedEvent
{
    public function __construct(
        public readonly string $operationId,
        public readonly int $projectId,
        public readonly int $fileId,
        public readonly string $externalOperationId,
        public readonly string $slotGuid
    ) {
    }
}
