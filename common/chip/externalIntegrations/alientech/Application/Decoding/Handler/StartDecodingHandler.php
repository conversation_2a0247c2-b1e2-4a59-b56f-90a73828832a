<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Application\Decoding\Handler;

use common\chip\externalIntegrations\alientech\Application\Decoding\Command\StartDecodingCommand;
use common\chip\externalIntegrations\alientech\Domain\Decoding\Entity\DecodingOperation;
use common\chip\externalIntegrations\alientech\Domain\Decoding\Repository\DecodingRepositoryInterface;
use common\chip\externalIntegrations\alientech\Domain\Decoding\Service\DecodingDomainService;
use common\chip\externalIntegrations\alientech\Domain\Shared\FileId;
use common\chip\externalIntegrations\alientech\Domain\Shared\ProjectId;

/**
 * Обработчик команды запуска декодирования
 */
final readonly class StartDecodingHandler
{
    public function __construct(
        private DecodingDomainService $domainService,
        private DecodingRepositoryInterface $repository,
        private AlientechApiClientInterface $apiClient,
        private EventDispatcherInterface $eventDispatcher,
        private LoggerInterface $logger,
        private UrlGeneratorInterface $urlGenerator
    ) {
    }

    public function handle(StartDecodingCommand $command): DecodingOperation
    {
        try {
            // Создаем операцию декодирования
            $operation = $this->domainService->createOperation(
                projectId: ProjectId::fromInt($command->projectId),
                fileId: FileId::fromInt($command->fileId),
                filePath: $command->filePath,
                callbackUrl: $command->callbackUrl ?: $this->urlGenerator->generateCallbackUrl()
            );

            // Сохраняем операцию
            $this->repository->save($operation);

            $userInfo = [
                'projectId' => $command->projectId,
                'fileId' => $command->fileId,
                'operationId' => $operation->getOperationId()->getValue(),
            ];

            // Отправляем запрос в Alientech API
            $externalResponse = $this->apiClient->startDecoding(
                filePath: $command->filePath,
                callbackUrl: $operation->getCallbackUrl(),
                userInfo: $userInfo
            );

            // Обновляем операцию с данными от внешнего API
            $operation->start(
                externalOperationId: $externalResponse['guid'] ?? '',
                slotGuid: $externalResponse['slotGUID'] ?? ''
            );

            $operation->setUserInfo($userInfo);

            // Сохраняем обновленную операцию
            $this->repository->save($operation);

            // Отправляем событие о запуске декодирования
            $event = new DecodingStartedEvent(
                operationId: $operation->getOperationId()->getValue(),
                projectId: $command->projectId,
                fileId: $command->fileId,
                externalOperationId: $operation->getExternalOperationId() ?? '',
                slotGuid: $operation->getSlotGuid() ?? ''
            );

            $this->eventDispatcher->dispatch(
                event: $event,
                entityType: 'alientech_decoding',
                entityId: $command->fileId
            );

            $this->logger->info(
                message: "Decoding started successfully for operation {$operation->getOperationId()}",
                category: 'alientech.decoding'
            );

            return $operation;
        } catch (\Exception $e) {
            $this->logger->error(
                message: "Failed to start decoding: {$e->getMessage()}",
                category: 'alientech.decoding'
            );

            throw new \RuntimeException(
                message: "Failed to start decoding operation: {$e->getMessage()}",
                previous: $e
            );
        }
    }
}

// Интерфейсы, которые должны быть реализованы в Infrastructure слое
interface AlientechApiClientInterface
{
    public function startDecoding(string $filePath, string $callbackUrl, array $userInfo): array;
}

interface EventDispatcherInterface
{
    public function dispatch(object $event, string $entityType, int $entityId): void;
}

interface LoggerInterface
{
    public function info(string $message, string $category): void;
    public function error(string $message, string $category): void;
}

interface UrlGeneratorInterface
{
    public function generateCallbackUrl(): string;
}

// Event класс
class DecodingStartedEvent
{
    public function __construct(
        public readonly string $operationId,
        public readonly int $projectId,
        public readonly int $fileId,
        public readonly string $externalOperationId,
        public readonly string $slotGuid
    ) {
    }
}
