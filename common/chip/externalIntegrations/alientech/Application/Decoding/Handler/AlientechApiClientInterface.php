<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Application\Decoding\Handler;

/**
 * Интерфейс для API клиента Alientech
 */
interface AlientechApiClientInterface
{
    /**
     * Запустить декодирование файла
     */
    public function startDecoding(string $filePath, string $callbackUrl, array $userInfo): array;

    /**
     * Запустить кодирование файлов
     */
    public function startEncoding(array $filePaths, string $callbackUrl, array $userInfo): array;

    /**
     * Получить статус операции
     */
    public function getOperationStatus(string $externalOperationId): array;

    /**
     * Отменить операцию
     */
    public function cancelOperation(string $externalOperationId): bool;
}
