<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Domain\Exception;

/**
 * Исключение для ошибок Alientech API
 */
class ApiException extends \Exception
{
    public function __construct(
        string $message = '',
        int $code = 0,
        ?\Throwable $previous = null,
        private readonly ?array $apiResponse = null
    ) {
        parent::__construct($message, $code, $previous);
    }
    
    /**
     * Возвращает ответ API, если доступен
     */
    public function getApiResponse(): ?array
    {
        return $this->apiResponse;
    }
    
    /**
     * Создает исключение из ответа API
     */
    public static function fromApiResponse(array $response, int $statusCode = 0): self
    {
        $message = $response['message'] ?? $response['error'] ?? 'Unknown API error';
        
        return new self(
            message: $message,
            code: $statusCode,
            apiResponse: $response
        );
    }
}
