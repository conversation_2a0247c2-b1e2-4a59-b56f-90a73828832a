<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Domain\Exception;

/**
 * Исключение для ошибок конфигурации Alientech API
 */
class ConfigurationException extends \Exception
{
    /**
     * Создает исключение для отсутствующего параметра конфигурации
     */
    public static function missingParameter(string $parameter): self
    {
        return new self("Missing configuration parameter: {$parameter}");
    }
    
    /**
     * Создает исключение для неверного значения параметра
     */
    public static function invalidParameter(string $parameter, string $value): self
    {
        return new self("Invalid configuration parameter '{$parameter}': {$value}");
    }
    
    /**
     * Создает исключение для неизвестного endpoint
     */
    public static function unknownEndpoint(string $endpoint): self
    {
        return new self("Unknown API endpoint: {$endpoint}");
    }
}
