<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Domain\Encoding\ValueObject;

/**
 * Value Object для статуса операции кодирования
 */
final readonly class EncodingStatus
{
    private const STATUS_IN_PROGRESS = 'in_progress';
    private const STATUS_COMPLETED = 'completed';
    private const STATUS_FAILED = 'failed';
    private const STATUS_CANCELLED = 'cancelled';

    private const VALID_STATUSES = [
        self::STATUS_IN_PROGRESS,
        self::STATUS_COMPLETED,
        self::STATUS_FAILED,
        self::STATUS_CANCELLED,
    ];

    public function __construct(
        private string $value
    ) {
        if (!in_array($value, self::VALID_STATUSES, true)) {
            throw new \InvalidArgumentException(
                sprintf('Invalid status: %s. Valid statuses: %s', $value, implode(', ', self::VALID_STATUSES))
            );
        }
    }

    public static function inProgress(): self
    {
        return new self(self::STATUS_IN_PROGRESS);
    }

    public static function completed(): self
    {
        return new self(self::STATUS_COMPLETED);
    }

    public static function failed(): self
    {
        return new self(self::STATUS_FAILED);
    }

    public static function cancelled(): self
    {
        return new self(self::STATUS_CANCELLED);
    }

    public function getValue(): string
    {
        return $this->value;
    }

    public function isInProgress(): bool
    {
        return $this->value === self::STATUS_IN_PROGRESS;
    }

    public function isCompleted(): bool
    {
        return $this->value === self::STATUS_COMPLETED;
    }

    public function isFailed(): bool
    {
        return $this->value === self::STATUS_FAILED;
    }

    public function isCancelled(): bool
    {
        return $this->value === self::STATUS_CANCELLED;
    }

    public function isFinished(): bool
    {
        return in_array($this->value, [self::STATUS_COMPLETED, self::STATUS_FAILED, self::STATUS_CANCELLED], true);
    }

    public function equals(EncodingStatus $other): bool
    {
        return $this->value === $other->value;
    }

    public function __toString(): string
    {
        return $this->value;
    }
}
