<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Domain\Encoding\Repository;

use common\chip\externalIntegrations\alientech\Domain\Encoding\Entity\EncodingOperation;
use common\chip\externalIntegrations\alientech\Domain\Shared\FileId;
use common\chip\externalIntegrations\alientech\Domain\Shared\OperationId;
use common\chip\externalIntegrations\alientech\Domain\Shared\ProjectId;

/**
 * Интерфейс репозитория для операций кодирования
 */
interface EncodingRepositoryInterface
{
    /**
     * Сохранить операцию кодирования
     */
    public function save(EncodingOperation $operation): void;

    /**
     * Найти операцию по ID
     */
    public function findById(OperationId $operationId): ?EncodingOperation;

    /**
     * Найти операции по ID файла
     * 
     * @return EncodingOperation[]
     */
    public function findByFileId(FileId $fileId): array;

    /**
     * Найти операции по ID проекта
     * 
     * @return EncodingOperation[]
     */
    public function findByProjectId(ProjectId $projectId): array;

    /**
     * Найти операции в процессе выполнения
     * 
     * @return EncodingOperation[]
     */
    public function findInProgress(): array;

    /**
     * Найти операцию по внешнему ID
     */
    public function findByExternalOperationId(string $externalOperationId): ?EncodingOperation;

    /**
     * Удалить операцию
     */
    public function delete(OperationId $operationId): void;
}
