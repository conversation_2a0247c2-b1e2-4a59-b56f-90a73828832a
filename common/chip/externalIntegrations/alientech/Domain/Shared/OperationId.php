<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Domain\Shared;

/**
 * Value Object для идентификатора операции
 */
final readonly class OperationId
{
    public function __construct(
        private string $value
    ) {
        if (empty($value)) {
            throw new \InvalidArgumentException('Operation ID cannot be empty');
        }
    }

    public static function fromString(string $value): self
    {
        return new self($value);
    }

    public static function generate(): self
    {
        return new self(uniqid('op_', true));
    }

    public function getValue(): string
    {
        return $this->value;
    }

    public function equals(OperationId $other): bool
    {
        return $this->value === $other->value;
    }

    public function __toString(): string
    {
        return $this->value;
    }
}
