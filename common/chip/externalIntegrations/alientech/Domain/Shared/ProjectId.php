<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Domain\Shared;

/**
 * Value Object для идентификатора проекта
 */
final readonly class ProjectId
{
    public function __construct(
        private int $value
    ) {
        if ($value <= 0) {
            throw new \InvalidArgumentException('Project ID must be positive integer');
        }
    }

    public static function fromInt(int $value): self
    {
        return new self($value);
    }

    public function getValue(): int
    {
        return $this->value;
    }

    public function equals(ProjectId $other): bool
    {
        return $this->value === $other->value;
    }

    public function __toString(): string
    {
        return (string) $this->value;
    }
}
