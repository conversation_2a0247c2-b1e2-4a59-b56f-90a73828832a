<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Domain\Shared;

/**
 * Value Object для идентификатора файла
 */
final readonly class FileId
{
    public function __construct(
        private int $value
    ) {
        if ($value <= 0) {
            throw new \InvalidArgumentException('File ID must be positive integer');
        }
    }

    public static function fromInt(int $value): self
    {
        return new self($value);
    }

    public function getValue(): int
    {
        return $this->value;
    }

    public function equals(FileId $other): bool
    {
        return $this->value === $other->value;
    }

    public function __toString(): string
    {
        return (string) $this->value;
    }
}
