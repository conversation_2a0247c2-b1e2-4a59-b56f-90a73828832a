<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Domain\Decoding\Entity;

use common\chip\externalIntegrations\alientech\Domain\Decoding\ValueObject\DecodingStatus;
use common\chip\externalIntegrations\alientech\Domain\Shared\FileId;
use common\chip\externalIntegrations\alientech\Domain\Shared\OperationId;
use common\chip\externalIntegrations\alientech\Domain\Shared\ProjectId;
use DateTimeImmutable;

/**
 * Доменная сущность операции декодирования
 */
final class DecodingOperation
{
    private DecodingStatus $status;
    private ?string $externalOperationId = null;
    private ?string $slotGuid = null;
    private ?array $result = null;
    private ?array $error = null;
    private DateTimeImmutable $createdAt;
    private ?DateTimeImmutable $startedAt = null;
    private ?DateTimeImmutable $completedAt = null;
    private ?array $userInfo = null;

    public function __construct(
        private readonly OperationId $operationId,
        private readonly ProjectId $projectId,
        private readonly FileId $fileId,
        private readonly string $filePath,
        private readonly string $callbackUrl,
        array $userInfo = []
    ) {
        $this->status = DecodingStatus::inProgress();
        $this->createdAt = new DateTimeImmutable();
        $this->userInfo = $userInfo;
    }

    public static function create(
        OperationId $operationId,
        ProjectId $projectId,
        FileId $fileId,
        string $filePath,
        string $callbackUrl,
        array $userInfo = []
    ): self {
        return new self($operationId, $projectId, $fileId, $filePath, $callbackUrl, $userInfo);
    }

    public function start(string $externalOperationId, string $slotGuid): void
    {
        if (!$this->status->isInProgress()) {
            throw new \DomainException('Operation can only be started when in progress');
        }

        $this->externalOperationId = $externalOperationId;
        $this->slotGuid = $slotGuid;
        $this->startedAt = new DateTimeImmutable();
    }

    public function complete(array $result): void
    {
        if (!$this->status->isInProgress()) {
            throw new \DomainException('Operation can only be completed when in progress');
        }

        $this->status = DecodingStatus::completed();
        $this->result = $result;
        $this->completedAt = new DateTimeImmutable();
    }

    public function fail(array $error): void
    {
        if ($this->status->isFinished()) {
            throw new \DomainException('Cannot fail an already finished operation');
        }

        $this->status = DecodingStatus::failed();
        $this->error = $error;
        $this->completedAt = new DateTimeImmutable();
    }

    public function cancel(): void
    {
        if ($this->status->isFinished()) {
            throw new \DomainException('Cannot cancel an already finished operation');
        }

        $this->status = DecodingStatus::cancelled();
        $this->completedAt = new DateTimeImmutable();
    }

    // Getters
    public function getOperationId(): OperationId
    {
        return $this->operationId;
    }

    public function getProjectId(): ProjectId
    {
        return $this->projectId;
    }

    public function getFileId(): FileId
    {
        return $this->fileId;
    }

    public function getFilePath(): string
    {
        return $this->filePath;
    }

    public function getCallbackUrl(): string
    {
        return $this->callbackUrl;
    }

    public function getStatus(): DecodingStatus
    {
        return $this->status;
    }

    public function getExternalOperationId(): ?string
    {
        return $this->externalOperationId;
    }

    public function getSlotGuid(): ?string
    {
        return $this->slotGuid;
    }

    public function getResult(): ?array
    {
        return $this->result;
    }

    public function getError(): ?array
    {
        return $this->error;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getStartedAt(): ?DateTimeImmutable
    {
        return $this->startedAt;
    }

    public function getCompletedAt(): ?DateTimeImmutable
    {
        return $this->completedAt;
    }

    public function isStarted(): bool
    {
        return $this->externalOperationId !== null;
    }

    public function isCompleted(): bool
    {
        return $this->status->isCompleted();
    }

    public function isFailed(): bool
    {
        return $this->status->isFailed();
    }

    public function isFinished(): bool
    {
        return $this->status->isFinished();
    }

    public function setUserInfo(array $userInfo): void
    {
        $this->userInfo = $userInfo;
    }

    public function getUserInfo(): ?array
    {
        return $this->userInfo;
    }
}
