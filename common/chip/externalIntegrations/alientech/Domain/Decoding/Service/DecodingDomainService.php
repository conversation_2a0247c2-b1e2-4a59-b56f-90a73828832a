<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Domain\Decoding\Service;

use common\chip\externalIntegrations\alientech\Domain\Decoding\Entity\DecodingOperation;
use common\chip\externalIntegrations\alientech\Domain\Decoding\Repository\DecodingRepositoryInterface;
use common\chip\externalIntegrations\alientech\Domain\Shared\FileId;
use common\chip\externalIntegrations\alientech\Domain\Shared\OperationId;
use common\chip\externalIntegrations\alientech\Domain\Shared\ProjectId;

/**
 * Доменный сервис для операций декодирования
 */
final readonly class DecodingDomainService
{
    public function __construct(
        private DecodingRepositoryInterface $repository
    ) {
    }

    /**
     * Создать новую операцию декодирования
     */
    public function createOperation(
        ProjectId $projectId,
        FileId $fileId,
        string $filePath,
        string $callbackUrl
    ): DecodingOperation {
        $operationId = OperationId::generate();

        // Проверим, нет ли уже операций декодирования для этого файла в процессе
        $existingOperations = $this->repository->findByFileId($fileId);
        foreach ($existingOperations as $operation) {
            if ($operation->getStatus()->isInProgress()) {
                throw new \DomainException(
                    sprintf('Decoding operation for file %d is already in progress', $fileId->getValue())
                );
            }
        }

        return DecodingOperation::create(
            operationId: $operationId,
            projectId: $projectId,
            fileId: $fileId,
            filePath: $filePath,
            callbackUrl: $callbackUrl
        );
    }

    /**
     * Проверить, можно ли начать операцию декодирования
     */
    public function canStartDecoding(DecodingOperation $operation): bool
    {
        return $operation->getStatus()->isInProgress() && !$operation->isStarted();
    }

    /**
     * Проверить, можно ли завершить операцию декодирования
     */
    public function canCompleteDecoding(DecodingOperation $operation): bool
    {
        return $operation->getStatus()->isInProgress() && $operation->isStarted();
    }

    /**
     * Получить статистику операций по проекту
     */
    public function getProjectStatistics(ProjectId $projectId): array
    {
        $operations = $this->repository->findByProjectId($projectId);

        $statistics = [
            'total' => count($operations),
            'in_progress' => 0,
            'completed' => 0,
            'failed' => 0,
            'cancelled' => 0,
        ];

        foreach ($operations as $operation) {
            $status = $operation->getStatus()->getValue();
            $statistics[$status] = ($statistics[$status] ?? 0) + 1;
        }

        return $statistics;
    }

    /**
     * Найти просроченные операции (в процессе более заданного времени)
     */
    public function findExpiredOperations(int $timeoutMinutes = 30): array
    {
        $operations = $this->repository->findInProgress();
        $expiredOperations = [];
        $timeout = new \DateInterval(sprintf('PT%dM', $timeoutMinutes));

        foreach ($operations as $operation) {
            $startedAt = $operation->getStartedAt() ?? $operation->getCreatedAt();
            $expiredAt = $startedAt->add($timeout);

            if (new \DateTimeImmutable() > $expiredAt) {
                $expiredOperations[] = $operation;
            }
        }

        return $expiredOperations;
    }

    /**
     * Отменить просроченные операции
     */
    public function cancelExpiredOperations(int $timeoutMinutes = 30): int
    {
        $expiredOperations = $this->findExpiredOperations($timeoutMinutes);
        $cancelledCount = 0;

        foreach ($expiredOperations as $operation) {
            try {
                $operation->cancel();
                $this->repository->save($operation);
                $cancelledCount++;
            } catch (\Exception $e) {
                // Логирование должно быть вынесено в Infrastructure слой
                // Здесь только бизнес-логика
            }
        }

        return $cancelledCount;
    }
}
