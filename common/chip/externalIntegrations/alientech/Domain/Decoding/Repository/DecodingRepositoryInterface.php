<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\alientech\Domain\Decoding\Repository;

use common\chip\externalIntegrations\alientech\Domain\Decoding\Entity\DecodingOperation;
use common\chip\externalIntegrations\alientech\Domain\Shared\FileId;
use common\chip\externalIntegrations\alientech\Domain\Shared\OperationId;
use common\chip\externalIntegrations\alientech\Domain\Shared\ProjectId;

/**
 * Интерфейс репозитория для операций декодирования
 */
interface DecodingRepositoryInterface
{
    /**
     * Сохранить операцию декодирования
     */
    public function save(DecodingOperation $operation): void;

    /**
     * Найти операцию по ID
     */
    public function findById(OperationId $operationId): ?DecodingOperation;

    /**
     * Найти операции по ID файла
     * 
     * @return DecodingOperation[]
     */
    public function findByFileId(FileId $fileId): array;

    /**
     * Найти операции по ID проекта
     * 
     * @return DecodingOperation[]
     */
    public function findByProjectId(ProjectId $projectId): array;

    /**
     * Найти операции в процессе выполнения
     * 
     * @return DecodingOperation[]
     */
    public function findInProgress(): array;

    /**
     * Найти операцию по внешнему ID
     */
    public function findByExternalOperationId(string $externalOperationId): ?DecodingOperation;

    /**
     * Удалить операцию
     */
    public function delete(OperationId $operationId): void;
}
