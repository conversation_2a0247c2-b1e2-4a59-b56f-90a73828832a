<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Interfaces;

/**
 * Интерфейс для хранилища файлов
 */
interface FileStorageInterface
{
    /**
     * Сохранить декодированный файл
     */
    public function saveDecodedFile(
        string $fileName,
        string $fileData,
        int $projectId,
        int $fileId,
        array $metadata = []
    ): string;
    
    /**
     * Получить путь к файлу
     */
    public function getFilePath(string $fileName): string;
    
    /**
     * Проверить существование файла
     */
    public function fileExists(string $fileName): bool;
    
    /**
     * Удалить файл
     */
    public function deleteFile(string $fileName): bool;
}
