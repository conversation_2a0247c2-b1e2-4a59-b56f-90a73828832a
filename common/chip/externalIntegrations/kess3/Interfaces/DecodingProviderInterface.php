<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Interfaces;

/**
 * Интерфейс для провайдера внешней интеграции декодинга
 */
interface DecodingProviderInterface
{
    /**
     * Начать процесс декодинга файла
     */
    public function startDecoding(string $filePath, array $userInfo = []): DecodingOperationInterface;
    
    /**
     * Проверить статус операции декодинга
     */
    public function checkOperationStatus(string $operationId): DecodingOperationInterface;
    
    /**
     * Скачать декодированные файлы
     */
    public function downloadDecodedFiles(string $operationId): array;
    
    /**
     * Проверить доступность сервиса
     */
    public function isAvailable(): bool;
}
