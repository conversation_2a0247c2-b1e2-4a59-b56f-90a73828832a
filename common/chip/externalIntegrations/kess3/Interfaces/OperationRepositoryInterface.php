<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Interfaces;

/**
 * Интерфейс для репозитория операций декодинга
 */
interface OperationRepositoryInterface
{
    /**
     * Сохранить операцию декодинга
     */
    public function save(DecodingOperationInterface $operation): void;
    
    /**
     * Найти операцию по ID
     */
    public function findById(string $operationId): ?DecodingOperationInterface;
    
    /**
     * Найти операции по проекту
     */
    public function findByProjectId(int $projectId): array;
    
    /**
     * Найти незавершенные операции
     */
    public function findIncomplete(): array;
}
