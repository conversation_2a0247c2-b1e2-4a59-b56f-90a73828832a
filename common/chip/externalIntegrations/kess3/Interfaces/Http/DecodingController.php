<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Interfaces\Http;

use common\chip\externalIntegrations\kess3\Application\Service\DecodingFacade;
use yii\web\Controller;
use yii\web\Response;
use Yii;

/**
 * HTTP Controller для обработки callback от Alientech API
 */
final class DecodingController extends Controller
{
    private DecodingFacade $decodingFacade;

    /**
     * Отключаем CSRF для callback endpoints
     */
    public function beforeAction($action): bool
    {
        if (in_array($action->id, ['callback', 'status'])) {
            $this->enableCsrfValidation = false;
        }
        $this->decodingFacade = Yii::$container->get(DecodingFacade::class);
        return parent::beforeAction($action);
    }

    /**
     * Обработка callback от Alientech API о завершении декодирования
     */
    public function actionCallback(): Response
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        try {
            $requestData = $this->getRequestData();
            
            if (empty($requestData)) {
                return $this->errorResponse('Empty request data', 400);
            }

            Yii::info(
                message: 'Received decoding callback: ' . json_encode($requestData),
                category: 'kess3.callback'
            );

            $operation = $this->decodingFacade->processDecodingResult($requestData);

            if ($operation === null) {
                return $this->errorResponse('Operation not found', 404);
            }

            return $this->successResponse([
                'status' => 'success',
                'operation_id' => $operation->getOperationId()->getValue(),
                'external_id' => $operation->getExternalOperationId(),
                'operation_status' => $operation->getStatus()->getValue(),
            ]);
        } catch (\Exception $e) {
            Yii::error(
                message: "Error processing callback: {$e->getMessage()}",
                category: 'kess3.callback'
            );

            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Получение статуса операции декодирования
     */
    public function actionStatus(string $operationId = ''): Response
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        try {
            if (empty($operationId)) {
                return $this->errorResponse('Operation ID is required', 400);
            }

            $operation = $this->decodingFacade->getOperation($operationId);

            if ($operation === null) {
                return $this->errorResponse('Operation not found', 404);
            }

            return $this->successResponse([
                'operation_id' => $operation->getOperationId()->getValue(),
                'project_id' => $operation->getProjectId()->getValue(),
                'file_id' => $operation->getFileId()->getValue(),
                'status' => $operation->getStatus()->getValue(),
                'external_operation_id' => $operation->getExternalOperationId(),
                'slot_guid' => $operation->getSlotGuid(),
                'is_completed' => $operation->isCompleted(),
                'is_failed' => $operation->isFailed(),
                'created_at' => $operation->getCreatedAt()->format('Y-m-d H:i:s'),
                'started_at' => $operation->getStartedAt()?->format('Y-m-d H:i:s'),
                'completed_at' => $operation->getCompletedAt()?->format('Y-m-d H:i:s'),
                'result' => $operation->getResult(),
                'error' => $operation->getError(),
            ]);
        } catch (\Exception $e) {
            Yii::error(
                message: "Error getting operation status: {$e->getMessage()}",
                category: 'kess3.callback'
            );

            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Получение всех операций по проекту
     */
    public function actionProjectOperations(int $projectId): Response
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        try {
            $operations = $this->decodingFacade->getProjectOperations($projectId);
            $statistics = $this->decodingFacade->getProjectStatistics($projectId);

            $operationsData = array_map(function ($operation) {
                return [
                    'operation_id' => $operation->getOperationId()->getValue(),
                    'file_id' => $operation->getFileId()->getValue(),
                    'status' => $operation->getStatus()->getValue(),
                    'external_operation_id' => $operation->getExternalOperationId(),
                    'is_completed' => $operation->isCompleted(),
                    'is_failed' => $operation->isFailed(),
                    'created_at' => $operation->getCreatedAt()->format('Y-m-d H:i:s'),
                    'completed_at' => $operation->getCompletedAt()?->format('Y-m-d H:i:s'),
                ];
            }, $operations);

            return $this->successResponse([
                'project_id' => $projectId,
                'statistics' => $statistics,
                'operations' => $operationsData,
            ]);
        } catch (\Exception $e) {
            Yii::error(
                message: "Error getting project operations: {$e->getMessage()}",
                category: 'kess3.callback'
            );

            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Отмена просроченных операций
     */
    public function actionCancelExpired(int $timeoutMinutes = 30): Response
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        try {
            $cancelledCount = $this->decodingFacade->cancelExpiredOperations($timeoutMinutes);

            return $this->successResponse([
                'cancelled_count' => $cancelledCount,
                'timeout_minutes' => $timeoutMinutes,
            ]);
        } catch (\Exception $e) {
            Yii::error(
                message: "Error cancelling expired operations: {$e->getMessage()}",
                category: 'kess3.callback'
            );

            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    private function getRequestData(): array
    {
        $rawBody = Yii::$app->request->getRawBody();
        
        if (!empty($rawBody)) {
            $data = json_decode($rawBody, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                return $data;
            }
        }

        return Yii::$app->request->post();
    }

    private function successResponse(array $data): Response
    {
        return $this->asJson($data);
    }

    private function errorResponse(string $message, int $statusCode = 400): Response
    {
        Yii::$app->response->statusCode = $statusCode;
        
        return $this->asJson([
            'error' => true,
            'message' => $message,
            'status_code' => $statusCode,
        ]);
    }
}
