<?php

declare(strict_types=1);

/**
 * Скрипт для проверки работы рефакторинга AlientechApiClient
 * 
 * Этот скрипт проверяет:
 * 1. Корректность загрузки DI контейнера
 * 2. Создание всех компонентов новой архитектуры
 * 3. Работу AlientechApiClient с новыми зависимостями
 */

require_once __DIR__ . '/../../../../vendor/autoload.php';
require_once __DIR__ . '/../../../../vendor/yiisoft/yii2/Yii.php';

use common\chip\externalIntegrations\kess3\Infrastructure\ExternalService\AlientechApiClient;
use common\chip\externalIntegrations\kess3\Infrastructure\ExternalService\AlientechApiClientInterface;
use common\chip\externalIntegrations\kess3\Infrastructure\Http\HttpClientInterface;
use common\chip\externalIntegrations\kess3\Infrastructure\Configuration\ConfigProviderInterface;
use common\chip\externalIntegrations\kess3\Infrastructure\SlotManagement\SlotManagerInterface;
use Psr\Log\NullLogger;
use yii\caching\ArrayCache;

echo "=== Тест рефакторинга AlientechApiClient ===\n\n";

try {
    // 1. Настройка базовых сервисов
    echo "1. Настройка базовых сервисов...\n";
    
    $logger = new NullLogger();
    $cache = new ArrayCache();
    
    // Создаем мок для базы данных
    $db = new class {
        public function createCommand($sql) {
            return new class {
                public function bindValues($values) { return $this; }
                public function execute() { return true; }
                public function queryScalar() { return 'test-token'; }
            };
        }
    };
    
    Yii::$container->set('logger', $logger);
    Yii::$container->set('cache', $cache);
    Yii::$container->set('db', $db);
    
    echo "✓ Базовые сервисы настроены\n\n";
    
    // 2. Загрузка конфигурации DI контейнера
    echo "2. Загрузка конфигурации DI контейнера...\n";
    
    $containerConfig = require __DIR__ . '/../config/container.php';
    
    // Регистрируем только новые компоненты
    $newComponents = [
        \common\chip\externalIntegrations\kess3\Infrastructure\Configuration\ConfigProviderInterface::class,
        \common\chip\externalIntegrations\kess3\Infrastructure\Configuration\ConfigProvider::class,
        \common\chip\externalIntegrations\kess3\Infrastructure\Http\HttpClientInterface::class,
        \common\chip\externalIntegrations\kess3\Infrastructure\Http\HttpClient::class,
        \common\chip\externalIntegrations\kess3\Infrastructure\Http\AuthProvider::class,
        \common\chip\externalIntegrations\kess3\Infrastructure\SlotManagement\SlotManagerInterface::class,
        \common\chip\externalIntegrations\kess3\Infrastructure\SlotManagement\SlotManager::class,
        AlientechApiClient::class,
    ];
    
    foreach ($containerConfig as $interface => $implementation) {
        if (in_array($interface, $newComponents)) {
            Yii::$container->set($interface, $implementation);
            echo "✓ Зарегистрирован: {$interface}\n";
        }
    }
    
    echo "\n";
    
    // 3. Тестирование создания компонентов
    echo "3. Тестирование создания компонентов...\n";
    
    // Тест ConfigProvider
    echo "   Создание ConfigProvider... ";
    $configProvider = Yii::$container->get(ConfigProviderInterface::class);
    echo "✓\n";
    echo "   - Базовый URL: " . $configProvider->getApiBaseUrl() . "\n";
    echo "   - Customer Code: " . $configProvider->getCustomerCode() . "\n";
    
    // Тест HttpClient
    echo "   Создание HttpClient... ";
    $httpClient = Yii::$container->get(HttpClientInterface::class);
    echo "✓\n";
    echo "   - Аутентификация: " . ($httpClient->isAuthenticated() ? 'Да' : 'Нет') . "\n";
    
    // Тест SlotManager
    echo "   Создание SlotManager... ";
    $slotManager = Yii::$container->get(SlotManagerInterface::class);
    echo "✓\n";
    echo "   - Максимум слотов: " . $slotManager->getMaxSlots() . "\n";
    
    echo "\n";
    
    // 4. Тестирование AlientechApiClient
    echo "4. Тестирование AlientechApiClient...\n";
    
    echo "   Создание AlientechApiClient... ";
    $apiClient = Yii::$container->get(AlientechApiClient::class);
    echo "✓\n";
    
    echo "   Проверка интерфейса... ";
    if ($apiClient instanceof AlientechApiClientInterface) {
        echo "✓\n";
    } else {
        echo "✗ Не реализует интерфейс\n";
    }
    
    echo "   Проверка методов:\n";
    $methods = ['startDecoding', 'getOperationStatus', 'downloadFile', 'isAvailable'];
    foreach ($methods as $method) {
        echo "     - {$method}: ";
        if (method_exists($apiClient, $method)) {
            echo "✓\n";
        } else {
            echo "✗\n";
        }
    }
    
    echo "\n";
    
    // 5. Тест доступности API (без реальных запросов)
    echo "5. Тест доступности API...\n";
    
    try {
        $isAvailable = $apiClient->isAvailable();
        echo "   Результат проверки: " . ($isAvailable ? 'Доступен' : 'Недоступен') . "\n";
    } catch (\Exception $e) {
        echo "   Ошибка проверки: " . $e->getMessage() . "\n";
        echo "   (Это нормально в тестовой среде)\n";
    }
    
    echo "\n";
    
    // 6. Проверка архитектуры
    echo "6. Проверка архитектуры...\n";
    
    $reflection = new \ReflectionClass($apiClient);
    $constructor = $reflection->getConstructor();
    $parameters = $constructor->getParameters();
    
    echo "   Зависимости в конструкторе:\n";
    foreach ($parameters as $param) {
        $type = $param->getType() ? $param->getType()->getName() : 'mixed';
        echo "     - {$param->getName()}: {$type}\n";
    }
    
    echo "\n";
    
    // 7. Сравнение с legacy архитектурой
    echo "7. Сравнение архитектур:\n";
    echo "   LEGACY (до рефакторинга):\n";
    echo "     - AlientechLinkService (HTTP + Auth + Logging)\n";
    echo "     - AlientechService (Configuration)\n";
    echo "     - FileSlotService (Slot Management)\n";
    echo "\n";
    echo "   NEW (после рефакторинга):\n";
    echo "     - HttpClientInterface (только HTTP)\n";
    echo "     - ConfigProviderInterface (только конфигурация)\n";
    echo "     - SlotManagerInterface (только слоты)\n";
    echo "     - LoggerInterface (только логирование)\n";
    echo "\n";
    echo "   Преимущества:\n";
    echo "     ✓ Разделение ответственности\n";
    echo "     ✓ Легкое тестирование\n";
    echo "     ✓ Соответствие SOLID принципам\n";
    echo "     ✓ Обратная совместимость\n";
    
    echo "\n=== ТЕСТ ЗАВЕРШЕН УСПЕШНО ===\n";
    
} catch (\Exception $e) {
    echo "\n✗ ОШИБКА: " . $e->getMessage() . "\n";
    echo "Трассировка:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
