<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Infrastructure\EventHandler;

use common\chip\event\core\EventInterface;
use common\chip\event\EventDispatcher;
use common\chip\event\EventHandler;
use common\chip\externalIntegrations\kess3\Application\Event\EncodingCompletedEvent;
use common\chip\externalIntegrations\kess3\Application\Event\EncodingFailedEvent;
use common\chip\externalIntegrations\kess3\Application\Event\EncodingStartedEvent;

use common\chip\externalIntegrations\kess3\Infrastructure\ExternalService\AlientechApiClient;
use common\models\ProjectNote;
use common\models\Projects;
use Yii;

/**
 * Обработчик событий энкодирования
 */
final class EncodingEventHandler implements EventHandler
{

    public function __construct(
        private AlientechApiClient $apiClient,
        private EventDispatcher $eventDispatcher
    ) {
    }

    public function canHandle(EventInterface $event): bool
    {
        return $event instanceof EncodingStartedEvent
            || $event instanceof EncodingCompletedEvent
            || $event instanceof EncodingFailedEvent;
    }

    public function handle(EventInterface $event): void
    {
        if ($event instanceof EncodingStartedEvent) {
            $this->handleEncodingStarted($event);
        } elseif ($event instanceof EncodingCompletedEvent) {
            $this->handleEncodingCompleted($event);
        } elseif ($event instanceof EncodingFailedEvent) {
            $this->handleEncodingFailed($event);
        }
    }

    private function handleEncodingStarted(EncodingStartedEvent $event): void
    {
        try {
            // Создание системной заметки о начале энкодирования
            $this->createProjectNote(
                projectId: $event->getProjectId(),
                noteText: sprintf(
                    'Энкодирование запущено (Сервис: %s, Файлов: %d, Операция: %s)',
                    $event->getService(),
                    count($event->getFileIds()),
                    $event->getOperationId()
                ),
                noteType: 'system'
            );

            Yii::info(
                message: "Encoding started event processed for operation {$event->getOperationId()}",
                category: 'kess3.encoding.events'
            );
        } catch (\Exception $e) {
            Yii::error(
                message: "Failed to process encoding started event: {$e->getMessage()}",
                category: 'kess3.encoding.events'
            );
        }
    }

    private function handleEncodingCompleted(EncodingCompletedEvent $event): void
    {
        try {
            // Скачивание закодированных файлов
            $this->downloadEncodedFiles($event);

            // Создание системной заметки об успешном завершении
            $this->createProjectNote(
                projectId: $event->getProjectId(),
                noteText: sprintf(
                    'Энкодирование успешно завершено (Файлов обработано: %d, Операция: %s)',
                    count($event->getFileIds()),
                    $event->getOperationId()
                ),
                noteType: 'success'
            );

            // Обновление статуса проекта
            $this->updateProjectStatus($event->getProjectId(), 'encoded');

            Yii::info(
                message: "Encoding completed event processed for operation {$event->getOperationId()}",
                category: 'kess3.encoding.events'
            );
        } catch (\Exception $e) {
            Yii::error(
                message: "Failed to process encoding completed event: {$e->getMessage()}",
                category: 'kess3.encoding.events'
            );
        }
    }

    private function handleEncodingFailed(EncodingFailedEvent $event): void
    {
        try {
            // Создание системной заметки об ошибке
            $this->createProjectNote(
                projectId: $event->getProjectId(),
                noteText: sprintf(
                    'Ошибка энкодирования: %s (Операция: %s)',
                    $event->getErrorMessage(),
                    $event->getOperationId()
                ),
                noteType: 'error'
            );

            // Обновление статуса проекта
            $this->updateProjectStatus($event->getProjectId(), 'encoding_failed');

            Yii::error(
                message: "Encoding failed for operation {$event->getOperationId()}: {$event->getErrorMessage()}",
                category: 'kess3.encoding.events'
            );
        } catch (\Exception $e) {
            Yii::error(
                message: "Failed to process encoding failed event: {$e->getMessage()}",
                category: 'kess3.encoding.events'
            );
        }
    }

    private function downloadEncodedFiles(EncodingCompletedEvent $event): void
    {
        // Логика скачивания закодированных файлов
        // Аналогично декодингу, но для закодированных файлов
        $result = $event->getResult();
        
        if (!empty($result['encodedFileUrls'])) {
            foreach ($result['encodedFileUrls'] as $fileType => $fileUrl) {
                try {
                    $this->downloadAndSaveEncodedFile(
                        projectId: $event->getProjectId(),
                        fileType: $fileType,
                        fileUrl: $fileUrl,
                        operationId: $event->getOperationId()
                    );
                } catch (\Exception $e) {
                    Yii::error(
                        message: "Failed to download encoded file {$fileType}: {$e->getMessage()}",
                        category: 'kess3.encoding.events'
                    );
                }
            }
        }
    }

    private function downloadAndSaveEncodedFile(
        int $projectId,
        string $fileType,
        string $fileUrl,
        string $operationId
    ): void {
        // Здесь будет логика скачивания и сохранения файла
        // Аналогично DecodingEventHandler, но для закодированных файлов
        Yii::info(
            message: "Downloaded encoded file {$fileType} for project {$projectId}, operation {$operationId}",
            category: 'kess3.encoding.events'
        );
    }

    private function createProjectNote(int $projectId, string $noteText, string $noteType = 'system'): void
    {
        $note = new ProjectNote();
        $note->project_id = $projectId;
        $note->note_text = $noteText;
        $note->note_type = $noteType;
        $note->created_on = date('Y-m-d H:i:s');
        
        if (!$note->save()) {
            Yii::warning(
                message: "Failed to create project note: " . json_encode($note->errors),
                category: 'kess3.encoding.events'
            );
        }
    }

    private function updateProjectStatus(int $projectId, string $status): void
    {
        $project = Projects::findOne($projectId);
        if ($project) {
            $project->status = $status;
            $project->save();
        }
    }

}
