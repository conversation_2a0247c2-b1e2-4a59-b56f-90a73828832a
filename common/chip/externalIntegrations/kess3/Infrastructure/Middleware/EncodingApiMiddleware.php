<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Infrastructure\Middleware;

use yii\base\ActionFilter;
use yii\web\Request;
use yii\web\Response;
use yii\web\BadRequestHttpException;
use yii\web\TooManyRequestsHttpException;
use Yii;

/**
 * Middleware для API энкодирования
 * 
 * Обеспечивает:
 * - Логирование запросов
 * - Rate limiting
 * - Валидацию Content-Type
 * - Мониторинг производительности
 */
final class EncodingApiMiddleware extends ActionFilter
{
    /**
     * Максимальное количество запросов в минуту
     */
    public int $rateLimit = 60;

    /**
     * Время окна для rate limiting (секунды)
     */
    public int $rateLimitWindow = 60;

    /**
     * Требовать Content-Type: application/json для POST запросов
     */
    public bool $requireJsonContentType = true;

    /**
     * Максимальный размер тела запроса (байты)
     */
    public int $maxRequestSize = 1024 * 1024; // 1MB

    public function beforeAction($action): bool
    {
        $request = Yii::$app->request;
        $response = Yii::$app->response;
        
        try {
            // Логирование входящего запроса
            $this->logRequest($request, $action);

            // Проверка rate limiting
            $this->checkRateLimit($request);

            // Валидация Content-Type для POST запросов
            if ($this->requireJsonContentType) {
                $this->validateContentType($request);
            }

            // Проверка размера запроса
            $this->validateRequestSize($request);

            // Установка начального времени для замера производительности
            $request->beginTime = microtime(true);

            return parent::beforeAction($action);
        } catch (\Exception $e) {
            Yii::error(
                message: "Encoding API middleware error: {$e->getMessage()}",
                category: 'kess3.encoding.api.middleware'
            );
            throw $e;
        }
    }

    public function afterAction($action, $result)
    {
        $request = Yii::$app->request;
        $response = Yii::$app->response;

        try {
            // Вычисление времени выполнения
            $executionTime = microtime(true) - ($request->beginTime ?? microtime(true));

            // Логирование ответа
            $this->logResponse($request, $response, $action, $executionTime);

            // Добавление заголовков CORS если нужно
            $this->addCorsHeaders($response);

            // Добавление заголовков производительности
            $this->addPerformanceHeaders($response, $executionTime);

        } catch (\Exception $e) {
            Yii::error(
                message: "Encoding API middleware error in afterAction: {$e->getMessage()}",
                category: 'kess3.encoding.api.middleware'
            );
        }

        return parent::afterAction($action, $result);
    }

    /**
     * Логирование входящего запроса
     */
    private function logRequest(Request $request, $action): void
    {
        $logData = [
            'method' => $request->method,
            'url' => $request->url,
            'action' => $action->id,
            'userAgent' => $request->userAgent,
            'ip' => $request->userIP,
            'contentLength' => $request->contentLength,
        ];

        // Логируем параметры запроса (без чувствительных данных)
        if ($request->isPost) {
            $bodyParams = $request->bodyParams;
            // Удаляем чувствительные данные перед логированием
            unset($bodyParams['authToken'], $bodyParams['apiKey']);
            $logData['bodyParams'] = $bodyParams;
        } else {
            $logData['queryParams'] = $request->queryParams;
        }

        Yii::info(
            message: "Encoding API request: " . json_encode($logData),
            category: 'kess3.encoding.api.request'
        );
    }

    /**
     * Логирование ответа
     */
    private function logResponse(Request $request, Response $response, $action, float $executionTime): void
    {
        $logData = [
            'method' => $request->method,
            'url' => $request->url,
            'action' => $action->id,
            'statusCode' => $response->statusCode,
            'executionTime' => round($executionTime * 1000, 2), // ms
            'memoryUsage' => memory_get_peak_usage(true),
        ];

        $category = $response->isSuccessful 
            ? 'kess3.encoding.api.response'
            : 'kess3.encoding.api.error';

        Yii::info(
            message: "Encoding API response: " . json_encode($logData),
            category: $category
        );
    }

    /**
     * Проверка rate limiting
     */
    private function checkRateLimit(Request $request): void
    {
        $cacheKey = 'encoding_api_rate_limit:' . $request->userIP;
        $cache = Yii::$app->cache;
        
        $requestCount = $cache->get($cacheKey) ?: 0;
        
        if ($requestCount >= $this->rateLimit) {
            throw new TooManyRequestsHttpException(
                "Rate limit exceeded. Maximum {$this->rateLimit} requests per {$this->rateLimitWindow} seconds."
            );
        }
        
        $cache->set($cacheKey, $requestCount + 1, $this->rateLimitWindow);
    }

    /**
     * Валидация Content-Type
     */
    private function validateContentType(Request $request): void
    {
        if (!$request->isPost && !$request->isPut && !$request->isPatch) {
            return;
        }

        $contentType = $request->contentType;
        
        if ($contentType !== 'application/json' && !str_starts_with($contentType, 'application/json;')) {
            throw new BadRequestHttpException(
                'Content-Type must be application/json for POST/PUT/PATCH requests'
            );
        }
    }

    /**
     * Проверка размера запроса
     */
    private function validateRequestSize(Request $request): void
    {
        $contentLength = $request->contentLength;
        
        if ($contentLength > $this->maxRequestSize) {
            $maxSizeMB = round($this->maxRequestSize / (1024 * 1024), 2);
            throw new BadRequestHttpException(
                "Request size exceeds maximum allowed size of {$maxSizeMB}MB"
            );
        }
    }

    /**
     * Добавление CORS заголовков
     */
    private function addCorsHeaders(Response $response): void
    {
        $response->headers->set('Access-Control-Allow-Origin', '*');
        $response->headers->set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        $response->headers->set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
        $response->headers->set('Access-Control-Max-Age', '86400');
    }

    /**
     * Добавление заголовков производительности
     */
    private function addPerformanceHeaders(Response $response, float $executionTime): void
    {
        $response->headers->set('X-Response-Time', round($executionTime * 1000, 2) . 'ms');
        $response->headers->set('X-Memory-Usage', round(memory_get_peak_usage(true) / 1024 / 1024, 2) . 'MB');
    }

    /**
     * Получить статистику rate limiting для IP
     */
    public function getRateLimitInfo(string $ip): array
    {
        $cacheKey = 'encoding_api_rate_limit:' . $ip;
        $cache = Yii::$app->cache;
        
        $requestCount = $cache->get($cacheKey) ?: 0;
        $remaining = max(0, $this->rateLimit - $requestCount);
        
        return [
            'limit' => $this->rateLimit,
            'remaining' => $remaining,
            'used' => $requestCount,
            'window' => $this->rateLimitWindow,
        ];
    }
}
