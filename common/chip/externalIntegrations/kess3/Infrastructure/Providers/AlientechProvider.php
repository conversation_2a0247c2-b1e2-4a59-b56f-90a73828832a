<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Infrastructure\Providers;

use common\chip\alientech\entities\dto\AsyncOperationDto;
use common\chip\alientech\services\AlientechLinkService;
use common\chip\alientech\services\AlientechService;
use common\chip\alientech\services\AsyncOperationService;
use common\chip\alientech\services\FileSlotService;
use common\chip\externalIntegrations\kess3\Infrastructure\Adapters\AlientechOperationAdapter;
use common\chip\externalIntegrations\kess3\Interfaces\DecodingOperationInterface;
use common\chip\externalIntegrations\kess3\Interfaces\DecodingProviderInterface;
use common\models\ProjectFiles;

/**
 * Провайдер для интеграции с Alientech API
 */
final class AlientechProvider implements DecodingProviderInterface
{
    public function __construct(
        private readonly AlientechLinkService $linkService,
        private readonly AlientechService $alientechService,
        private readonly FileSlotService $fileSlotService,
        private readonly AlientechOperationAdapter $operationAdapter
    ) {}
    
    public function startDecoding(string $filePath, array $userInfo = []): DecodingOperationInterface
    {
        // Создаем временный ProjectFiles объект для совместимости со старым API
        $projectFile = new ProjectFiles();
        $projectFile->path = $filePath;
        $projectFile->project_id = $userInfo['projectId'] ?? 0;
        $projectFile->id = $userInfo['fileId'] ?? 0;
        
        // Используем существующую логику для начала декодинга
        $response = $this->linkService->processRequest()
            ->setMethod('POST')
            ->setFormat('urlencoded')
            ->setUrl([
                "/api/kess3/decode-read-file/" . $this->alientechService->getCustomerCode(),
                "callbackURL" => $this->alientechService->getDecodeCallbackUrl()
            ])
            ->addFile('readFile', $filePath)
            ->addData(['userInfo' => json_encode($userInfo)])
            ->send();
            
        $this->linkService->processResponse($response);
        
        // Если слишком много открытых слотов, закрываем их и повторяем
        if ($this->linkService->getRespData() === AsyncOperationService::TOO_MANY_OPEN_KESS3_FILE_SLOTS) {
            $this->fileSlotService->closeAllFileSlots();
            return $this->startDecoding($filePath, $userInfo);
        }
        
        // Создаем DTO из ответа
        $asyncOperationDto = new AsyncOperationDto($this->linkService->getRespData());
        $asyncOperationDto->setUserInfo($userInfo);
        
        // Адаптируем к нашему интерфейсу
        return $this->operationAdapter->fromAsyncOperationDto($asyncOperationDto);
    }
    
    public function checkOperationStatus(string $operationId): DecodingOperationInterface
    {
        $response = $this->linkService->processRequest()
            ->setMethod('GET')
            ->setFormat('urlencoded')
            ->setUrl('/api/async-operations/' . $operationId)
            ->send();
            
        $this->linkService->processResponse($response);
        
        $asyncOperationDto = new AsyncOperationDto($this->linkService->getRespData());
        
        return $this->operationAdapter->fromAsyncOperationDto($asyncOperationDto);
    }
    
    public function downloadDecodedFiles(string $operationId): array
    {
        // Эта логика будет реализована в отдельном сервисе
        // Пока возвращаем пустой массив
        return [];
    }
    
    public function isAvailable(): bool
    {
        try {
            // Простая проверка доступности через получение токена
            return !empty($this->linkService->processAccessToken());
        } catch (\Throwable $e) {
            return false;
        }
    }
}
