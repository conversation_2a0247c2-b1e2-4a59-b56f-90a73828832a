<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Infrastructure\Http;

use common\chip\externalIntegrations\kess3\Infrastructure\Dto\AuthCredentials;
use common\chip\externalIntegrations\kess3\Domain\Exception\AuthenticationException;
use Psr\Log\LoggerInterface;
use yii\db\Connection;
use yii\caching\CacheInterface;

/**
 * Провайдер аутентификации для Alientech API
 */
final class AuthProvider implements AuthProviderInterface
{
    private const TOKEN_CACHE_KEY = 'kess3_alientech_auth_token';
    private const TOKEN_EXPIRY_CACHE_KEY = 'kess3_alientech_token_expiry';
    
    private ?string $accessToken = null;
    private ?\DateTimeInterface $tokenExpiry = null;
    
    public function __construct(
        private readonly Connection $db,
        private readonly CacheInterface $cache,
        private readonly LoggerInterface $logger,
        private readonly string $authUrl = '/api/access-tokens/request',
        private readonly int $tokenCacheDuration = 3600,
        private readonly int $refreshThreshold = 300
    ) {
        $this->loadTokenFromCache();
    }
    
    public function getToken(): string
    {
        $this->logger->info('AuthProvider: Getting access token');
        
        if ($this->isValid()) {
            $this->logger->info('AuthProvider: Using cached valid token');
            return $this->accessToken;
        }
        
        $this->logger->info('AuthProvider: Token invalid or expired, refreshing');
        
        if (!$this->refreshToken()) {
            throw AuthenticationException::authenticationFailed('Failed to refresh token');
        }
        
        return $this->accessToken;
    }
    
    public function refreshToken(): bool
    {
        $this->logger->info('AuthProvider: Refreshing access token');
        
        try {
            // Очищаем текущий токен
            $this->clearToken();
            
            // Получаем учетные данные из БД (как в оригинальном коде)
            $credentials = $this->getCredentialsFromDatabase();
            
            // Выполняем аутентификацию
            return $this->authenticate($credentials);
            
        } catch (\Throwable $e) {
            $this->logger->error('AuthProvider: Failed to refresh token', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw AuthenticationException::authenticationFailed($e->getMessage());
        }
    }
    
    public function isValid(): bool
    {
        if (empty($this->accessToken)) {
            return false;
        }
        
        if ($this->tokenExpiry === null) {
            // Если нет информации о времени истечения, считаем токен действительным
            // но проверяем, не пора ли его обновить по времени кеширования
            return $this->isTokenFreshEnough();
        }
        
        $now = new \DateTimeImmutable();
        $refreshTime = $this->tokenExpiry->modify("-{$this->refreshThreshold} seconds");
        
        return $now < $refreshTime;
    }
    
    public function authenticate(AuthCredentials $credentials): bool
    {
        $this->logger->info('AuthProvider: Authenticating with credentials', [
            'credentials' => $credentials->toMaskedArray()
        ]);
        
        if (!$credentials->isValid()) {
            throw AuthenticationException::invalidCredentials();
        }
        
        try {
            // Создаем HTTP клиент (упрощенная версия для аутентификации)
            $client = new \yii\httpclient\Client([
                'baseUrl' => $this->getApiBaseUrl(),
                'contentLoggingMaxSize' => 1000000,
                'responseConfig' => [
                    'format' => \yii\httpclient\Client::FORMAT_JSON
                ],
                'requestConfig' => [
                    'format' => \yii\httpclient\Client::FORMAT_JSON
                ]
            ]);
            
            $response = $client->createRequest()
                ->setMethod('POST')
                ->setUrl($this->authUrl)
                ->setData($credentials->toArray())
                ->send();
            
            $this->logger->info('AuthProvider: Authentication response', [
                'status' => $response->getStatusCode(),
                'isOk' => $response->isOk
            ]);
            
            if (!$response->isOk) {
                $this->logger->error('AuthProvider: Authentication failed', [
                    'status' => $response->getStatusCode(),
                    'content' => $response->getContent()
                ]);
                
                if ($response->getStatusCode() === 401) {
                    throw AuthenticationException::invalidCredentials();
                }
                
                throw AuthenticationException::authenticationFailed(
                    'HTTP ' . $response->getStatusCode()
                );
            }
            
            $content = $response->getContent();
            $data = json_decode($content, true);
            
            if (!isset($data['accessToken']) || empty($data['accessToken'])) {
                $this->logger->error('AuthProvider: No access token in response', [
                    'response' => $content
                ]);
                throw AuthenticationException::authenticationFailed('No access token in response');
            }
            
            $this->accessToken = $data['accessToken'];
            $this->tokenExpiry = new \DateTimeImmutable("+{$this->tokenCacheDuration} seconds");
            
            // Сохраняем токен в БД и кеш
            $this->saveTokenToDatabase();
            $this->saveTokenToCache();
            
            $this->logger->info('AuthProvider: Authentication successful');
            
            return true;
            
        } catch (AuthenticationException $e) {
            throw $e;
        } catch (\Throwable $e) {
            $this->logger->error('AuthProvider: Authentication error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw AuthenticationException::authenticationFailed($e->getMessage());
        }
    }
    
    public function clearToken(): void
    {
        $this->logger->info('AuthProvider: Clearing access token');
        
        $this->accessToken = null;
        $this->tokenExpiry = null;
        
        // Очищаем кеш
        $this->cache->delete(self::TOKEN_CACHE_KEY);
        $this->cache->delete(self::TOKEN_EXPIRY_CACHE_KEY);
    }
    
    public function getTokenExpiry(): ?\DateTimeInterface
    {
        return $this->tokenExpiry;
    }
    
    /**
     * Загружает токен из кеша
     */
    private function loadTokenFromCache(): void
    {
        $this->loadTokenFromDatabase();
//        $this->accessToken = $this->cache->get(self::TOKEN_CACHE_KEY) ?? '';
//        $expiryTimestamp = $this->cache->get(self::TOKEN_EXPIRY_CACHE_KEY) ?? '';
//
//        if ($expiryTimestamp) {
//            $this->tokenExpiry = new \DateTimeImmutable('@' . $expiryTimestamp);
//        }
//
//        // Если нет токена в кеше, пытаемся загрузить из БД
//        if (empty($this->accessToken)) {
//            $this->loadTokenFromDatabase();
//        }
    }
    
    /**
     * Загружает токен из БД (как в оригинальном коде)
     */
    private function loadTokenFromDatabase(): void
    {
        try {
            $token = $this->db->createCommand(
                'SELECT accessToken FROM alientech_api WHERE id = 1'
            )->queryScalar();
            
            if (!empty($token)) {
                $this->accessToken = (string) $token;
                $this->logger->info('AuthProvider: Token loaded from database');
            }
        } catch (\Throwable $e) {
            $this->logger->error('AuthProvider: Failed to load token from database', [
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Сохраняет токен в БД (как в оригинальном коде)
     */
    private function saveTokenToDatabase(): void
    {
        try {
            $this->db->createCommand(
                "INSERT INTO alientech_api (id, accessToken) VALUES(1, :token) 
                 ON DUPLICATE KEY UPDATE accessToken = :token"
            )->bindValues([
                ':token' => $this->accessToken,
            ])->execute();
            
            $this->logger->info('AuthProvider: Token saved to database');
        } catch (\Throwable $e) {
            $this->logger->error('AuthProvider: Failed to save token to database', [
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Сохраняет токен в кеш
     */
    private function saveTokenToCache(): void
    {
        $this->cache->set(self::TOKEN_CACHE_KEY, $this->accessToken, $this->tokenCacheDuration);
        
        if ($this->tokenExpiry) {
            $this->cache->set(
                self::TOKEN_EXPIRY_CACHE_KEY, 
                $this->tokenExpiry->getTimestamp(), 
                $this->tokenCacheDuration
            );
        }
    }
    
    /**
     * Получает учетные данные из переменных окружения (как в оригинальном коде)
     */
    private function getCredentialsFromDatabase(): AuthCredentials
    {
        $clientGuid = $_ENV['ALIENTECH_CLIENT_GUID'] ?? '';
        $secretKey = $_ENV['ALIENTECH_SECRET_KEY'] ?? '';
        
        if (empty($clientGuid) || empty($secretKey)) {
            throw AuthenticationException::invalidCredentials();
        }
        
        return new AuthCredentials($clientGuid, $secretKey);
    }
    
    /**
     * Получает базовый URL API из переменных окружения
     */
    private function getApiBaseUrl(): string
    {
        return $_ENV['ALIENTECH_API_URL'] ?? 'https://api.alientech.to';
    }
    
    /**
     * Проверяет, достаточно ли свежий токен (если нет информации об истечении)
     */
    private function isTokenFreshEnough(): bool
    {
        $cacheExpiry = $this->cache->get(self::TOKEN_EXPIRY_CACHE_KEY);
        
        if (!$cacheExpiry) {
            return false;
        }
        
        $now = time();
        $refreshTime = $cacheExpiry - $this->refreshThreshold;
        
        return $now < $refreshTime;
    }
}
