<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Infrastructure\Http;

use common\chip\externalIntegrations\kess3\Infrastructure\Dto\ApiRequest;
use common\chip\externalIntegrations\kess3\Infrastructure\Dto\ApiResponse;
use common\chip\externalIntegrations\kess3\Domain\Exception\ApiException;
use common\chip\externalIntegrations\kess3\Domain\Exception\AuthenticationException;
use Psr\Log\LoggerInterface;
use yii\base\InvalidConfigException;
use yii\httpclient\Client;
use yii\httpclient\Exception as HttpException;

/**
 * HTTP клиент для Alientech API
 */
final class HttpClient implements HttpClientInterface
{
    private ?Client $client = null;
    private ?AuthProviderInterface $authProvider = null;
    private string $baseUrl = '';
    private int $defaultTimeout = 30;
    
    public function __construct(
        private readonly LoggerInterface $logger
    ) {}
    
    public function sendRequest(ApiRequest $request): ApiResponse
    {
        $this->logger->info('HttpClient: Sending request', [
            'method' => $request->getMethod(),
            'url' => $request->getUrl(),
            'timeout' => $request->getTimeout(),
            'hasFiles' => $request->hasFiles()
        ]);
        
        try {
            $client = $this->getClient();
            $httpRequest = $this->buildHttpRequest($client, $request);
            
            // Добавляем аутентификацию если доступна
            if ($this->authProvider && $this->isAuthenticated()) {
                $token = $this->authProvider->getToken();
                $httpRequest->setHeaders(['X-Alientech-ReCodAPI-LLC' => $token]);
            }
            
            $response = $httpRequest->send();
            
            $apiResponse = $this->processResponse($response);
            
            // Обрабатываем ошибки аутентификации
            if ($apiResponse->isAuthError() && $this->authProvider) {
                $this->logger->info('HttpClient: Authentication error, trying to refresh token');
                
                if ($this->authProvider->refreshToken()) {
                    $this->logger->info('HttpClient: Token refreshed, retrying request');
                    
                    // Повторяем запрос с новым токеном
                    $token = $this->authProvider->getToken();
                    $httpRequest->setHeaders(['X-Alientech-ReCodAPI-LLC' => $token]);
                    $response = $httpRequest->send();
                    $apiResponse = $this->processResponse($response);
                } else {
                    throw AuthenticationException::authenticationFailed('Failed to refresh token');
                }
            }
            
            $this->logger->info('HttpClient: Request completed', [
                'status' => $apiResponse->getStatusCode(),
                'successful' => $apiResponse->isSuccessful()
            ]);
            
            return $apiResponse;
            
        } catch (AuthenticationException $e) {
            throw $e;
        } catch (HttpException $e) {
            $this->logger->error('HttpClient: HTTP error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw new ApiException('HTTP request failed: ' . $e->getMessage(), 0, $e);
        } catch (\Throwable $e) {
            $this->logger->error('HttpClient: Unexpected error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw new ApiException('Unexpected error: ' . $e->getMessage(), 0, $e);
        }
    }
    
    public function isAuthenticated(): bool
    {
        if (!$this->authProvider) {
            return false;
        }
        
        try {
            return $this->authProvider->isValid();
        } catch (\Throwable $e) {
            $this->logger->error('HttpClient: Error checking authentication', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    public function setAuthProvider(AuthProviderInterface $authProvider): void
    {
        $this->authProvider = $authProvider;
        $this->logger->info('HttpClient: Auth provider set');
    }
    
    public function setBaseUrl(string $baseUrl): void
    {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->client = null; // Сбрасываем клиент для пересоздания с новым URL
        
        $this->logger->info('HttpClient: Base URL set', ['baseUrl' => $this->baseUrl]);
    }
    
    public function setDefaultTimeout(int $seconds): void
    {
        $this->defaultTimeout = $seconds;
        $this->logger->info('HttpClient: Default timeout set', ['timeout' => $seconds]);
    }
    
    /**
     * Получает или создает HTTP клиент
     */
    private function getClient(): Client
    {
        if ($this->client === null) {
            $this->client = new Client([
                'baseUrl' => $this->baseUrl,
                'contentLoggingMaxSize' => 1000000,
                'responseConfig' => [
                    'format' => Client::FORMAT_JSON
                ],
            ]);
            
            $this->logger->info('HttpClient: Client created', ['baseUrl' => $this->baseUrl]);
        }
        
        return $this->client;
    }

    /**
     * Создает HTTP запрос из ApiRequest
     * @throws InvalidConfigException
     */
    private function buildHttpRequest(Client $client, ApiRequest $request): \yii\httpclient\Request
    {
        $httpRequest = $client->createRequest()
            ->setMethod($request->getMethod())
            ->setUrl($request->getUrl())
            ->setOptions(['timeout' => $request->getTimeout() ?: $this->defaultTimeout]);
        
        // Добавляем заголовки
        foreach ($request->getHeaders() as $key => $value) {
            $httpRequest->addHeaders([$key => $value]);
        }
        
        // Обрабатываем данные и файлы
        if ($request->hasFiles()) {
            // Для запросов с файлами используем multipart/form-data
            $httpRequest->setFormat(Client::FORMAT_URLENCODED);
            
            $data = $request->getData();
            foreach ($request->getFiles() as $key => $filePath) {
                $httpRequest->addFile('file',$filePath);
            }
            
            $httpRequest->setData($data);
        } else {
            // Для обычных запросов используем JSON
            $httpRequest->setFormat(Client::FORMAT_JSON);
            
            if (!empty($request->getData())) {
                $httpRequest->setData($request->getData());
            }
        }
        
        return $httpRequest;
    }
    
    /**
     * Обрабатывает HTTP ответ и создает ApiResponse
     */
    private function processResponse(\yii\httpclient\Response $response): ApiResponse
    {
        $statusCode = (int)$response->getStatusCode();
        $headers = $response->getHeaders()->toArray();
        $rawContent = $response->getContent();
        
        // Пытаемся декодировать JSON
        $data = null;
        if (!empty($rawContent)) {
            $decoded = json_decode($rawContent, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $data = $decoded;
            } else {
                // Если не JSON, сохраняем как строку
                $data = $rawContent;
            }
        }
        
        $this->logger->info('HttpClient: Response processed', [
            'status' => $statusCode,
            'contentLength' => strlen($rawContent),
            'hasData' => !empty($data)
        ]);
        
        // Логируем ошибки
        if (!$response->isOk) {
            $this->logger->warning('HttpClient: Non-OK response', [
                'status' => $statusCode,
                'content' => substr($rawContent, 0, 500) // Ограничиваем размер для логирования
            ]);
        }
        
        return new ApiResponse($statusCode, $headers, $data, $rawContent);
    }
}
