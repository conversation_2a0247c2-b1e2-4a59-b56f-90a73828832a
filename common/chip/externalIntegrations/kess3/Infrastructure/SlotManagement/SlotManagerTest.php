<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Infrastructure\SlotManagement;

use common\chip\externalIntegrations\kess3\Infrastructure\Http\HttpClientInterface;
use common\chip\externalIntegrations\kess3\Infrastructure\Dto\ApiRequest;
use common\chip\externalIntegrations\kess3\Infrastructure\Dto\ApiResponse;
use common\chip\externalIntegrations\kess3\Infrastructure\Dto\SlotInfo;
use PHPUnit\Framework\TestCase;
use Psr\Log\NullLogger;
use yii\caching\ArrayCache;

/**
 * Базовый тест для проверки SlotManager
 */
class SlotManagerTest extends TestCase
{
    private HttpClientInterface $httpClient;
    private ArrayCache $cache;
    private NullLogger $logger;
    private SlotManager $slotManager;
    
    protected function setUp(): void
    {
        $this->httpClient = $this->createMock(HttpClientInterface::class);
        $this->cache = new ArrayCache();
        $this->logger = new NullLogger();
        
        $this->slotManager = new SlotManager(
            $this->httpClient,
            $this->cache,
            $this->logger,
            60, // cache duration
            3   // max slots
        );
    }
    
    public function testSlotManagerCreation(): void
    {
        $this->assertInstanceOf(SlotManagerInterface::class, $this->slotManager);
        $this->assertEquals(3, $this->slotManager->getMaxSlots());
    }
    
    public function testHasAvailableSlotsWithEmptyResponse(): void
    {
        // Мокаем ответ API с пустым списком слотов
        $response = new ApiResponse(200, [], [], '[]');
        
        $this->httpClient->expects($this->once())
            ->method('sendRequest')
            ->willReturn($response);
        
        $hasAvailable = $this->slotManager->hasAvailableSlots();
        
        $this->assertTrue($hasAvailable); // 0 открытых слотов < 3 максимальных
    }
    
    public function testHasAvailableSlotsWithFullSlots(): void
    {
        // Мокаем ответ API с 3 открытыми слотами
        $slotsData = [
            ['guid' => 'slot1', 'isClosed' => false, 'createdOn' => '2024-01-01T10:00:00Z'],
            ['guid' => 'slot2', 'isClosed' => false, 'createdOn' => '2024-01-01T10:00:00Z'],
            ['guid' => 'slot3', 'isClosed' => false, 'createdOn' => '2024-01-01T10:00:00Z'],
        ];
        
        $response = new ApiResponse(200, [], $slotsData, json_encode($slotsData));
        
        $this->httpClient->expects($this->once())
            ->method('sendRequest')
            ->willReturn($response);
        
        $hasAvailable = $this->slotManager->hasAvailableSlots();
        
        $this->assertFalse($hasAvailable); // 3 открытых слотов = 3 максимальных
    }
    
    public function testGetSlots(): void
    {
        $slotsData = [
            ['guid' => 'slot1', 'isClosed' => false, 'createdOn' => '2024-01-01T10:00:00Z'],
            ['guid' => 'slot2', 'isClosed' => true, 'createdOn' => '2024-01-01T09:00:00Z'],
        ];
        
        $response = new ApiResponse(200, [], $slotsData, json_encode($slotsData));
        
        $this->httpClient->expects($this->once())
            ->method('sendRequest')
            ->willReturn($response);
        
        $slots = $this->slotManager->getSlots();
        
        $this->assertCount(2, $slots);
        $this->assertInstanceOf(SlotInfo::class, $slots[0]);
        $this->assertEquals('slot1', $slots[0]->getGuid());
        $this->assertTrue($slots[0]->isOpen());
        $this->assertEquals('slot2', $slots[1]->getGuid());
        $this->assertTrue($slots[1]->isClosed());
    }
    
    public function testCloseSlot(): void
    {
        $response = new ApiResponse(200, [], null, '');
        
        $this->httpClient->expects($this->once())
            ->method('sendRequest')
            ->with($this->callback(function (ApiRequest $request) {
                return $request->getMethod() === 'POST' 
                    && str_contains($request->getUrl(), '/api/kess3/file-slots/test-guid/close');
            }))
            ->willReturn($response);
        
        $result = $this->slotManager->closeSlot('test-guid');
        
        $this->assertTrue($result);
    }
    
    public function testCloseSlotWithEmptyGuid(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Slot GUID cannot be empty');
        
        $this->slotManager->closeSlot('');
    }
    
    public function testGetOpenSlotsCount(): void
    {
        $slotsData = [
            ['guid' => 'slot1', 'isClosed' => false, 'createdOn' => '2024-01-01T10:00:00Z'],
            ['guid' => 'slot2', 'isClosed' => true, 'createdOn' => '2024-01-01T09:00:00Z'],
            ['guid' => 'slot3', 'isClosed' => false, 'createdOn' => '2024-01-01T08:00:00Z'],
        ];
        
        $response = new ApiResponse(200, [], $slotsData, json_encode($slotsData));
        
        $this->httpClient->expects($this->once())
            ->method('sendRequest')
            ->willReturn($response);
        
        $openCount = $this->slotManager->getOpenSlotsCount();
        
        $this->assertEquals(2, $openCount); // slot1 и slot3 открыты
    }
    
    public function testClearCache(): void
    {
        // Сначала заполняем кеш
        $this->cache->set('kess3_alientech_slots', ['test' => 'data'], 60);
        
        $this->slotManager->clearCache();
        
        // Проверяем, что кеш очищен
        $this->assertFalse($this->cache->exists('kess3_alientech_slots'));
    }
}
