<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Infrastructure\Adapter;

use common\chip\externalIntegrations\kess3\Domain\Exception\UnknownFileTypeException;
use common\chip\externalIntegrations\kess3\Infrastructure\Dto\ApiResponse;
use common\chip\externalIntegrations\kess3\Infrastructure\DTO\EncodingRequestDto;
use common\chip\externalIntegrations\kess3\Infrastructure\DTO\EncodingResultDto;
use common\chip\externalIntegrations\kess3\Infrastructure\ExternalService\AlientechApiClientInterface;
use common\chip\externalIntegrations\kess3\Infrastructure\Configuration\ConfigProviderInterface;
use common\chip\externalIntegrations\kess3\Infrastructure\Http\HttpClientInterface;
use common\chip\externalIntegrations\kess3\Infrastructure\SlotManagement\SlotManagerInterface;
use Yii;

/**
 * Адаптер для энкодинга через Alientech API
 */
final class AlientechEncodingAdapter implements ExternalEncodingAdapter
{
    public function __construct(
        private readonly AlientechApiClientInterface $apiClient,
        private readonly HttpClientInterface $httpClient,
        private readonly ConfigProviderInterface $config,
        private readonly SlotManagerInterface $slotManager
    ) {}

    public function encode(EncodingRequestDto $request): EncodingResultDto
    {
        // Устаревший метод - перенаправляем на encodeProject
        Yii::warning(
            message: "AlientechEncodingAdapter::encode() is deprecated. Use encodeProject() instead.",
            category: 'kess3.encoding.alientech'
        );

        // Получаем файлы проекта
        $files = \common\models\ProjectFiles::find()
            ->where([
                'project_id' => $request->projectId,
                'file_type' => \common\helpers\ProjectHelper::FILE_TYPE_MODIFIED_DECODED,
                'isDeleted' => 0
            ])
            ->all();

        return $this->encodeProject($request->projectId, $files, $request->options);
    }

    /**
     * Запустить энкодирование проекта (новый метод для совместимости с архитектурой)
     */
    public function startEncoding(int $projectId, array $files, string $callbackUrl, array $userInfo): array
    {
        try {
            Yii::info(
                message: "AlientechEncodingAdapter: Starting encoding for project {$projectId} with " . count($files) . " files",
                category: 'kess3.encoding.alientech'
            );

            if (empty($files)) {
                return [
                    'success' => false,
                    'error' => "No files found for project {$projectId}",
                    'externalOperationId' => null,
                    'slotGuid' => null,
                ];
            }

            // Запускаем энкодирование через старый метод
            $result = $this->encodeProject($projectId, $files, ['callbackUrl' => $callbackUrl, 'userInfo' => $userInfo]);

            if ($result->success) {
                return [
                    'success' => true,
                    'externalOperationId' => $result->meta['projectOperationId'] ?? '',
                    'slotGuid' => $result->meta['kess3FileSlotGUID'] ?? '',
                    'result' => $result->meta,
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $result->error,
                    'externalOperationId' => null,
                    'slotGuid' => null,
                ];
            }
        } catch (\Exception $e) {
            Yii::error(
                message: "AlientechEncodingAdapter: Failed to start encoding: {$e->getMessage()}",
                category: 'kess3.encoding.alientech'
            );

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'externalOperationId' => null,
                'slotGuid' => null,
            ];
        }
    }

    public function encodeProject(int $projectId, array $files, array $options = []): EncodingResultDto
    {
        try {
            Yii::info(
                message: "AlientechEncodingAdapter: Starting project encoding for project {$projectId} with " . count($files) . " files",
                category: 'kess3.encoding.alientech'
            );

            if (empty($files)) {
                return new EncodingResultDto(
                    success: false,
                    error: "No files found for project {$projectId}",
                    encodedFilePath: null,
                    meta: ['projectId' => $projectId]
                );
            }

            // Этап 1: Загрузка всех файлов проекта
            $uploadResults = $this->uploadAllProjectFiles($files, $options);
            if (!$uploadResults['success']) {
                return new EncodingResultDto(
                    success: false,
                    error: "Files upload failed: " . $uploadResults['error'],
                    encodedFilePath: null,
                    meta: ['uploadResults' => $uploadResults, 'projectId' => $projectId]
                );
            }

            // Этап 2: Получение данных декодинга проекта
            $decodeResult = $this->getProjectDecodeResult($projectId);
            if (!$decodeResult) {
                return new EncodingResultDto(
                    success: false,
                    error: "No decode result found for project {$projectId}",
                    encodedFilePath: null,
                    meta: ['projectId' => $projectId]
                );
            }

            // Этап 3: Подготовка данных для энкодинга проекта
            $encodeData = $this->prepareProjectEncodeData($uploadResults['uploadedFiles'], $decodeResult, $options);

            // Этап 4: Запуск энкодинга всего проекта
            $encodeResult = $this->startProjectEncoding($encodeData, $decodeResult);

            return $encodeResult;

        } catch (\Exception $e) {
            Yii::error(
                message: "AlientechEncodingAdapter: Failed to encode project {$projectId}: {$e->getMessage()}",
                category: 'kess3.encoding.alientech'
            );

            return new EncodingResultDto(
                success: false,
                error: $e->getMessage(),
                encodedFilePath: null,
                meta: ['exception' => get_class($e), 'projectId' => $projectId]
            );
        }
    }

    /**
     * Загрузить файл на удаленный сервер если необходимо
     */
    private function uploadFileIfNeeded(EncodingRequestDto $request): array
    {
        try {
            // Проверяем, нужна ли загрузка файла
            $uploadType = $request->options['uploadType'] ?? 'modified';

            Yii::info(
                message: "AlientechEncodingAdapter: Uploading file {$request->fileName} (type: {$uploadType})",
                category: 'kess3.encoding.alientech'
            );

            // Создаем временный объект ProjectFiles для совместимости
            $projectFile = $this->createProjectFileFromRequest($request);

            // Используем RemoteUploadAdapter для загрузки
            $remoteUpload = new RemoteUploadAdapter($this->linkService, $this->alientechService);
            $uploadResult = $remoteUpload->uploadFile($projectFile, [
                'uploadType' => $uploadType
            ]);

            if ($uploadResult['success']) {
                Yii::info(
                    message: "AlientechEncodingAdapter: File uploaded successfully, uploadId: {$uploadResult['uploadId']}",
                    category: 'kess3.encoding.alientech'
                );
            }

            return $uploadResult;

        } catch (\Exception $e) {
            Yii::error(
                message: "AlientechEncodingAdapter: File upload failed: {$e->getMessage()}",
                category: 'kess3.encoding.alientech'
            );

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'exception' => get_class($e)
            ];
        }
    }

    /**
     * Запустить процесс энкодинга
     */
    private function startEncodingProcess(EncodingRequestDto $request, array $uploadResult): EncodingResultDto
    {
        try {
            Yii::info(
                message: "AlientechEncodingAdapter: Starting encoding process for {$request->fileName}",
                category: 'kess3.encoding.alientech'
            );

            $response = $this->linkService->processRequest()
                ->setMethod('POST')
                ->setFormat('json')
                ->setUrl("/api/kess3/encode-file/" . $this->alientechService->getCustomerCode())
                ->addData([
                    'userCustomerCode' => $this->alientechService->getCustomerCode(),
                    'kess3FileSlotGUID' => $uploadResult['slotGUID'] ?? null,
                    'callbackURL' => $this->alientechService->getEncodeCallbackUrl(),
                    'userInfo' => json_encode([
                        'projectId' => $request->projectId,
                        'fileName' => $request->fileName,
                        'options' => $request->options,
                        'uploadId' => $uploadResult['uploadId'] ?? null
                    ])
                ])
                ->send();

            $this->linkService->processResponse($response);
            $responseData = $this->linkService->getRespData();

            if (isset($responseData['error'])) {
                return new EncodingResultDto(
                    success: false,
                    error: $responseData['error'],
                    encodedFilePath: null,
                    meta: [
                        'response' => $responseData,
                        'uploadResult' => $uploadResult
                    ]
                );
            }

            Yii::info(
                message: "AlientechEncodingAdapter: Successfully started encoding, operation ID: " . ($responseData['guid'] ?? 'unknown'),
                category: 'kess3.encoding.alientech'
            );

            return new EncodingResultDto(
                success: true,
                error: null,
                encodedFilePath: null, // Будет заполнено после завершения асинхронной операции
                meta: [
                    'operationId' => $responseData['guid'] ?? null,
                    'slotGUID' => $uploadResult['slotGUID'] ?? null,
                    'uploadId' => $uploadResult['uploadId'] ?? null,
                    'response' => $responseData,
                    'uploadResult' => $uploadResult
                ]
            );

        } catch (\Exception $e) {
            return new EncodingResultDto(
                success: false,
                error: $e->getMessage(),
                encodedFilePath: null,
                meta: [
                    'exception' => get_class($e),
                    'uploadResult' => $uploadResult
                ]
            );
        }
    }

    /**
     * Загрузить все файлы проекта
     */
    private function uploadAllProjectFiles(array $files, array $options): array
    {
        try {
            Yii::info(
                message: "AlientechEncodingAdapter: Uploading " . count($files) . " project files",
                category: 'kess3.encoding.alientech'
            );

            $uploadedFiles = [];
            $errors = [];
            $remoteUpload = new RemoteUploadAdapter($this->httpClient, $this->config, $this->slotManager);

            foreach ($files as $file) {

                $params = $file->params ? json_decode($file->params, true) : [];

                if (!$params['kess3FileSlotGUID']) {
                    throw new \Exception("Missing 'kess3FileSlotGUID' in file params for file {$file->title}");
                }

                if (!$params['fileType']) {
                    throw new \Exception("Missing 'fileType' in file params for file {$file->title}");
                }


                $uploadResult = $remoteUpload->uploadFile($file, [
                    'uploadType' => $options['uploadType'] ?? 'modified',
                    'kess3FileSlotGUID' => $params['kess3FileSlotGUID'],
                    'fileType' => $this->calculateModifiedFileType($params['fileType']),
                ]);

                if ($uploadResult['success']) {
                    $uploadedFiles[] = [
                        'file' => $file,
                        'uploadId' => $uploadResult['uploadId'],
                        'slotGUID' => $uploadResult['slotGUID'] ?? null
                    ];
                } else {
                    $errors[] = "File {$file->title}: " . $uploadResult['error'];
                }
            }

            if (!empty($errors)) {
                return [
                    'success' => false,
                    'error' => implode('; ', $errors),
                    'uploadedFiles' => $uploadedFiles
                ];
            }

            Yii::info(
                message: "AlientechEncodingAdapter: Successfully uploaded " . count($uploadedFiles) . " files",
                category: 'kess3.encoding.alientech'
            );

            return [
                'success' => true,
                'uploadedFiles' => $uploadedFiles,
                'totalFiles' => count($files)
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'exception' => get_class($e)
            ];
        }
    }

    /**
     * @throws UnknownFileTypeException
     */
    public function calculateModifiedFileType(string $fileType = ''): string
    {
        $probableType = str_replace('Decoded', 'Modified', $fileType);

        if (in_array($probableType, $this->availableFileTypes())) {
            Yii::info(
                message: "AlientechEncodingAdapter: FileType found " . $probableType,
                category: 'kess3.encoding.alientech'
            );

            return $probableType;
        }

        Throw new UnknownFileTypeException("Unknown file type '{$fileType}'");
    }

    private function availableFileTypes(): array
    {
        return [
            'Read',
            'ID',
            'OBDDecoded',
            'OBDModified',

            'OBDOriginal',
            'OBDEncoded',

            'BootBenchDecodedMicro',
            'BootBenchDecodedFlash',
            'BootBenchDecodedEEPROM',
            'BootBenchDecodedMapFile',

            'BootBenchModifiedMicro',
            'BootBenchModifiedFlash',
            'BootBenchModifiedEEPROM',
            'BootBenchModifiedMapFile',

            'BootBenchEncoded',
        ];
    }

    /**
     * Получить результат декодинга проекта
     */
    private function getProjectDecodeResult(int $projectId): ?\common\chip\alientech\entities\dto\AsyncOperationResultDto
    {
        try {
            $asyncOperation = \common\models\AlientechAsyncOperation::find()
                ->where(['project_id' => $projectId])
                ->one();

            if (!$asyncOperation || !$asyncOperation->result) {
                return null;
            }

            $result = new \common\chip\alientech\entities\dto\AsyncOperationResultDto($asyncOperation->result);

            return $result;

        } catch (\Exception $e) {
            Yii::error(
                message: "AlientechEncodingAdapter: Failed to get decode result for project {$projectId}: {$e->getMessage()}",
                category: 'kess3.encoding.alientech'
            );
            return null;
        }
    }

    /**
     * Подготовить данные для энкодинга проекта
     */
    private function prepareProjectEncodeData(array $uploadedFiles, \common\chip\alientech\entities\dto\AsyncOperationResultDto $decodeResult, array $options): array
    {
        // Создаем массив файлов для совместимости со старым API
        $files = [];
        foreach ($uploadedFiles as $uploadedFile) {
            $file = $uploadedFile['file'];
            // Обновляем file_history с результатом загрузки
            $file->file_history = json_encode(['guid' => $uploadedFile['uploadId']]);
            $files[] = $file;
        }

        // Генерируем ключи данных для энкодинга
        $encodeRequestDataKeys = $decodeResult->generateEncodeRequestDataKeysArray($files);

        // Заполняем данные
        $filledRequestData = $decodeResult->fillEncodeRequestDataArray($encodeRequestDataKeys, $files);

        if ($filledRequestData['error']) {
            throw new \Exception('Failed to prepare encode request data');
        }

        $operationType = $decodeResult->isObd() ? 'encode_obd' : 'encode_boot';
        $encodeData = $filledRequestData['data'];
        $encodeData['userCustomerCode'] = $this->config->getCustomerCode();
        $encodeData['kess3FileSlotGUID'] = $decodeResult->getKess3FileSlotGUID();
        $encodeData['callbackURL'] = $this->config->getCallbackUrl($operationType);

        return $encodeData;
    }

    /**
     * Запустить энкодинг проекта
     */
    private function startProjectEncoding(array $encodeData, \common\chip\alientech\entities\dto\AsyncOperationResultDto $decodeResult): EncodingResultDto
    {
        try {
            Yii::info(
                message: "AlientechEncodingAdapter: Starting project encoding with data: " . json_encode(array_keys($encodeData)),
                category: 'kess3.encoding.alientech'
            );

            // Создаем запрос через новый HTTP клиент
            $apiRequest = new \common\chip\externalIntegrations\kess3\Infrastructure\DTO\ApiRequest(
                method: 'POST',
                url: "/api/kess3" . $decodeResult->generateEncodeUrl(),
                data: $encodeData,
                timeout: $this->config->getTimeout('encode')
            );

//            $response = $this->httpClient->sendRequest($apiRequest);
//            todo
            $responseString = file_get_contents(Yii::getAlias('@storage') . "/payload/alientechOperation7Started.json");

            $responseDataFake = json_decode($responseString, true);
            $response = new ApiResponse(200, [], $responseDataFake, $responseString);

            if (!$response->isSuccessful()) {
                throw new \Exception("HTTP request failed: " . $response->getStatusCode());
            }

            $responseData = $response->getData();

            if (isset($responseData['error'])) {
                return new EncodingResultDto(
                    success: false,
                    error: $responseData['error'],
                    encodedFilePath: null,
                    meta: [
                        'response' => $responseData,
                        'encodeData' => $encodeData
                    ]
                );
            }

            Yii::info(
                message: "AlientechEncodingAdapter: Successfully started project encoding, operation ID: " . ($responseData['guid'] ?? 'unknown'),
                category: 'kess3.encoding.alientech'
            );

            return new EncodingResultDto(
                success: true,
                error: null,
                encodedFilePath: null, // Для проекта не указывается
                meta: [
                    'projectOperationId' => $responseData['guid'] ?? null,
                    'kess3FileSlotGUID' => $encodeData['kess3FileSlotGUID'] ?? null,
                    'filesCount' => count($encodeData) - 3, // Исключаем служебные поля
                    'response' => $responseData,
                    'encodeData' => $encodeData
                ]
            );

        } catch (\Exception $e) {
            return new EncodingResultDto(
                success: false,
                error: $e->getMessage(),
                encodedFilePath: null,
                meta: [
                    'exception' => get_class($e),
                    'encodeData' => $encodeData
                ]
            );
        }
    }

    /**
     * Создать объект ProjectFiles из запроса для совместимости
     */
    private function createProjectFileFromRequest(EncodingRequestDto $request): \common\models\ProjectFiles
    {
        $projectFile = new \common\models\ProjectFiles();
        $projectFile->project_id = $request->projectId;
        $projectFile->title = $request->fileName;
        $projectFile->path = $request->filePath;

        // Добавляем параметры, если они есть в опциях
        if (isset($request->options['fileParams'])) {
            $projectFile->params = json_encode($request->options['fileParams']);
        }

        return $projectFile;
    }
}
