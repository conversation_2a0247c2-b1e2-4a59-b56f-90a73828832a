<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Infrastructure\Adapter;

use common\chip\externalIntegrations\kess3\Infrastructure\DTO\EncodingRequestDto;
use common\chip\externalIntegrations\kess3\Infrastructure\DTO\EncodingResultDto;
use Yii;

/**
 * Кастомный адаптер для энкодинга
 * 
 * Используется для внутренних алгоритмов энкодинга или тестирования
 */
final class CustomEncodingAdapter implements ExternalEncodingAdapter
{
    public function startEncoding(int $projectId, array $files, string $callbackUrl, array $userInfo): array
    {
        try {
            Yii::info(
                message: "CustomEncodingAdapter: Starting encoding for project {$projectId} with " . count($files) . " files",
                category: 'kess3.encoding.custom'
            );

            // Запускаем энкодирование через старый метод
            $result = $this->encodeProject($projectId, $files, ['callbackUrl' => $callbackUrl, 'userInfo' => $userInfo]);

            if ($result->success) {
                // Для custom адаптера генерируем fake operation ID
                $operationId = 'custom_' . uniqid();
                
                return [
                    'success' => true,
                    'externalOperationId' => $operationId,
                    'slotGuid' => null, // Custom адаптер не использует слоты
                    'result' => $result->meta,
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $result->error,
                    'externalOperationId' => null,
                    'slotGuid' => null,
                ];
            }
        } catch (\Exception $e) {
            Yii::error(
                message: "CustomEncodingAdapter: Failed to start encoding: {$e->getMessage()}",
                category: 'kess3.encoding.custom'
            );

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'externalOperationId' => null,
                'slotGuid' => null,
            ];
        }
    }

    public function encode(EncodingRequestDto $request): EncodingResultDto
    {
        // Устаревший метод - перенаправляем на encodeProject
        Yii::warning(
            message: "CustomEncodingAdapter::encode() is deprecated. Use encodeProject() instead.",
            category: 'kess3.encoding.custom'
        );

        // Получаем файлы проекта
        $files = \common\models\ProjectFiles::find()
            ->where([
                'project_id' => $request->projectId,
                'file_type' => \common\helpers\ProjectHelper::FILE_TYPE_MODIFIED_DECODED,
                'isDeleted' => 0
            ])
            ->all();

        return $this->encodeProject($request->projectId, $files, $request->options);
    }

    public function encodeProject(int $projectId, array $files, array $options = []): EncodingResultDto
    {
        try {
            Yii::info(
                message: "CustomEncodingAdapter: Starting project encoding for project {$projectId} with " . count($files) . " files",
                category: 'kess3.encoding.custom'
            );

            if (empty($files)) {
                return new EncodingResultDto(
                    success: false,
                    error: "No files found for project {$projectId}",
                    encodedFilePath: null,
                    meta: ['adapter' => 'custom', 'projectId' => $projectId]
                );
            }

            $encodedFiles = [];
            $errors = [];

            // Обрабатываем каждый файл проекта
            foreach ($files as $file) {
                $result = $this->encodeFile($file, $options);
                if ($result['success']) {
                    $encodedFiles[] = $result;
                } else {
                    $errors[] = "File {$file->title}: " . $result['error'];
                }
            }

            if (!empty($errors)) {
                return new EncodingResultDto(
                    success: false,
                    error: "Some files failed to encode: " . implode('; ', $errors),
                    encodedFilePath: null,
                    meta: [
                        'adapter' => 'custom',
                        'projectId' => $projectId,
                        'encodedFiles' => $encodedFiles,
                        'errors' => $errors
                    ]
                );
            }

            Yii::info(
                message: "CustomEncodingAdapter: Successfully encoded " . count($encodedFiles) . " files for project {$projectId}",
                category: 'kess3.encoding.custom'
            );

            return new EncodingResultDto(
                success: true,
                error: null,
                encodedFilePath: null, // Для проекта не указывается
                meta: [
                    'adapter' => 'custom',
                    'projectId' => $projectId,
                    'filesCount' => count($encodedFiles),
                    'encodedFiles' => $encodedFiles,
                    'options' => $options
                ]
            );

        } catch (\Exception $e) {
            Yii::error(
                message: "CustomEncodingAdapter: Failed to encode project {$projectId}: {$e->getMessage()}",
                category: 'kess3.encoding.custom'
            );

            return new EncodingResultDto(
                success: false,
                error: $e->getMessage(),
                encodedFilePath: null,
                meta: [
                    'adapter' => 'custom',
                    'projectId' => $projectId,
                    'exception' => get_class($e)
                ]
            );
        }
    }

    /**
     * Энкодинг одного файла
     */
    private function encodeFile(\common\models\ProjectFiles $file, array $options): array
    {
        try {
            // Проверяем существование файла
            if (!file_exists($file->path)) {
                return [
                    'success' => false,
                    'error' => "File not found: {$file->path}",
                    'file' => $file
                ];
            }

            // Симулируем процесс энкодинга
            $encodedFileName = $this->generateEncodedFileName($file->title);
            $encodedFilePath = dirname($file->path) . '/' . $encodedFileName;

            // Простая симуляция энкодинга - копируем файл с новым именем
            if (copy($file->path, $encodedFilePath)) {
                return [
                    'success' => true,
                    'originalFile' => $file->path,
                    'encodedFile' => $encodedFilePath,
                    'fileName' => $encodedFileName,
                    'file' => $file
                ];
            } else {
                return [
                    'success' => false,
                    'error' => "Failed to create encoded file",
                    'file' => $file
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'exception' => get_class($e),
                'file' => $file
            ];
        }
    }

    private function generateEncodedFileName(string $originalFileName): string
    {
        $pathInfo = pathinfo($originalFileName);
        $baseName = $pathInfo['filename'] ?? 'encoded';
        $extension = $pathInfo['extension'] ?? 'bin';
        
        return $baseName . '_encoded_' . time() . '.' . $extension;
    }
}
