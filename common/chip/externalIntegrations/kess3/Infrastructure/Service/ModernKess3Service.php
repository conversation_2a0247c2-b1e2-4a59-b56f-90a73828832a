<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Infrastructure\Service;

use common\chip\alientech\interfaces\Kess3ServiceInterface;
use common\chip\externalIntegrations\kess3\Infrastructure\Bridge\LegacyBridge;
use common\models\ProjectFiles;
use Yii;

/**
 * Модернизированный Kess3Service с новой архитектурой
 * Заменяет старый common\chip\alientech\services\Kess3Service
 * 
 * Использует новую архитектуру, но сохраняет совместимость интерфейса
 */
final readonly class ModernKess3Service implements Kess3ServiceInterface
{
    public function __construct(
        private LegacyBridge $legacyBridge
    ) {
    }

    /**
     * Запустить декодирование файла
     * Совместимый метод с оригинальным Kess3Service::start()
     */
    public function start(ProjectFiles $file): bool
    {
        Yii::info(
            message: "Starting modern Kess3 decoding for file {$file->id}",
            category: 'kess3.modern_service'
        );

        try {
            // Проверяем, подходит ли файл для Kess3
            if (!$this->legacyBridge->isKess3Project($file)) {
                Yii::warning(
                    message: "File {$file->id} is not suitable for Kess3 decoding",
                    category: 'kess3.modern_service'
                );
                return false;
            }

            return $this->legacyBridge->startDecoding($file);
        } catch (\Exception $e) {
            Yii::error(
                message: "Modern Kess3 service failed to start decoding: {$e->getMessage()}",
                category: 'kess3.modern_service'
            );
            
            return false;
        }
    }

    /**
     * Запустить декодирование (альтернативный метод)
     * Совместимый с оригинальным интерфейсом
     */
    public function startDecoding(ProjectFiles $file): bool
    {
        return $this->start($file);
    }

    /**
     * Запустить кодирование (заглушка для совместимости)
     * В новой архитектуре это будет отдельный сервис
     */
    public function startEncoding(int $projectId): bool
    {
        Yii::warning(
            message: "Encoding not implemented in modern service yet. Project ID: {$projectId}",
            category: 'kess3.modern_service'
        );
        
        // Временно возвращаем false, пока не реализовано кодирование
        return false;
    }

    /**
     * Проверить, может ли сервис обработать файл
     */
    public function canHandle(ProjectFiles $file): bool
    {
        return $this->legacyBridge->isKess3Project($file);
    }

    /**
     * Получить статистику по проекту
     */
    public function getProjectStatistics(int $projectId): array
    {
        try {
            return $this->legacyBridge->decodingFacade->getProjectStatistics($projectId);
        } catch (\Exception $e) {
            Yii::error(
                message: "Failed to get project statistics: {$e->getMessage()}",
                category: 'kess3.modern_service'
            );
            
            return [];
        }
    }

    /**
     * Получить операции по проекту
     */
    public function getProjectOperations(int $projectId): array
    {
        try {
            return $this->legacyBridge->decodingFacade->getProjectOperations($projectId);
        } catch (\Exception $e) {
            Yii::error(
                message: "Failed to get project operations: {$e->getMessage()}",
                category: 'kess3.modern_service'
            );
            
            return [];
        }
    }

    /**
     * Обработать callback от Alientech
     */
    public function processCallback(string $callbackData): bool
    {
        return $this->legacyBridge->processCallback($callbackData);
    }
}
