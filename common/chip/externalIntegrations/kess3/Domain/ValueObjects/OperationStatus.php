<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Domain\ValueObjects;

/**
 * Value Object для статуса операции
 */
final class OperationStatus
{
    public const IN_PROGRESS = 'in_progress';
    public const COMPLETED = 'completed';
    public const FAILED = 'failed';
    
    private function __construct(
        private readonly string $value
    ) {
        if (!in_array($value, [self::IN_PROGRESS, self::COMPLETED, self::FAILED], true)) {
            throw new \InvalidArgumentException("Invalid operation status: {$value}");
        }
    }
    
    public static function inProgress(): self
    {
        return new self(self::IN_PROGRESS);
    }
    
    public static function completed(): self
    {
        return new self(self::COMPLETED);
    }
    
    public static function failed(): self
    {
        return new self(self::FAILED);
    }
    
    public static function fromString(string $value): self
    {
        return new self($value);
    }
    
    public function getValue(): string
    {
        return $this->value;
    }
    
    public function isInProgress(): bool
    {
        return $this->value === self::IN_PROGRESS;
    }
    
    public function isCompleted(): bool
    {
        return $this->value === self::COMPLETED;
    }
    
    public function isSuccessful(): bool
    {
        return $this->value === self::COMPLETED;
    }
    
    public function isFailed(): bool
    {
        return $this->value === self::FAILED;
    }
    
    public function equals(OperationStatus $other): bool
    {
        return $this->value === $other->value;
    }
    
    public function __toString(): string
    {
        return $this->value;
    }
}
