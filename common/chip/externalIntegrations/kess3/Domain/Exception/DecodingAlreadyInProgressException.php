<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Domain\Exception;

/**
 * Исключение когда операция декодирования уже выполняется для файла
 */
final class DecodingAlreadyInProgressException extends DecodingDomainException
{
    public static function forFile(int $fileId, string $existingOperationId): self
    {
        return new self(
            message: "Decoding operation is already in progress for file {$fileId}",
            context: [
                'file_id' => $fileId,
                'existing_operation_id' => $existingOperationId
            ]
        );
    }
}
