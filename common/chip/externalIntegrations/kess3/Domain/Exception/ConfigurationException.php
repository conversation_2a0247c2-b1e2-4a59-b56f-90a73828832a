<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Domain\Exception;

/**
 * Исключение для ошибок конфигурации Alientech API
 */
class ConfigurationException extends ApiException
{
    public function __construct(
        private string $configKey,
        string $message = 'Configuration error',
        ?\Throwable $previous = null
    ) {
        $context = ['configKey' => $configKey];
        
        parent::__construct($message, 500, $previous, $context);
    }
    
    public function getConfigKey(): string
    {
        return $this->configKey;
    }
    
    /**
     * Создает исключение для отсутствующего ключа конфигурации
     */
    public static function missingKey(string $key): self
    {
        $message = sprintf('Missing configuration key: %s', $key);
        
        return new self($key, $message);
    }
    
    /**
     * Создает исключение для недействительного значения конфигурации
     */
    public static function invalidValue(string $key, mixed $value, string $expectedType = ''): self
    {
        $message = sprintf('Invalid configuration value for key "%s": %s', $key, var_export($value, true));
        
        if (!empty($expectedType)) {
            $message .= sprintf(' (expected: %s)', $expectedType);
        }
        
        return new self($key, $message);
    }
    
    /**
     * Создает исключение для неизвестного endpoint'а
     */
    public static function unknownEndpoint(string $endpoint): self
    {
        $message = sprintf('Unknown API endpoint: %s', $endpoint);
        
        return new self('endpoints.' . $endpoint, $message);
    }
    
    /**
     * Создает исключение для неизвестного типа операции
     */
    public static function unknownOperationType(string $type): self
    {
        $message = sprintf('Unknown operation type: %s', $type);
        
        return new self('operation_type', $message);
    }
    
    /**
     * Создает исключение для недействительного URL
     */
    public static function invalidUrl(string $key, string $url): self
    {
        $message = sprintf('Invalid URL in configuration key "%s": %s', $key, $url);
        
        return new self($key, $message);
    }
}
