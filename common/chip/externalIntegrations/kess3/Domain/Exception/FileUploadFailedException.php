<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Domain\Exception;

/**
 * Исключение при ошибке загрузки файла
 */
final class FileUploadFailedException extends EncodingException
{
    public function __construct(
        string $fileName,
        string $reason,
        ?\Throwable $previous = null
    ) {
        $message = "Failed to upload file '{$fileName}': {$reason}";
        parent::__construct($message, 0, $previous);
    }

    public static function fileNotFound(string $fileName): self
    {
        return new self($fileName, 'File not found');
    }

    public static function fileNotReadable(string $fileName): self
    {
        return new self($fileName, 'File is not readable');
    }

    public static function fileTooLarge(string $fileName, int $maxSize): self
    {
        return new self($fileName, "File size exceeds maximum allowed size of {$maxSize} bytes");
    }

    public static function invalidFileType(string $fileName, string $fileType): self
    {
        return new self($fileName, "Invalid file type: {$fileType}");
    }

    public static function networkError(string $fileName, string $error): self
    {
        return new self($fileName, "Network error: {$error}");
    }

    public static function serverError(string $fileName, int $statusCode): self
    {
        return new self($fileName, "Server error: HTTP {$statusCode}");
    }
}
