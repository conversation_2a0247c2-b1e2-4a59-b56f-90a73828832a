<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Domain\Exception;

/**
 * Исключение когда операция декодирования не найдена
 */
final class OperationNotFoundException extends DecodingDomainException
{
    public static function byId(string $operationId): self
    {
        return new self(
            message: "Decoding operation not found: {$operationId}",
            context: ['operation_id' => $operationId]
        );
    }

    public static function byExternalId(string $externalOperationId): self
    {
        return new self(
            message: "Decoding operation not found by external ID: {$externalOperationId}",
            context: ['external_operation_id' => $externalOperationId]
        );
    }
}
