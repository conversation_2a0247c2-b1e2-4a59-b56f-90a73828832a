<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Domain\Exception;

/**
 * Исключение для ошибок аутентификации Alientech API
 */
class AuthenticationException extends ApiException
{
    public function __construct(
        string $message = 'Authentication failed',
        ?\Throwable $previous = null,
        ?array $context = null
    ) {
        parent::__construct($message, 401, $previous, $context);
    }
    
    /**
     * Создает исключение для истекшего токена
     */
    public static function tokenExpired(): self
    {
        return new self('Access token has expired', null, ['reason' => 'token_expired']);
    }
    
    /**
     * Создает исключение для недействительного токена
     */
    public static function invalidToken(): self
    {
        return new self('Access token is invalid', null, ['reason' => 'invalid_token']);
    }
    
    /**
     * Создает исключение для неудачной аутентификации
     */
    public static function authenticationFailed(string $reason = ''): self
    {
        $message = 'Authentication failed';
        if (!empty($reason)) {
            $message .= ': ' . $reason;
        }
        
        return new self($message, null, ['reason' => $reason ?: 'unknown']);
    }
    
    /**
     * Создает исключение для недействительных учетных данных
     */
    public static function invalidCredentials(): self
    {
        return new self('Invalid credentials provided', null, ['reason' => 'invalid_credentials']);
    }
}
