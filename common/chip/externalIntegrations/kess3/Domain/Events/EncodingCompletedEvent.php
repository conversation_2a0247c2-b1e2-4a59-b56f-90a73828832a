<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Domain\Events;

use common\chip\event\core\BaseEvent;

/**
 * Событие завершения энкодинга файла
 */
class EncodingCompletedEvent extends BaseEvent
{
    public const TYPE = 'kess3.encoding.completed';

    public function __construct(
        private readonly string $operationId,
        private readonly int $projectId,
        private readonly string $fileName,
        private readonly \DateTimeImmutable $completedAt,
        private readonly bool $successful,
        private readonly ?string $error = null,
        private readonly ?array $result = null,
        array $contextData = []
    ) {
        parent::__construct($operationId, array_merge($contextData, [
            'operationId' => $operationId,
            'projectId' => $projectId,
            'fileName' => $fileName,
            'completedAt' => $completedAt->format(DATE_ATOM),
            'successful' => $successful,
            'error' => $error,
            'result' => $result
        ]));
    }

    public function getType(): string
    {
        return self::TYPE;
    }

    public function getDescription(): string
    {
        $status = $this->successful ? 'успешно завершено' : 'завершено с ошибкой';
        return "Энкодирование файла {$status} (проект {$this->projectId})";
    }

    public function getOperationId(): string
    {
        return $this->operationId;
    }

    public function getProjectId(): int
    {
        return $this->projectId;
    }

    public function getFileName(): string
    {
        return $this->fileName;
    }

    public function getCompletedAt(): \DateTimeImmutable
    {
        return $this->completedAt;
    }

    public function isSuccessful(): bool
    {
        return $this->successful;
    }

    public function getError(): ?string
    {
        return $this->error;
    }

    public function getResult(): ?array
    {
        return $this->result;
    }

    public function getNoticeTitle(): string
    {
        return $this->successful
            ? 'Энкодинг завершён успешно'
            : 'Ошибка при энкодинге файла';
    }

    public function getNoticeContent(): string
    {
        if ($this->successful) {
            return "Файл '{$this->fileName}' успешно закодирован для проекта #{$this->projectId}.";
        }
        return "Ошибка при энкодинге файла '{$this->fileName}' (проект #{$this->projectId}): " . ($this->error ?? 'Неизвестная ошибка.');
    }
}
