<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Domain\ValueObject;

use InvalidArgumentException;

/**
 * Value Object для идентификатора операции декодирования
 */
final readonly class OperationId
{
    public function __construct(
        private string $value
    ) {
        $this->validate();
    }

    public static function fromString(string $value): self
    {
        return new self($value);
    }

    public static function generate(): self
    {
        return new self(\Yii::$app->security->generateRandomString(36));
    }

    public function getValue(): string
    {
        return $this->value;
    }

    public function equals(self $other): bool
    {
        return $this->value === $other->value;
    }

    public function isEmpty(): bool
    {
        return empty($this->value);
    }

    public function __toString(): string
    {
        return $this->value;
    }

    private function validate(): void
    {
        if (empty($this->value)) {
            throw new InvalidArgumentException('Operation ID cannot be empty');
        }

        if (strlen($this->value) > 255) {
            throw new InvalidArgumentException('Operation ID cannot be longer than 255 characters');
        }
    }
}
