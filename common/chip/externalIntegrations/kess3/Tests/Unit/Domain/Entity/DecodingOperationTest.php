<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Tests\Unit\Domain\Entity;

use common\chip\externalIntegrations\kess3\Domain\Entity\DecodingOperation;
use common\chip\externalIntegrations\kess3\Domain\ValueObject\DecodingStatus;
use common\chip\externalIntegrations\kess3\Domain\ValueObject\FileId;
use common\chip\externalIntegrations\kess3\Domain\ValueObject\OperationId;
use common\chip\externalIntegrations\kess3\Domain\ValueObject\ProjectId;
use PHPUnit\Framework\TestCase;

/**
 * Пример Unit тестов для доменной сущности DecodingOperation
 * 
 * Показывает, как тестировать бизнес-логику в изоляции
 */
final class DecodingOperationTest extends TestCase
{
    public function test_create_operation_with_valid_data(): void
    {
        // Arrange
        $operationId = OperationId::generate();
        $projectId = ProjectId::fromInt(123);
        $fileId = FileId::fromInt(456);
        $filePath = '/path/to/file.bin';
        $callbackUrl = 'https://example.com/callback';

        // Act
        $operation = DecodingOperation::create(
            operationId: $operationId,
            projectId: $projectId,
            fileId: $fileId,
            filePath: $filePath,
            callbackUrl: $callbackUrl
        );

        // Assert
        $this->assertEquals($operationId, $operation->getOperationId());
        $this->assertEquals($projectId, $operation->getProjectId());
        $this->assertEquals($fileId, $operation->getFileId());
        $this->assertEquals($filePath, $operation->getFilePath());
        $this->assertEquals($callbackUrl, $operation->getCallbackUrl());
        $this->assertTrue($operation->getStatus()->isInProgress());
        $this->assertFalse($operation->isStarted());
        $this->assertFalse($operation->isCompleted());
        $this->assertFalse($operation->isFailed());
    }

    public function test_start_operation_changes_state(): void
    {
        // Arrange
        $operation = $this->createOperation();
        $externalOperationId = 'ext-123';
        $slotGuid = 'slot-456';

        // Act
        $operation->start($externalOperationId, $slotGuid);

        // Assert
        $this->assertTrue($operation->isStarted());
        $this->assertEquals($externalOperationId, $operation->getExternalOperationId());
        $this->assertEquals($slotGuid, $operation->getSlotGuid());
        $this->assertNotNull($operation->getStartedAt());
    }

    public function test_complete_operation_with_result(): void
    {
        // Arrange
        $operation = $this->createOperation();
        $operation->start('ext-123', 'slot-456');
        $result = ['decoded_files' => ['file1.bin', 'file2.bin']];

        // Act
        $operation->complete($result);

        // Assert
        $this->assertTrue($operation->isCompleted());
        $this->assertTrue($operation->isFinished());
        $this->assertFalse($operation->isFailed());
        $this->assertEquals($result, $operation->getResult());
        $this->assertNull($operation->getError());
        $this->assertNotNull($operation->getCompletedAt());
    }

    public function test_fail_operation_with_error(): void
    {
        // Arrange
        $operation = $this->createOperation();
        $operation->start('ext-123', 'slot-456');
        $error = ['message' => 'Decoding failed', 'code' => 500];

        // Act
        $operation->fail($error);

        // Assert
        $this->assertTrue($operation->isFailed());
        $this->assertTrue($operation->isFinished());
        $this->assertFalse($operation->isCompleted());
        $this->assertEquals($error, $operation->getError());
        $this->assertNull($operation->getResult());
        $this->assertNotNull($operation->getCompletedAt());
    }

    public function test_cancel_operation(): void
    {
        // Arrange
        $operation = $this->createOperation();

        // Act
        $operation->cancel();

        // Assert
        $this->assertTrue($operation->getStatus()->isCancelled());
        $this->assertTrue($operation->isFinished());
        $this->assertNotNull($operation->getCompletedAt());
    }

    public function test_cannot_start_operation_when_not_in_progress(): void
    {
        // Arrange
        $operation = $this->createOperation();
        $operation->complete(['result' => 'success']);

        // Act & Assert
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Operation can only be started when in progress');
        
        $operation->start('ext-123', 'slot-456');
    }

    public function test_cannot_complete_operation_when_not_in_progress(): void
    {
        // Arrange
        $operation = $this->createOperation();
        $operation->fail(['error' => 'failed']);

        // Act & Assert
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Operation can only be completed when in progress');
        
        $operation->complete(['result' => 'success']);
    }

    public function test_cannot_fail_finished_operation(): void
    {
        // Arrange
        $operation = $this->createOperation();
        $operation->complete(['result' => 'success']);

        // Act & Assert
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Cannot fail an already finished operation');
        
        $operation->fail(['error' => 'failed']);
    }

    public function test_cannot_cancel_finished_operation(): void
    {
        // Arrange
        $operation = $this->createOperation();
        $operation->complete(['result' => 'success']);

        // Act & Assert
        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Cannot cancel an already finished operation');
        
        $operation->cancel();
    }

    private function createOperation(): DecodingOperation
    {
        return DecodingOperation::create(
            operationId: OperationId::generate(),
            projectId: ProjectId::fromInt(123),
            fileId: FileId::fromInt(456),
            filePath: '/path/to/file.bin',
            callbackUrl: 'https://example.com/callback'
        );
    }
}
