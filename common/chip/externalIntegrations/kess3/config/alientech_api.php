<?php

declare(strict_types=1);

/**
 * Конфигурация Alientech API для KESS3
 */
return [
    // Базовые настройки API
    'base_url' => $_ENV['ALIENTECH_API_URL'] ?? 'https://api.alientech.to',
    
    // Код клиента
    'customer_code' => $_ENV['ALIENTECH_CUSTOMER_CODE'] ?? 'user037',
    
    // Настройки аутентификации
    'auth' => [
        'client_application_guid' => $_ENV['ALIENTECH_CLIENT_GUID'] ?? '',
        'secret_key' => $_ENV['ALIENTECH_SECRET_KEY'] ?? '',
        'token_cache_duration' => 3600, // секунды
        'token_refresh_threshold' => 300, // обновлять токен за 5 минут до истечения
    ],
    
    // Callback URL'ы для различных операций
    'callbacks' => [
        'decode' => '/api/kess3-decoded',
        'encode_obd' => '/api/kess3-encoded-obd',
        'encode_boot' => '/api/kess3-encoded-boot',
    ],
    
    // API endpoints
    'endpoints' => [
        'auth' => '/api/auth',
        'decode' => '/api/kess3/decode-read-file/{customerCode}',
        'encode_obd' => '/api/kess3/encode-obd/{customerCode}',
        'encode_boot' => '/api/kess3/encode-boot/{customerCode}',
        'status' => '/api/async-operations/{guid}',
        'download' => '/api/files/{guid}',
        'slots' => '/api/kess3/file-slots',
        'close_slot' => '/api/kess3/file-slots/{guid}/close',
        'reopen_slot' => '/api/kess3/file-slots/{guid}/reopen',
    ],
    
    // Настройки таймаутов (в секундах)
    'timeouts' => [
        'default' => 30,
        'upload' => 120,
        'download' => 60,
        'auth' => 10,
        'slots' => 15,
    ],
    
    // Настройки повторных попыток
    'retry' => [
        'max_attempts' => 3,
        'delay' => 1000, // миллисекунды
        'backoff_multiplier' => 2.0,
        'retry_on_status' => [500, 502, 503, 504], // HTTP статусы для повтора
    ],
    
    // Настройки слотов
    'slots' => [
        'max_slots' => 3,
        'cache_duration' => 60, // секунды
        'auto_close_expired' => true,
        'expiry_minutes' => 30,
        'check_interval' => 300, // секунды между проверками
    ],
    
    // Настройки логирования
    'logging' => [
        'enabled' => true,
        'log_requests' => true,
        'log_responses' => true,
        'log_auth_details' => false, // не логировать токены и секреты
        'max_body_length' => 1024, // максимальная длина тела запроса/ответа для логирования
    ],
    
    // Настройки кеширования
    'cache' => [
        'enabled' => true,
        'prefix' => 'kess3_alientech_',
        'auth_token_key' => 'auth_token',
        'slots_key' => 'file_slots',
    ],
];
