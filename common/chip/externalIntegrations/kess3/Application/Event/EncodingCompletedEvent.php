<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Application\Event;

use common\chip\event\core\BaseEvent;

/**
 * Событие завершения энкодирования
 */
final class EncodingCompletedEvent extends BaseEvent
{
    public function __construct(
        private readonly string $operationId,
        private readonly int $projectId,
        private readonly array $fileIds,
        private readonly array $result,
        private readonly string $externalOperationId = ''
    ) {
        parent::__construct($operationId, [
            'operation_id' => $operationId,
            'project_id' => $projectId,
            'file_ids' => $fileIds,
            'result' => $result,
            'external_operation_id' => $externalOperationId,
        ]);
    }

    public function getType(): string
    {
        return 'kess3.encoding.completed';
    }

    public function getDescription(): string
    {
        $filesCount = count($this->fileIds);
        return sprintf(
            'Encoding completed for project %d, %d files (operation: %s)',
            $this->projectId,
            $filesCount,
            $this->operationId
        );
    }

    public function getOperationId(): string
    {
        return $this->operationId;
    }

    public function getProjectId(): int
    {
        return $this->projectId;
    }

    public function getFileIds(): array
    {
        return $this->fileIds;
    }

    public function getResult(): array
    {
        return $this->result;
    }

    public function getExternalOperationId(): string
    {
        return $this->externalOperationId;
    }

    public function isSuccessful(): bool
    {
        return !empty($this->result);
    }
}
