<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Application\Service;

use common\chip\externalIntegrations\kess3\Application\Command\ProcessEncodingResultCommand;
use common\chip\externalIntegrations\kess3\Application\Command\StartEncodingCommand;
use common\chip\externalIntegrations\kess3\Application\Handler\ProcessEncodingResultHandler;
use common\chip\externalIntegrations\kess3\Application\Handler\StartEncodingHandler;
use common\chip\externalIntegrations\kess3\Domain\Entity\EncodingOperation;
use common\chip\externalIntegrations\kess3\Domain\Repository\EncodingOperationRepositoryInterface;
use common\chip\externalIntegrations\kess3\Domain\Service\EncodingDomainService;
use common\chip\externalIntegrations\kess3\Domain\ValueObject\OperationId;
use common\chip\externalIntegrations\kess3\Domain\ValueObject\ProjectId;

/**
 * Фасад для операций энкодирования Kess3
 * Упрощает использование системы энкодирования
 */
final readonly class EncodingFacade
{
    public function __construct(
        private StartEncodingHandler $startEncodingHandler,
        private ProcessEncodingResultHandler $processEncodingResultHandler,
        private EncodingOperationRepositoryInterface $repository,
        private EncodingDomainService $domainService
    ) {
    }

    /**
     * Запустить энкодирование проекта
     */
    public function startEncoding(
        int $projectId,
        string $service = 'alientech',
        string $callbackUrl = ''
    ): EncodingOperation {
        $command = StartEncodingCommand::create(
            projectId: $projectId,
            service: $service,
            callbackUrl: $callbackUrl
        );

        return $this->startEncodingHandler->handle($command);
    }

    /**
     * Обработать результат энкодирования из callback
     */
    public function processEncodingResult(array $callbackData): EncodingOperation
    {
        $command = ProcessEncodingResultCommand::fromCallbackData($callbackData);
        return $this->processEncodingResultHandler->handle($command);
    }

    /**
     * Получить статус операции энкодирования
     */
    public function getOperationStatus(string $operationId): ?EncodingOperation
    {
        return $this->repository->findById(OperationId::fromString($operationId));
    }

    /**
     * Получить операции энкодирования по проекту
     */
    public function getProjectOperations(int $projectId): array
    {
        return $this->repository->findByProjectId(ProjectId::fromInt($projectId));
    }

    /**
     * Получить операции в процессе выполнения
     */
    public function getInProgressOperations(): array
    {
        return $this->repository->findInProgress();
    }

    /**
     * Отменить операцию энкодирования
     */
    public function cancelOperation(string $operationId): void
    {
        $operation = $this->repository->findById(OperationId::fromString($operationId));
        
        if (!$operation) {
            throw new \RuntimeException("Encoding operation not found: {$operationId}");
        }

        if ($operation->isFinished()) {
            throw new \DomainException("Cannot cancel finished operation: {$operationId}");
        }

        $operation->cancel();
        $this->repository->save($operation);
    }

    /**
     * Отменить просроченные операции
     */
    public function cancelExpiredOperations(int $timeoutMinutes = 30): int
    {
        return $this->domainService->cancelExpiredOperations($timeoutMinutes);
    }

    /**
     * Получить статистику операций по проекту
     */
    public function getProjectStatistics(int $projectId): array
    {
        return $this->domainService->getProjectStatistics(ProjectId::fromInt($projectId));
    }
}
