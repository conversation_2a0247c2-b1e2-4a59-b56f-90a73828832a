<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Application\Service;

use common\chip\event\EventDispatcher;
use common\chip\externalIntegrations\kess3\Application\Command\ProcessDecodingResultCommand;
use common\chip\externalIntegrations\kess3\Application\Command\StartDecodingCommand;
use common\chip\externalIntegrations\kess3\Application\Handler\ProcessDecodingResultHandler;
use common\chip\externalIntegrations\kess3\Application\Handler\StartDecodingHandler;
use common\chip\externalIntegrations\kess3\Domain\Entity\DecodingOperation;
use common\chip\externalIntegrations\kess3\Domain\Repository\DecodingOperationRepositoryInterface;
use common\chip\externalIntegrations\kess3\Domain\Service\DecodingDomainService;
use common\chip\externalIntegrations\kess3\Domain\ValueObject\OperationId;
use common\chip\externalIntegrations\kess3\Infrastructure\Repository\DecodingOperationRepository;

/**
 * Фасад для операций декодирования Kess3
 * Упрощает использование системы декодирования
 */
final readonly class Kess3DecodingFacade
{
    public function __construct(
        private StartDecodingHandler $startDecodingHandler,
        private ProcessDecodingResultHandler $processResultHandler,
        private DecodingOperationRepositoryInterface $repository
    ) {
    }

    /**
     * Запустить декодирование файла
     */
    public function startDecoding(
        int $projectId,
        int $fileId,
        string $filePath,
        string $callbackUrl = ''
    ): DecodingOperation {
        $command = StartDecodingCommand::create(
            projectId: $projectId,
            fileId: $fileId,
            filePath: $filePath,
            callbackUrl: $callbackUrl
        );

        return $this->startDecodingHandler->handle($command);
    }

    /**
     * Обработать результат декодирования из callback
     */
    public function processDecodingResult(array $callbackData): ?DecodingOperation
    {
        $command = ProcessDecodingResultCommand::fromCallbackData($callbackData);
        return $this->processResultHandler->handle($command);
    }

    /**
     * Получить операцию по ID
     */
    public function getOperation(string $operationId): ?DecodingOperation
    {
        return $this->repository->findById(OperationId::fromString($operationId));
    }
}
