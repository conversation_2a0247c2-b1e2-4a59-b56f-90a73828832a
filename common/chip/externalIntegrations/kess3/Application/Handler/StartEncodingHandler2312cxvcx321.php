<?php
declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Application\Handler;

use common\chip\externalIntegrations\kess3\Application\Command\StartEncodingCommand;
use common\chip\externalIntegrations\kess3\Application\Validator\EncodingRequestValidator;
use common\chip\externalIntegrations\kess3\Domain\Repository\EncodingOperationRepositoryInterface;
use common\chip\externalIntegrations\kess3\Infrastructure\Service\ModernEncodingService;


final class StartEncodingHandler2312321
{
    public function __construct(
        private readonly EncodingRequestValidator $validator,
        private readonly ModernEncodingService $encodingService,
        private readonly EncodingOperationRepositoryInterface $repository
    ) {}

    public function handle(StartEncodingCommand $command): void
    {
        $validation = $this->validator->validate($command);
        if (!$validation->isValid()) {
            throw new \InvalidArgumentException('Validation failed: ' . implode(', ', $validation->getErrors()));
        }
        $operation = $this->encodingService->startEncoding($command);
        $this->repository->save($operation);
    }
}
