<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Application\Command;

/**
 * Команда для запуска процесса декодирования
 */
final readonly class StartDecodingCommand
{
    public function __construct(
        public int $projectId,
        public int $fileId,
        public string $filePath,
        public string $callbackUrl = ''
    ) {
    }

    public static function create(
        int $projectId,
        int $fileId,
        string $filePath,
        string $callbackUrl = ''
    ): self {
        return new self($projectId, $fileId, $filePath, $callbackUrl);
    }
}
