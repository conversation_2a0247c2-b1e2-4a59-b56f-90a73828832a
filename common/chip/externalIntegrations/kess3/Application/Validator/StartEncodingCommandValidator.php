<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Application\Validator;

use common\chip\externalIntegrations\kess3\Application\Command\StartEncodingCommand;
use common\chip\externalIntegrations\kess3\Infrastructure\Factory\EncodingAdapterFactory;
use common\models\Projects;
use common\models\ProjectFiles;
use common\helpers\ProjectHelper;

/**
 * Валидатор команды запуска энкодирования
 */
final class StartEncodingCommandValidator
{
    public function __construct(
        private readonly EncodingAdapterFactory $adapterFactory
    ) {}

    /**
     * Валидировать команду запуска энкодирования
     *
     * @param StartEncodingCommand $command
     * @return ValidationResult
     */
    public function validate(StartEncodingCommand $command): ValidationResult
    {
        $errors = [];

        // Проверка projectId
        if ($command->projectId <= 0) {
            $errors['projectId'] = 'Project ID must be a positive integer';
        } else {
            // Проверка существования проекта
            $project = Projects::findOne($command->projectId);
            if (!$project) {
                $errors['projectId'] = "Project not found: {$command->projectId}";
            } else {
                // Проверка готовности проекта к энкодированию
                $projectValidation = $this->validateProject($project);
                if (!$projectValidation->isValid()) {
                    $errors['project'] = $projectValidation->getErrors();
                }
            }
        }

        // Проверка service
        if (empty($command->service)) {
            $errors['service'] = 'Service is required';
        } elseif (!EncodingAdapterFactory::isSupported($command->service)) {
            $errors['service'] = "Unsupported service: {$command->service}. Supported: " . 
                implode(', ', EncodingAdapterFactory::getSupportedAdapters());
        } elseif (!$this->adapterFactory->isAdapterAvailable($command->service)) {
            $errors['service'] = "Service is not available: {$command->service}";
        }

        // Проверка callbackUrl (если указан)
        if (!empty($command->callbackUrl) && !filter_var($command->callbackUrl, FILTER_VALIDATE_URL)) {
            $errors['callbackUrl'] = 'Invalid callback URL format';
        }

        return new ValidationResult(empty($errors), $errors);
    }

    /**
     * Валидировать готовность проекта к энкодированию
     *
     * @param Projects $project
     * @return ValidationResult
     */
    private function validateProject(Projects $project): ValidationResult
    {
        $errors = [];

        // Проверка статуса проекта
        if ($project->status !== 'decoded') {
            $errors['status'] = "Project must be decoded before encoding. Current status: {$project->status}";
        }

        // Проверка наличия модифицированных файлов
        $modifiedFiles = ProjectFiles::find()
            ->where([
                'project_id' => $project->id,
                'file_type' => ProjectHelper::FILE_TYPE_MODIFIED_DECODED,
                'isDeleted' => 0
            ])
            ->andWhere(['!=', 'path', ''])
            ->all();

        if (empty($modifiedFiles)) {
            $errors['files'] = 'No modified files found for encoding';
        } else {
            // Проверка каждого файла
            $fileErrors = [];
            foreach ($modifiedFiles as $file) {
                $fileValidation = $this->validateProjectFile($file);
                if (!$fileValidation->isValid()) {
                    $fileErrors[$file->id] = $fileValidation->getErrors();
                }
            }
            
            if (!empty($fileErrors)) {
                $errors['fileValidation'] = $fileErrors;
            }
        }

        // Проверка отсутствия текущих операций энкодирования
        $activeOperations = \common\models\AlientechAsyncOperation::find()
            ->where([
                'project_id' => $project->id,
                'operation_type' => 'encoding',
                'status' => \common\chip\externalIntegrations\kess3\Domain\ValueObject\EncodingStatus::IN_PROGRESS
            ])
            ->count();

        if ($activeOperations > 0) {
            $errors['operations'] = 'Project has active encoding operations';
        }

        return new ValidationResult(empty($errors), $errors);
    }

    /**
     * Валидировать файл проекта
     *
     * @param ProjectFiles $file
     * @return ValidationResult
     */
    private function validateProjectFile(ProjectFiles $file): ValidationResult
    {
        $errors = [];

        // Проверка существования файла
        if (!file_exists($file->path)) {
            $errors['path'] = "File not found: {$file->path}";
        } else {
            // Проверка размера файла
            $fileSize = filesize($file->path);
            if ($fileSize === false) {
                $errors['size'] = "Cannot determine file size: {$file->path}";
            } elseif ($fileSize === 0) {
                $errors['size'] = "File is empty: {$file->path}";
            } elseif ($fileSize > 100 * 1024 * 1024) { // 100MB
                $errors['size'] = "File too large (max 100MB): {$file->path}";
            }

            // Проверка прав доступа
            if (!is_readable($file->path)) {
                $errors['permissions'] = "File is not readable: {$file->path}";
            }
        }

        // Проверка параметров файла
        $params = $file->params ? json_decode($file->params, true) : [];
        
        if (empty($params['kess3FileSlotGUID'])) {
            $errors['params'] = 'Missing kess3FileSlotGUID parameter';
        }
        
        if (empty($params['fileType'])) {
            $errors['params'] = 'Missing fileType parameter';
        } elseif (!$this->isValidFileType($params['fileType'])) {
            $errors['params'] = "Invalid fileType: {$params['fileType']}";
        }

        return new ValidationResult(empty($errors), $errors);
    }

    /**
     * Проверить корректность типа файла
     *
     * @param string $fileType
     * @return bool
     */
    private function isValidFileType(string $fileType): bool
    {
        $validTypes = [
            'OBDDecoded',
            'BootBenchDecodedMicro',
            'BootBenchDecodedFlash',
            'BootBenchDecodedEEPROM',
            'BootBenchDecodedMapFile',
        ];

        return in_array($fileType, $validTypes);
    }

    /**
     * Быстрая валидация (без глубокой проверки файлов)
     *
     * @param StartEncodingCommand $command
     * @return ValidationResult
     */
    public function quickValidate(StartEncodingCommand $command): ValidationResult
    {
        $errors = [];

        if ($command->projectId <= 0) {
            $errors['projectId'] = 'Project ID must be a positive integer';
        }

        if (empty($command->service)) {
            $errors['service'] = 'Service is required';
        } elseif (!EncodingAdapterFactory::isSupported($command->service)) {
            $errors['service'] = "Unsupported service: {$command->service}";
        }

        if (!empty($command->callbackUrl) && !filter_var($command->callbackUrl, FILTER_VALIDATE_URL)) {
            $errors['callbackUrl'] = 'Invalid callback URL format';
        }

        return new ValidationResult(empty($errors), $errors);
    }
}
