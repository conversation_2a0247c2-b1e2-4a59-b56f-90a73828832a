<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Application\Validator;

use common\chip\externalIntegrations\kess3\Application\Command\StartDecodingCommand;

/**
 * Валидатор для команды запуска декодирования
 */
final class StartDecodingCommandValidator
{
    /**
     * Валидировать команду запуска декодирования
     * 
     * @return string[] Массив ошибок валидации
     */
    public function validate(StartDecodingCommand $command): array
    {
        $errors = [];

        // Проверка ID проекта
        if ($command->projectId <= 0) {
            $errors[] = 'Project ID must be greater than 0';
        }

        // Проверка ID файла
        if ($command->fileId <= 0) {
            $errors[] = 'File ID must be greater than 0';
        }

        // Проверка пути к файлу
        if (empty($command->filePath)) {
            $errors[] = 'File path cannot be empty';
        } elseif (!file_exists($command->filePath)) {
            $errors[] = "File does not exist: {$command->filePath}";
        } elseif (!is_readable($command->filePath)) {
            $errors[] = "File is not readable: {$command->filePath}";
        } else {
            // Проверка размера файла
            $fileSize = filesize($command->filePath);
            if ($fileSize === false) {
                $errors[] = "Cannot determine file size: {$command->filePath}";
            } elseif ($fileSize === 0) {
                $errors[] = "File is empty: {$command->filePath}";
            } elseif ($fileSize > $this->getMaxFileSize()) {
                $maxSizeMB = $this->getMaxFileSize() / 1024 / 1024;
                $errors[] = "File is too large. Maximum size: {$maxSizeMB}MB";
            }
        }

        // Проверка callback URL (если указан)
        if (!empty($command->callbackUrl) && !filter_var($command->callbackUrl, FILTER_VALIDATE_URL)) {
            $errors[] = 'Invalid callback URL format';
        }

        return $errors;
    }

    /**
     * Проверить, валидна ли команда (без ошибок)
     */
    public function isValid(StartDecodingCommand $command): bool
    {
        return empty($this->validate($command));
    }

    /**
     * Получить максимальный размер файла в байтах
     */
    private function getMaxFileSize(): int
    {
        // 100MB по умолчанию
        return 100 * 1024 * 1024;
    }
}
