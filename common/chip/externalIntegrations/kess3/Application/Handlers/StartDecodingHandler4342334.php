<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Application\Handlers;

use common\chip\event\EventDispatcher;
use common\chip\externalIntegrations\kess3\Application\Commands\StartDecodingCommand;
use common\chip\externalIntegrations\kess3\Domain\Entities\DecodingOperation;
use common\chip\externalIntegrations\kess3\Domain\Events\DecodingStartedEvent;
use common\chip\externalIntegrations\kess3\Domain\ValueObjects\UserInfo;
use common\chip\externalIntegrations\kess3\Interfaces\DecodingProviderInterface;
use common\chip\externalIntegrations\kess3\Interfaces\OperationRepositoryInterface;

/**
 * Обработчик команды начала декодинга
 */
final class StartDecodingHandler4342334
{
    public function __construct(
        private readonly DecodingProviderInterface $decodingProvider,
        private readonly OperationRepositoryInterface $operationRepository,
        private readonly EventDispatcher $eventDispatcher
    ) {}
    
    public function handle(StartDecodingCommand $command): string
    {
        // Проверяем доступность провайдера
        if (!$this->decodingProvider->isAvailable()) {
            throw new \RuntimeException('Decoding provider is not available');
        }
        
        // Создаем UserInfo из команды
        $userInfo = UserInfo::create(
            projectId: $command->getProjectId(),
            fileId: $command->getFileId(),
            userId: $command->getUserId(),
            additionalData: $command->getAdditionalData()
        );
        
        // Начинаем декодинг через провайдера
        $providerOperation = $this->decodingProvider->startDecoding(
            filePath: $command->getFilePath(),
            userInfo: $userInfo->toArray()
        );
        
        // Создаем доменную сущность операции
        $operation = DecodingOperation::create(
            id: $providerOperation->getId(),
            projectId: $command->getProjectId(),
            fileId: $command->getFileId(),
            userInfo: $userInfo
        );
        
        // Сохраняем операцию
        $this->operationRepository->save($operation);
        
        // Получаем имя файла из пути для события
        $fileName = basename($command->getFilePath());
        
        // Отправляем событие о начале декодинга
        $event = new DecodingStartedEvent(
            operationId: $operation->getId(),
            projectId: $operation->getProjectId(),
            fileId: $operation->getFileId(),
            fileName: $fileName
        );
        
        $this->eventDispatcher->dispatch(
            event: $event,
            entityType: 'project',
            entityId: $operation->getProjectId(),
            createdBy: $operation->getUserInfo()['userId'] ?? null
        );
        
        return $operation->getId();
    }
}
