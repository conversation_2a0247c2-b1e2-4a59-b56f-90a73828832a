<?php

declare(strict_types=1);

namespace common\chip\externalIntegrations\kess3\Application\Services;

use common\chip\externalIntegrations\kess3\Application\Commands\StartDecodingCommand332223;
use common\chip\externalIntegrations\kess3\Application\Handlers\StartDecodingHandler4342334;
use common\chip\externalIntegrations\kess3\Interfaces\DecodingOperationInterface;
use common\chip\externalIntegrations\kess3\Interfaces\OperationRepositoryInterface;

/**
 * Основной сервис приложения для работы с декодингом
 */
final class DecodingApplicationService233232323
{
    public function __construct(
        private readonly StartDecodingHandler4342334  $startDecodingHandler,
        private readonly OperationRepositoryInterface $operationRepository
    ) {}
    
    /**
     * Начать декодинг файла
     */
    public function startDecoding(
        string $filePath,
        int $projectId,
        int $fileId,
        ?int $userId = null,
        array $additionalData = []
    ): string {
        $command = new StartDecodingCommand332223(
            filePath: $filePath,
            projectId: $projectId,
            fileId: $fileId,
            userId: $userId,
            additionalData: $additionalData
        );
        
        return $this->startDecodingHandler->handle($command);
    }
    
    /**
     * Получить операцию по ID
     */
    public function getOperation(string $operationId): ?DecodingOperationInterface
    {
        return $this->operationRepository->findById($operationId);
    }
    
    /**
     * Получить операции по проекту
     */
    public function getOperationsByProject(int $projectId): array
    {
        return $this->operationRepository->findByProjectId($projectId);
    }
    
    /**
     * Получить незавершенные операции
     */
    public function getIncompleteOperations(): array
    {
        return $this->operationRepository->findIncomplete();
    }
}
