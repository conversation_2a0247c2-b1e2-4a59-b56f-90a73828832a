<?php
namespace common\chip\alientech\services;

use common\chip\alientech\entities\exceptions\AlientechNotAuthException;
use common\chip\alientech\interfaces\Kess3ServiceInterface;
use common\chip\alientech\jobs\AlientechStartDecodingJob;
use common\models\AlientechAsyncOperation;
use common\models\ProjectFiles;
use Yii;
use yii\base\InvalidConfigException;
use yii\httpclient\Exception;

class Kess3Service implements Kess3ServiceInterface
{
    public LogService $logService;
    private AsyncOperationService $asyncOperationService;

    /**
     * Kess3Service constructor.
     */
    public function __construct(AsyncOperationService $asyncOperationService, LogService $logService)
    {
        $this->asyncOperationService = $asyncOperationService;
        $this->logService = $logService;
        $this->logService->info('Kess3Service __construct');
    }

    /**
     * @throws Exception
     * @throws InvalidConfigException
     * @throws AlientechNotAuthException
     */
    public function start(ProjectFiles $file): bool
    {
        $this->logService->info('Kess3Service start');
        $id = Yii::$app->queue->push(new AlientechStartDecodingJob(['file' => $file]));
        return (bool)$id;
    }

    /**
     * @throws Exception
     * @throws InvalidConfigException
     * @throws AlientechNotAuthException
     */
    public function startDecoding(ProjectFiles $file): bool
    {
        $this->logService->info('Kess3Service startDecoding');
        $operation = $this->asyncOperationService->startOperationDecode($file);
        dump($operation);
        if (!$operation || !$operation->getGuid()) {
            $this->logService->info('no_AsyncOperationDto');
            return false;
        }
        return true;
    }

    /**
     * @throws Exception
     * @throws InvalidConfigException
     * @throws AlientechNotAuthException
     */
    public function startEncoding(int $projectId): bool
    {
        $this->logService->info('Kess3Service startEncoding');

        $operation = $this->asyncOperationService->startOperationEncode($projectId);

        if (!$operation || !$operation->getGuid()) {
            $this->logService->info('no_AsyncOperationDto');
            return false;
        }

        return true;
    }

}