<?php
namespace common\chip\alientech\services;

use common\chip\alientech\entities\dto\AsyncEncodeOperationResultDto;
use common\chip\alientech\entities\dto\AsyncOperationDto;
use common\chip\alientech\entities\dto\AsyncOperationResultDto;
use common\chip\alientech\entities\exceptions\AlientechNotAuthException;
use common\chip\alientech\interfaces\AlientechServiceInterface;
use common\chip\alientech\jobs\AlientechDownloadDecodedFileJob;
use common\chip\alientech\jobs\AlientechDownloadEncodedFileJob;
use common\chip\alientech\repositories\AsyncOperationRepository;
use common\models\AlientechAsyncOperation;
use common\models\ProjectFiles;
use Yii;
use yii\base\InvalidConfigException;
use yii\httpclient\Exception;

class AsyncOperationService implements AlientechServiceInterface
{
    /**
     *
     */
    const TOO_MANY_OPEN_KESS3_FILE_SLOTS = '429';
    /**
     * @var AlientechLinkService
     */
    private AlientechLinkService $linkService;
    /**
     * @var LogService
     */
    public LogService $logService;
    /**
     * @var AlientechService
     */
    private AlientechService $alientechService;
    /**
     * @var AsyncOperationRepository
     */
    private AsyncOperationRepository $repository;
    /**
     * @var AlientechProjectService
     */
    private AlientechProjectService $projectService;
    /**
     * @var FileService
     */
    private FileService $fileService;
    /**
     * @var FileSlotService
     */
    private FileSlotService $fileSlotService;

    /**
     * @param AlientechLinkService $linkService
     * @param LogService $logService
     * @param AlientechService $alientechService
     * @param AlientechProjectService $projectService
     * @param FileService $fileService
     */
    public function __construct(AlientechLinkService $linkService, LogService $logService, AlientechService $alientechService, AlientechProjectService $projectService, FileService $fileService, FileSlotService $fileSlotService)
    {
        $this->linkService = $linkService;
        $this->logService = $logService;
        $this->logService->info('AsyncOperationService __construct');
        $this->alientechService = $alientechService;
        $this->projectService = $projectService;
        $this->fileService = $fileService;
        $this->fileSlotService = $fileSlotService;
        $this->repository = new AsyncOperationRepository();
    }

    /**
     * @throws \yii\base\InvalidConfigException
     * @throws Exception
     * @throws \common\chip\alientech\entities\exceptions\AlientechNotAuthException
     */
    public function startOperationDecode(ProjectFiles $file): ?AsyncOperationDto
    {
        $this->logService->info('AsyncOperationService startOperationDecode');

        $response = $this->linkService->processRequest()
            ->setMethod('POST')
            ->setFormat('urlencoded')
            ->setUrl(["/api/kess3/decode-read-file/".$this->alientechService->getCustomerCode(), "callbackURL" => $this->alientechService->getDecodeCallbackUrl()])
            ->addFile('readFile', $file->path)
            ->addData(['userInfo' => json_encode(['projectId' => $file->project_id, 'fileId' => $file->id])])
            ->send();
        dump($response);

        $this->linkService->processResponse($response);

        if ($this->linkService->getRespData() === self::TOO_MANY_OPEN_KESS3_FILE_SLOTS) {
            $this->fileSlotService->closeAllFileSlots();
            return $this->startOperationDecode($file);
        }

        $operation = $this->createOperationDtoByData($this->linkService->getRespData());

        $operation->setUserInfo(['projectId' => $file->project_id, 'fileId' => $file->id]);

        if (empty($operation->getGuid())) {
            $this->logService->log('no_operation_guid');
        }

        return $this->repository->createAsyncOperation($operation);
    }

    /**
     * @throws \common\chip\alientech\entities\exceptions\AlientechNotAuthException
     * @throws \yii\base\InvalidConfigException
     * @throws Exception
     */
    public function startOperationEncode(int $projectId): ?AsyncOperationDto
    {
        $this->logService->info('AsyncOperationService startOperationEncode');

        $files = $this->fileService->getFilesByProjectId($projectId);
        if (!$files) {
            $this->logService->info('AsyncOperationService startOperationEncode no_files');
            return null;
        }

        $filesUploaded = $this->fileService->uploadModifiedFiles($files);

        if ($filesUploaded['error']) {
            $this->logService->log($filesUploaded['message']);
            return null;
        }

//        !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
//        $filesUploaded['data'] = $files;
//        !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

        $asyncOperation = AlientechAsyncOperation::find()->where(['project_id' => $projectId])->one();

        if (is_null($asyncOperation)) {
            $this->logService->info('AsyncOperationService startOperationEncode no_operation');
            return null;
        }

        $resultDto = new AsyncOperationResultDto($asyncOperation->result);

        if (!$resultDto->getKess3FileSlotGUID()) {
            $this->logService->info('AsyncOperationService startOperationEncode no_operation_result');
            return null;
        }
        $encodeRequestData = $resultDto->generateEncodeRequestDataKeysArray($filesUploaded['data']);

        $filledRequestData = $resultDto->fillEncodeRequestDataArray($encodeRequestData, $filesUploaded['data']);
        if ($filledRequestData['error']) {
            $this->logService->info('AsyncOperationService startOperationEncode no_filled_data');
            return null;
        }

        $encodeRequestData = $filledRequestData['data'];
        $encodeRequestData['userCustomerCode'] = $this->alientechService->getCustomerCode();
        $encodeRequestData['kess3FileSlotGUID'] = $resultDto->getKess3FileSlotGUID();
        $encodeRequestData['callbackURL'] = $this->alientechService->getEncodeCallbackUrl();

        $response = $this->linkService->processRequest()
            ->setMethod('POST')
            ->setFormat('json')
            ->setUrl("/api/kess3".$resultDto->generateEncodeUrl())
            ->addData($encodeRequestData)
            ->send();

        $this->linkService->processResponse($response);

        $operation = $this->createOperationDtoByData($this->linkService->getRespData());

        $operation->setUserInfo(['projectId' => $projectId, 'fileId' => $asyncOperation->file_id]);

        if (empty($operation->getGuid())) {
            $this->logService->log('no_operation_guid');
        }

        return $this->repository->createAsyncOperation($operation);
    }

    /**
     * @param $data
     * @return AsyncOperationDto
     */
    public function createOperationDtoByData($data): AsyncOperationDto
    {
        $this->logService->info('AsyncOperationService createOperationDtoByData');
        $this->repository->buildAsyncOperationByData((object)$data);
        return $this->repository->getOperation();
    }

    /**
     * @param AsyncOperationDto $operation
     * @throws Exception
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\db\Exception
     */
    public function finishCompletedOperationDecode(AsyncOperationDto $operation): bool
    {
        $this->logService->info('AsyncOperationService finishCompletedOperationDecode');
        if (!empty($operation->getError())) {
            try {
                $this->projectService->processErrorOperationDecode($operation);
            } catch (\yii\db\Exception $e) {
                $this->logService->log($e->getMessage());
            }
            return false;
        }
        if ($operation->isSuccessful() && !$operation->isHasFailed()) {
            $this->projectService->processSuccessOperationDecode($operation);
            $id = Yii::$app->queue->push(new AlientechDownloadDecodedFileJob(['operation' => $operation]));
            return (bool)$id;
        }
        return false;
    }

    /**
     * @param AsyncOperationDto $operation
     * @throws Exception
     * @throws \yii\base\InvalidConfigException
     */
    public function finishCompletedOperationEncode(AsyncOperationDto $operation): bool
    {
        $this->logService->info('AsyncOperationService finishCompletedOperationEncode');
        if (!empty($operation->getError())) {
            $this->projectService->processErrorOperationEncode($operation);
            return false;
        }
        if ($operation->isSuccessful() && !$operation->isHasFailed()) {
            $this->projectService->processSuccessOperationEncode($operation);
            $id = Yii::$app->queue->push(new AlientechDownloadEncodedFileJob(['operation' => $operation]));
            return (bool)$id;
        }
        return false;
    }

    /**
     * @param AsyncOperationDto $operation
     * @return bool
     * @throws \Exception
     */
    public function downloadDecodedFiles(AsyncOperationDto $operation): bool
    {
        $this->logService->info('AsyncOperationService downloadDecodedFiles');
        $result = false;

        if (!$operation->getResult()) {
            $this->logService->info('no_operation_decoded_result');
            return false;
        }

        $operationResultDto = new AsyncOperationResultDto((object)$operation->getResult());

        if (!$operationResultDto->getKess3FileSlotGUID()) {
            $this->logService->info('no_Kess3FileSlotGUID');
            return false;
        }

        $urls = $operationResultDto->generateDecodedFileUrls();

        if (empty($urls)) {
            $this->logService->info('no_DecodedFileUrls');
            return false;
        }

        foreach ($urls as $type => $url) {
            $kess3FileDto = $this->fileService->downloadFile($url);
            if (!$kess3FileDto) {
                continue;
            }

            if(!$kess3FileDto->getData()) {
                $this->logService->info('no_data_file');
                continue;
            }

            $fileSuffix = '_DECODED'.($type ? '_'.$type : '');
            $fileName = $operationResultDto->getName().$fileSuffix;
            $kess3FileDto->setFileName($fileName);

            try {
                $projectFile = $this->fileService->saveDecodedFile($kess3FileDto, $operation);
            } catch (\yii\base\Exception $e) {
                $this->logService->log($e->getMessage());
                $this->projectService->processErrorAddDecodedFile($kess3FileDto, $operation);
                continue;
            }

            if(!$projectFile) {
                $this->logService->log('no_save_file');

                continue;
            }
// успешно декодирован скачан с алиентех и добавлен файл в проект
            $result = $this->projectService->processSuccessAddDecodedFile($projectFile, $operationResultDto);

        }
        return $result;
    }

    /**
     * @param AsyncOperationDto $operation
     * @return bool
     */
    public function downloadEncodedFiles(AsyncOperationDto $operation): bool
    {
        $this->logService->info('AsyncOperationService downloadEncodedFiles');
        if (!$operation->getResult()) {
            $this->logService->info('no_operation_encoded_result');
            return false;
        }

        $operationResultDto = new AsyncEncodeOperationResultDto((object)$operation->getResult());

        if (!$operationResultDto->getKess3FileSlotGUID()) {
            $this->logService->info('no_Kess3FileSlotGUID');
            return false;
        }

        $url = $operationResultDto->getEncodedFileURL();

        if (empty($url)) {
            $this->logService->info('no_EncodedFileUrl');
            return false;
        }
        $kess3FileDto = $this->fileService->downloadFile($url);
        $fileSuffix = '_ENCODED';
        $fileName = $operationResultDto->getName().$fileSuffix;
        $kess3FileDto->setFileName($fileName);

        if(!$kess3FileDto->getData()) {
            $this->logService->info('no_data_file');
            return false;
        }

        $projectFile = $this->fileService->saveEncodedFile($kess3FileDto, $operation);

        if(!$projectFile) {
            $this->logService->info('no_save_file');
            return false;
        }

        try {
            return $this->projectService->processSuccessAddEncodedFile($projectFile, $operationResultDto);
        } catch (\Exception $e) {
            $this->logService->info($e->getMessage());
            return false;
        }

    }


    /**
     * @param AsyncOperationDto $operationOld
     * @return bool
     * @throws Exception
     * @throws AlientechNotAuthException
     * @throws InvalidConfigException
     */
//    public function checkOperationCompleting(AsyncOperationDto $operationOld): bool
//    {
//        $this->logService->info('AsyncOperationService checkOperationCompleting');
//
//        if ($operationOld->isCompleted()) {
//            $this->logService->info('operation_already_completed');
//        }
//
//        $response = $this->linkService->processRequest()
//            ->setMethod('GET')
//            ->setFormat('urlencoded')
//            ->setUrl('/api/async-operations/'.$operationOld->getGuid())
//            ->send();
//
//        $this->linkService->processResponse($response);
//
//        $operationNew = $this->createOperationDtoByData($this->linkService->getRespData());
//
//        if ($operationNew->isCompleted()) {
//            return $this->processCompletedOperation($operationNew);
//        }
//
//        sleep($operationOld->getPollingIntervalMultiplier() * $operationOld->getRecommendedPollingInterval());
//
//        return $this->checkOperationCompleting($operationOld);
//    }

    /**
     * @param AsyncOperationDto $operation
     * @throws Exception
     * @throws \yii\base\InvalidConfigException
     */
    public function finishCompletedOperation(AsyncOperationDto $operation): bool
    {
        $this->logService->info('AsyncOperationService finishCompletedOperation');
        if ($operation->isKess3Decoding()) {
            return $this->finishCompletedOperationDecode($operation);
        }
        if ($operation->isKess3Encoding()) {
            return $this->finishCompletedOperationEncode($operation);
        }
        return true;
    }

    /**
     * @throws Exception
     * @throws \yii\base\InvalidConfigException
     */
    public function processCompletedOperation(AsyncOperationDto $operationNew): bool
    {
        $this->logService->info('AsyncOperationService processCompletedOperation');
        $operationNew = $this->repository->updateAsyncOperation($operationNew);
        return $this->finishCompletedOperation($operationNew);
    }

    /**
     * @param string $data
     * @return bool
     */
    public function decoded(string $data): bool
    {
        try {
            return $this->processDataToOperation($data);
        } catch (InvalidConfigException|Exception $e) {
            return false;
        }
    }

    /**
     * @param string $data
     * @return bool
     */
    public function encoded(string $data): bool
    {
        try {
            return $this->processDataToOperation($data);
        } catch (InvalidConfigException|Exception $e) {
            return false;
        }
    }

    /**
     * @throws Exception
     * @throws InvalidConfigException
     */
    private function processDataToOperation(string $data): bool
    {
        $this->logService->info('AsyncOperationService processDataToOperation');

        $this->logService->log($data);

        if (empty($data)) {
            $this->logService->log('no_data');
            return false;
        }

        try {
            $operation = $this->createOperationDtoByData(json_decode($data));
        } catch (Exception $e) {
            $this->logService->log('no_operation');
            return false;
        }

        if (!$operation->getGuid()) {
            $this->logService->log('no_operation_guid');
            return false;
        }

        if (!$operation->isCompleted()) {
            return false;
        }

        if (!$this->processCompletedOperation($operation) ){
            $this->logService->log('no_finish_completed_operation');
            return false;
        }

        return true;
    }


}