<?php
namespace common\chip\alientech\services;

use common\chip\alientech\entities\dto\AsyncOperationDto;
use common\chip\alientech\entities\dto\Kess3FileDto;
use common\chip\alientech\entities\exceptions\AlientechNotAuthException;
use common\chip\alientech\interfaces\AlientechServiceInterface;
use common\chip\project\builders\ProjectFileNameBuilder;
use common\chip\project\entities\dto\FileOptionsDto;
use common\chip\project\repositories\ProjectFileRepository;
use common\helpers\ProjectHelper;
use common\models\AlientechAsyncOperation;
use common\models\ProjectFiles;
use common\models\Projects;
use Yii;
use yii\base\InvalidConfigException;
use yii\httpclient\Exception;

class FileService implements AlientechServiceInterface
{
    private LogService $logService;
    private AlientechLinkService $linkService;
    private FileSlotService $fileSlotService;
    private AlientechService $alientechService;
    private ProjectFileRepository $repository;

    public function __construct(AlientechLinkService $linkService, LogService $logService, FileSlotService $fileSlotService, AlientechService $alientechService, ProjectFileRepository $repository)
    {
        $this->linkService = $linkService;
        $this->logService = $logService;
        $this->fileSlotService = $fileSlotService;
        $this->alientechService = $alientechService;
        $this->repository = $repository;
    }

    /**
     * @throws AlientechNotAuthException
     * @throws InvalidConfigException
     * @throws Exception
     */
    public function downloadFile($url): ?Kess3FileDto
    {
        $this->logService->info('FileService downloadFile');

        if (empty($url)) {
            $this->logService->info('no_download_url');
            return null;
        }

        $response = $this->linkService->processRequest()
            ->setMethod('GET')
            ->setUrl($url)
            ->send();

        $this->linkService->processResponse($response);

        if ($this->linkService->getRespData() === AsyncOperationService::TOO_MANY_OPEN_KESS3_FILE_SLOTS) {
            $this->fileSlotService->closeFileSlots();
            return $this->downloadFile($url);
        }

       return new Kess3FileDto($this->linkService->getRespData());
    }

    /**
     * @throws \common\chip\alientech\entities\exceptions\AlientechNotAuthException
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\httpclient\Exception
     */
    public function uploadModifiedFiles(array $files): array
    {
        $result = ['error' => false, 'data' => []];
        $this->logService->info('FileService uploadFiles');

        if (empty($files)) {
            $this->logService->info('no_files');
            return ['error' => true, 'message' => 'no_files'];
        }

        foreach($files as $file) {
            $fileDto = new Kess3FileDto(json_decode($file->params));
            if (!$fileDto->getKess3FileSlotGUID()) {
                $result['error'] = true;
                $result['message'][] = 'no_file_result';
                continue;
            }
            $this->fileSlotService->reOpenSlot($fileDto->getKess3FileSlotGUID());

            $response = $this->linkService->processRequest()
                ->setMethod('PUT')
                ->setFormat('urlencoded')
                ->setUrl("/api/kess3/upload-modified-file/".$this->alientechService->getCustomerCode().'/'.$fileDto->getKess3FileSlotGUID().'/'.$fileDto->calculateModifiedFileType())
                ->addFile('file', $file->path)
                ->send();

            $this->linkService->processResponse($response);

//            $fileDto->setUploadResult(json_encode($this->linkService->getRespData()));

            $file->file_history = json_encode($this->linkService->getRespData());

            if (!$file->save(false)){
                $result['error'] = true;
                $result['message'][] = json_encode($file->errors);
            }

            $result['data'][] = $file;
        }
        return $result;
    }

    public function uploadModFile(ProjectFiles $file = null)
    {
        $this->logService->info('FileService uploadModFile');

        if (is_null($file)) {
            return;
        }

        $fileParams = json_decode($file->params);

        $response = $this->linkService->processRequest()
            ->setMethod('PUT')
            ->setFormat('urlencoded')
            ->addFile('readFile', $file->path)
            ->setUrl($url)
            ->send();

    }

    /**
     * @param Kess3FileDto $kess3FileDto
     * @param AsyncOperationDto $operationDto
     * @return ProjectFiles|null
     * @throws \yii\base\Exception
     */
    public function saveDecodedFile(Kess3FileDto $kess3FileDto, AsyncOperationDto $operationDto): ?ProjectFiles
    {
        $this->logService->info('FileService saveDecodedFile');
        $filePath = Yii::getAlias('@storage') . '/web/projects/files/' . $kess3FileDto->getFileName();

        $data_decoded = base64_decode ($kess3FileDto->getData());

        $savedFile = fopen ($filePath,'w');

        if (fwrite ($savedFile, $data_decoded)) {
            fclose ($savedFile);

            $alientechOperation = AlientechAsyncOperation::find()->where(['guid' => $operationDto->getGuid(), 'asyncOperationType' => AsyncOperationDto::TYPE_KESS3_DECODING])->one();

            $projectFile = new ProjectFiles([
                'type' => 'external',
                'title' => $kess3FileDto->getFileName(),
                'component_name' => $component ?? null,
                'project_id' => $operationDto->getProjectId(),
                'file_id' => $operationDto->getProjectFileId(),
                'alientech_operation_id' => $alientechOperation->id,
                'file_type' => ProjectHelper::FILE_TYPE_ORIGINAL_DECODED,
                'orig' => ProjectHelper::PROJECT_FILE_ORIGINAL,
                'params' => $kess3FileDto->toJson(),
                'path' => $filePath,
                'filename' => $kess3FileDto->getFileName(),
                'hash' => yii::$app->security->generateRandomString(12),
            ]);

            if ($projectFile->save(false)) {
                return $projectFile;
            } else {
                $this->logService->log($projectFile->errors);
                return null;
            }
        }
        return null;
    }

    public function saveEncodedFile(Kess3FileDto $kess3FileDto, AsyncOperationDto $operationDto): ?ProjectFiles
    {
        $this->logService->info('FileService saveEncodedFile');
        $filePath = Yii::getAlias('@storage') . '/web/projects/files/' . $kess3FileDto->getFileName();

        $data_decoded = base64_decode ($kess3FileDto->getData());

        $savedFile = fopen ($filePath,'w');

        if (fwrite ($savedFile, $data_decoded)) {
            fclose ($savedFile);

            $alientechOperation = AlientechAsyncOperation::find()->where(['guid' => $operationDto->getGuid(), 'asyncOperationType' => $operationDto->getAsyncOperationType()])->one();

            $initFile = $this->repository->findInitModel($operationDto->getProjectId());
            $project = Projects::findOne($operationDto->getProjectId());
            $attributes = [
                'type' => 'external',
                'title' => $kess3FileDto->getFileName(),
                'component_name' => $component ?? null,
                'project_id' => $operationDto->getProjectId(),
                'file_id' => $operationDto->getProjectFileId(),
                'alientech_operation_id' => $alientechOperation->id,
                'file_type' => ProjectHelper::FILE_TYPE_MODIFIED_ENCODED,
                'params' => $kess3FileDto->toJson(),
                'path' => $filePath,
                'filename' => $kess3FileDto->getFileName(),
                'hash' => yii::$app->security->generateRandomString(12),
            ];

             if (!empty($initFile->file_history)) {
                $this->logService->info('FileService $initFile->file_history='. $initFile->file_history);
                $file_history = json_decode($initFile->file_history, true);
                $fileOptionsDto = new FileOptionsDto($file_history);
                switch ($fileOptionsDto->getFileVer()) {
                    case ProjectHelper::PROJECT_FILE_ORIGINAL:
                        $fileSuffix = ProjectHelper::PROJECT_FILE_SUFFIX_ORIGINAL;
                        break;

                    default:
                    case ProjectHelper::PROJECT_FILE_MODIFIED:
                        $fileSuffix = '_MOD_v' . ($project->modFilesCount + 1);
                        break;
                }

                $fileName = (new ProjectFileNameBuilder())->byProject($project)->withDelimiter('_')->withSuffix($fileSuffix)->buildFileName();

                $attributes['title'] = $fileName;
                $attributes['filename'] = $fileName;
                $attributes['orig'] = $fileOptionsDto->getFileVer();
                $attributes['comment'] = $fileOptionsDto->getComment();
                $attributes[$fileOptionsDto->getOption()] = $fileOptionsDto->getValue();
            }

            try {
                $projectFile = $this->repository->createFileByAttributes($attributes);
            } catch (\Exception $e) {
                $this->logService->info('FileService error save encoded file');
            }

            if (!empty($projectFile->errors)) {
                $this->logService->log($projectFile->errors);
                return null;
            }

            return $projectFile;
        }
        return null;
    }

    public function getFilesByProjectId(int $projectId): array
    {
        return ProjectFiles::find()->where([
            'file_type' => ProjectHelper::FILE_TYPE_MODIFIED_DECODED,
            'project_id' => $projectId,
            'isDeleted' => 0
        ])->all();
    }



}