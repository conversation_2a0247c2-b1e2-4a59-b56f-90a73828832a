<?php
namespace common\chip\alientech\services;

use common\chip\alientech\entities\exceptions\AlientechNotAuthException;
use common\chip\alientech\interfaces\AlientechServiceInterface;
use yii\base\InvalidConfigException;
use yii\httpclient\Exception;

class FileSlotService implements AlientechServiceInterface
{
    private array $fileSlots;
    private AlientechLinkService $linkService;
    private LogService $logService;
    const MAXIMUM_KESS_FILE_SLOTS = 3;

    public function __construct(AlientechLinkService $linkService, LogService $logService)
    {
        $this->linkService = $linkService;
        $this->logService = $logService;
        $this->logService->info('FileSlotService __construct');
    }

    /**
     * @throws \yii\httpclient\Exception
     * @throws \yii\base\InvalidConfigException
     */
    public function hasFileSlotsForOpen() : bool
    {
        $this->logService->info('FileSlotService hasOpenFileSlots');
        $this->fileSlots = $this->getSlots();
        return $this->fileSlotLimitIsNotReached();
    }

    /**
     * @throws \common\chip\alientech\entities\exceptions\AlientechNotAuthException
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\httpclient\Exception
     */
    private function getSlots()
    {
        $this->logService->info('FileSlotService getSlots');
        $response = $this->linkService->processRequest()
            ->setMethod('GET')
            ->setUrl('/api/kess3/file-slots')
            ->send();

        $this->linkService->processResponse($response);
        return $this->linkService->getRespData();
    }

    /**
     * REV-5 Проверка количества доступных слотов
     * @param $slots
     * @return bool
     */
    protected function fileSlotLimitIsNotReached():bool
    {
        $this->logService->info('FileSlotService fileSlotLimitIsNotReached');
        $result = true;

        $openedSlots = 0;

        if (!is_null($this->fileSlots) && (count($this->fileSlots) >0)) {
            foreach ($this->fileSlots as $slot) {
                if (!$slot->isClosed) {
                    $openedSlots++;
                }
            }

            if ($openedSlots >= self::MAXIMUM_KESS_FILE_SLOTS) {
                $result = false;
            }
        }

        return $result;
    }

    public function closeFileSlots(): array
    {
        $this->logService->info('FileSlotService closeFileSlots');
        $result = [];
        if (count($this->fileSlots) > 0) {
            foreach ($this->fileSlots as $slot) {
                if (!$slot->isClosed) {
                    $startedOn = new \DateTime($slot->createdOn);
                    $dateNow = new \DateTime('now');
                    $this->logService->info('slot [' . $slot->guid . '] dateDiff '.json_encode(date_diff($dateNow, $startedOn)));
                    $closeFileSlotResult = $this->closeSlot($slot->guid);
                    $this->logService->info('slot [' . $slot->guid . '] closeSlot '.json_encode($closeFileSlotResult));
                    $result[] = $closeFileSlotResult;
                }
            }
        }
        $this->logService->info('closeAllSlots finish');
        return $result;
    }

    /**
     * @throws \common\chip\alientech\entities\exceptions\AlientechNotAuthException
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\httpclient\Exception
     */
    public function closeAllFileSlots(): array
    {
        $this->logService->info('FileSlotService closeAllFileSlots');
        $this->fileSlots = $this->getSlots();
        $result = [];
        if (count($this->fileSlots) > 0) {
            foreach ($this->fileSlots as $slot) {
                if (!$slot->isClosed) {
                    $startedOn = new \DateTime($slot->createdOn);
                    $dateNow = new \DateTime('now');
                    $this->logService->info('slot [' . $slot->guid . '] dateDiff '.json_encode(date_diff($dateNow, $startedOn)));
                    $closeFileSlotResult = $this->closeSlot($slot->guid);
                    $this->logService->info('slot [' . $slot->guid . '] closeSlot '.json_encode($closeFileSlotResult));
                    $result[] = $closeFileSlotResult;
                }
            }
        }
        $this->logService->info('closeAllSlots finish');
        return $result;
    }

    /**
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\httpclient\Exception
     */
    protected function closeSlot($slotGuid = '')
    {
        $this->logService->info('FileSlotService closeSlot');
        if (empty($slotGuid)) {
            $this->logService->info('closeSlot_slotGuid_is_empty');
            return null;
        }

        $url = '/api/kess3/file-slots/'.$slotGuid.'/close ';

        $this->logService->info($url);

        $response = $this->linkService->processRequest()
            ->setMethod('POST')
            ->addHeaders(['Content-Length' => 0])
            ->setUrl($url)
            ->send();

        $this->linkService->processResponse($response);
        return $this->linkService->getRespData();
    }

    /**
     * @throws AlientechNotAuthException
     * @throws InvalidConfigException
     * @throws Exception
     */
    public function reOpenSlot(string $slotGuid = '')
    {
        $this->logService->info('FileSlotService reOpenSlot');

        if (empty($slotGuid)) {
            $this->logService->info('reOpenSlot_slotGuid_is_empty');
            return null;
        }

        if (!$this->hasFileSlotsForOpen()) {
            $closedSlots = $this->closeAllFileSlots();
        }

        $url = '/api/kess3/file-slots/'.$slotGuid.'/reopen';

        $this->logService->info($url);

        $response = $this->linkService->processRequest()
            ->setMethod('POST')
            ->addHeaders(['Content-Length' => 0])
            ->setUrl($url)
            ->send();

        $this->linkService->processResponse($response);
        return $this->linkService->getRespData();

    }


}