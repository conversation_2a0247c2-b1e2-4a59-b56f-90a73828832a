<?php

namespace common\chip\alientech\jobs;

use common\chip\alientech\services\AsyncOperationService;
use common\chip\alientech\services\FileSlotService;
use Yii;
use yii\base\BaseObject;

class AlientechDownloadDecodedFileJob extends BaseObject implements \yii\queue\JobInterface
{

    /**
     * @var mixed
     */
    public $operation;

    public function execute($queue): void
    {
        $service = Yii::$container->get(AsyncOperationService::class);
        try {
            $service->downloadDecodedFiles($this->operation);
        } catch (\Exception $e) {
            $service->logService->info($e->getMessage());
        }
    }
}