<?php

namespace common\chip\alientech\jobs;

use common\chip\alientech\entities\exceptions\AlientechNotAuthException;
use common\chip\alientech\services\AsyncOperationService;
use common\chip\alientech\services\FileSlotService;
use common\chip\alientech\services\Kess3Service;
use Yii;
use yii\base\BaseObject;
use yii\base\InvalidConfigException;
use yii\httpclient\Exception;

class AlientechStartEncodingJob extends BaseObject implements \yii\queue\JobInterface
{

    /**
     * @var mixed
     */
    public $projectId;

    /**
     * @throws \yii\di\NotInstantiableException
     * @throws \common\chip\alientech\entities\exceptions\AlientechNotAuthException
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\httpclient\Exception
     */
    public function execute($queue): void
    {
        $service = Yii::$container->get(Kess3Service::class);
        try {
            $service->startEncoding($this->projectId);
        } catch (AlientechNotAuthException|Exception|InvalidConfigException $e) {
            $service->logService->info($e->getMessage());
        }
    }
}