<?php

namespace common\chip\alientech\entities\dto;

class Kess3FileDto extends AlientechDto
{
    private string $guid;    //“706a4fd1-f7bc-4845-9a60-7909f897e050”
    private string $kess3FileSlotGUID;  //“706a4fd1-f7bc-4845-9a60-7909f897e050”
    private string $fileType;
//Read
//ID
//OBDDecoded
//OBDModified

//OBDOriginal
//OBDEncoded

//BootBenchDecodedMicro
//BootBenchDecodedFlash
//BootBenchDecodedEEPROM
//BootBenchDecodedMapFile

//BootBenchModifiedMicro
//BootBenchModifiedFlash
//BootBenchModifiedEEPROM
//BootBenchModifiedMapFile

//BootBenchEncoded

//Micro, Flash, EEPROM, MapFile

    private string $name;
    private string $data;
    private int $fileSize;
    private int $length;

    private string $jsonData;
    private string $fileName;
    private string $uploadResult;


    /**
     * Kess3FileSlotDto constructor.
     * @param $data
     */
    public function __construct($data = null)
    {
        $this->jsonData = $this->generateFileJson($data);
        $this->guid = $data->guid ?? '';
        $this->kess3FileSlotGUID = $data->kess3FileSlotGUID ?? '';
        $this->fileType = $data->fileType ?? '';
        $this->name = $data->name ?? '';
        $this->data = $data->data ?? '';
        $this->fileSize = $data->fileSize ?? 0;
        $this->length = $data->length ?? 0;
        $this->uploadResult = $data->uploadResult ?? '';
    }

    private array $fileTypes = [
        'Read',
        'ID',
        'OBDDecoded',
        'OBDModified',

        'OBDOriginal',
        'OBDEncoded',

        'BootBenchDecodedMicro',
        'BootBenchDecodedFlash',
        'BootBenchDecodedEEPROM',
        'BootBenchDecodedMapFile',

        'BootBenchModifiedMicro',
        'BootBenchModifiedFlash',
        'BootBenchModifiedEEPROM',
        'BootBenchModifiedMapFile',

        'BootBenchEncoded',
    ];

    public function calculateModifiedFileType()
    {
        $probableType = str_replace('Decoded', 'Modified', $this->fileType);
        return in_array($probableType, $this->fileTypes) ? $probableType : $this->fileType;
    }

    public static function createEmpty()
    {
        return new Kess3FileDto([]);
    }

    public function generateFileJson($data = null): string
    {
        $cloneData = clone($data);
        if (isset($cloneData->data)) {
            $cloneData->data = '';
        }
        return json_encode($cloneData);
    }

    public function toJson()
    {
        return $this->jsonData;
    }

    /**
     * @return string
     */
    public function getUploadResult(): string
    {
        return $this->uploadResult;
    }

    /**
     * @param string $uploadResult
     */
    public function setUploadResult(string $uploadResult): void
    {
        $this->uploadResult = $uploadResult;
    }


    /**
     * @return string
     */
    public function getGuid(): string
    {
        return $this->guid;
    }

    /**
     * @param string $guid
     */
    public function setGuid(string $guid): void
    {
        $this->guid = $guid;
    }

    /**
     * @return string
     */
    public function getKess3FileSlotGUID(): string
    {
        return $this->kess3FileSlotGUID;
    }

    /**
     * @param string $kess3FileSlotGUID
     */
    public function setKess3FileSlotGUID(string $kess3FileSlotGUID): void
    {
        $this->kess3FileSlotGUID = $kess3FileSlotGUID;
    }

    /**
     * @return string
     */
    public function getFileType(): string
    {
        return $this->fileType;
    }

    /**
     * @param string $fileType
     */
    public function setFileType(string $fileType): void
    {
        $this->fileType = $fileType;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @param string $name
     */
    public function setName(string $name): void
    {
        $this->name = $name;
    }

    /**
     * @return string
     */
    public function getData(): string
    {
        return $this->data;
    }

    /**
     * @param string $data
     */
    public function setData(string $data): void
    {
        $this->data = $data;
    }

    /**
     * @return int
     */
    public function getFileSize(): int
    {
        return $this->fileSize;
    }

    /**
     * @param int $fileSize
     */
    public function setFileSize(int $fileSize): void
    {
        $this->fileSize = $fileSize;
    }

    /**
     * @return int
     */
    public function getLength(): int
    {
        return $this->length;
    }

    /**
     * @param int $length
     */
    public function setLength(int $length): void
    {
        $this->length = $length;
    }

    /**
     * @return string
     */
    public function getJsonData(): string
    {
        return $this->jsonData;
    }

    /**
     * @param string $jsonData
     */
    public function setJsonData(string $jsonData): void
    {
        $this->jsonData = $jsonData;
    }

    public function setFileName(string $fileName)
    {
        $this->fileName = $fileName;
    }

    /**
     * @return string
     */
    public function getFileName(): string
    {
        return $this->fileName;
    }


}