<?php
namespace common\chip\alientech\repositories;

use common\chip\alientech\entities\dto\AlientechLinkDto;

class AlientechLinkRepository
{
    private AlientechLinkDto $dto;

    /**
     * AuthRepository constructor.
     */
    public function __construct()
    {
        $this->dto = AlientechLinkDto::fromEnv();
    }

    /**
     * @return AlientechLinkDto
     */
    public function getDto(): AlientechLinkDto
    {
        return $this->dto;
    }

}