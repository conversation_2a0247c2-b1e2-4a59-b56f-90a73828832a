<?php
namespace common\chip\alientech\repositories;

use common\chip\alientech\entities\dto\AsyncOperationDto;
use common\chip\alientech\services\LogService;
use common\models\AlientechAsyncOperation;
use common\models\AlientechOperation;
use yii\helpers\ArrayHelper;

class AsyncOperationRepository
{
    private AsyncOperationDto $dto;
    private LogService $logService;

    /**
     * AuthRepository constructor.
     */
    public function __construct()
    {
        $this->logService = new LogService();
        $this->dto = AsyncOperationDto::createEmpty();
    }

    /**
     * @param null $data
     */
    public function buildAsyncOperationByData($data = null)
    {
        $this->logService->info('AsyncOperationRepository buildAsyncOperationByData');
        $this->dto = new AsyncOperationDto($data);
        if(($model = AlientechAsyncOperation::find()->where(['guid' => $this->dto->getGuid()])->one())!== null) {
            $this->dto->setUserInfo(['projectId' => $model->project_id, 'fileId' => $model->file_id]);
        }
    }

    /**
     * @return AsyncOperationDto
     */
    public function getOperation(): AsyncOperationDto
    {
        return $this->dto;
    }

//    public function saveOperation(AsyncOperationDto $operation): ?AsyncOperationDto
//    {
//        $this->logService->info('AsyncOperationRepository saveOperation');
//        if (($model = AlientechAsyncOperation::find()->where(['guid' => $operation->getGuid()])->one()) !== null) {
////            $model = $this->updateAsyncOperation($operation, $model);
//            $operation->setUserInfo(['projectId' => $model->project_id, 'fileId' => $model->file_id]);
//            $model->setAttributes($operation->toArray());
//        } else {
////            return $this->createAsyncOperation($operation);
//            $model = new AlientechAsyncOperation($operation->toArray());
//            $model->setAttributes([
//                'project_id' => $operation->getProjectId(),
//                'file_id' => $operation->getProjectFileId(),
//            ]);
//        }
//        if (!$model->save(false)){
//            $this->logService->log($model->errors);
//            return null;
//        }
//        return $operation;
//    }

    public function createAsyncOperation(AsyncOperationDto $operation): ?AsyncOperationDto
    {
        $this->logService->info('AsyncOperationRepository createAsyncOperation');
        $model = new AlientechAsyncOperation($operation->toArray());
        $model->setAttributes([
            'project_id' => $operation->getProjectId(),
            'file_id' => $operation->getProjectFileId(),
        ]);
        if (!$model->save(false)){
            $this->logService->log($model->errors);
            return null;
        }
        return $operation;
    }

    public function updateAsyncOperation(AsyncOperationDto $operation): ?AsyncOperationDto
    {
        $this->logService->info('AsyncOperationRepository updateAsyncOperation');
        $model = AlientechAsyncOperation::find()->where(['guid' => $operation->getGuid()])->one();
        $operation->setUserInfo(['projectId' => $model->project_id, 'fileId' => $model->file_id]);
        $model->setAttributes($operation->toArray());
        if (!$model->save(false)){
            $this->logService->log($model->errors);
            return null;
        }
        return $operation;
    }

}