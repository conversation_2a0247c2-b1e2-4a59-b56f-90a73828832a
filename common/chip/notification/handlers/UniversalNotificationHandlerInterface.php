<?php

namespace common\chip\notification\handlers;

use common\chip\event\core\FilePackedEvent;
use common\chip\event\core\FileProcessedEvent;
use common\chip\event\core\FileProcessingErrorEvent;
use common\chip\event\core\FileUnpackedEvent;
use common\chip\event\core\FileUploadedEvent;
use common\chip\event\core\OptionsRequestEvent;
use common\chip\event\core\ProjectClosedEvent;
use common\chip\event\core\ProjectCreatedEvent;
use common\chip\event\EventHandler;
use common\chip\event\core\EventInterface;
use common\chip\externalIntegrations\kess3\Domain\Events\DecodingStartedEvent;
use common\chip\externalIntegrations\kess3\Domain\Events\EncodingCompletedEvent;
use common\chip\notification\events\NoteAddedEvent;
use common\chip\notification\events\ProjectStatusChangedEvent;
use common\chip\notification\events\SystemErrorEvent;
use common\chip\event\routing\NotificationRouter;
use Yii;

/**
 * Универсальный обработчик уведомлений
 * Наследуется от системы событий common/chip/event
 * Фокусируется только на логике уведомлений
 */
class UniversalNotificationHandler implements EventHandler
{
    private NotificationRouter $router;

    /**
     * @var callable Функция для логирования (как в EventDispatcher)
     */
    private $logger;

    public function __construct(
        NotificationRouter $router = null,
        ?callable $logger = null
    ) {
        $this->router = $router ?: Yii::$container->get(NotificationRouter::class);

        // Используем тот же подход логирования, что и в EventDispatcher
        $this->logger = $logger ?? function ($message, $category) {
            // По умолчанию используем стандартную функцию логирования Yii, если она доступна
            if (class_exists('\Yii') && Yii::$app !== null) {
                Yii::info($message, $category);
            }
        };
    }

    /**
     * {@inheritdoc}
     */
    public function canHandle(EventInterface $event): bool
    {
        return $event instanceof NoteAddedEvent
            || $event instanceof ProjectStatusChangedEvent
            || $event instanceof ProjectCreatedEvent
            || $event instanceof OptionsRequestEvent
            || $event instanceof FileUploadedEvent
            || $event instanceof FileProcessingErrorEvent
            || $event instanceof FileProcessedEvent
            || $event instanceof FilePackedEvent
            || $event instanceof FileUnpackedEvent
            || $event instanceof ProjectClosedEvent
            || $event instanceof SystemErrorEvent
            || $event instanceof DecodingStartedEvent
            || $event instanceof EncodingCompletedEvent;
    }

    /**
     * {@inheritdoc}
     */
    public function handle(EventInterface $event): void
    {
        if (!$this->canHandle($event)) {
            return;
        }

        try {
            call_user_func($this->logger,
                "Processing notification event: " . get_class($event),
                'notification'
            );

            // Обрабатываем событие через роутер
            $this->router->handleEvent($event);

            call_user_func($this->logger,
                "Notification event processed successfully: " . get_class($event),
                'notification'
            );

        } catch (\Exception $e) {
            call_user_func($this->logger,
                "Failed to process notification event " . get_class($event) . ": {$e->getMessage()}",
                'notification.error'
            );
            throw $e;
        }
    }
}
