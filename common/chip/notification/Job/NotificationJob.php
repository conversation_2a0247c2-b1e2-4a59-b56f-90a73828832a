<?php

namespace common\chip\notification\Job;

use notification\Core\Event\BaseEvent;
use notification\Service\NotificationService;
use yii\queue\Queue;

/**
 * Задача для асинхронного создания и маршрутизации уведомлений
 */
class NotificationJob extends BaseNotificationJob
{
    /**
     * Сериализованное событие
     * 
     * @var string
     */
    public string $event;
    
    /**
     * Тип события
     * 
     * @var string
     */
    public string $eventType;
    
    /**
     * {@inheritdoc}
     */
    protected function process(Queue $queue): void
    {
        // Получаем сервис уведомлений
        $notificationService = \Yii::$container->get(NotificationService::class);
        
        // Десериализуем событие
        /** @var BaseEvent $event */
        $event = unserialize($this->event);
        
        // Проверяем, что событие корректно десериализовано и соответствует ожидаемому типу
        if (!$event instanceof BaseEvent || get_class($event) !== $this->eventType) {
            throw new \RuntimeException("Некорректный тип события: ожидался {$this->eventType}");
        }
        
        // Обрабатываем событие и создаем уведомления
        $notificationService->processEvent($event);
        
        \Yii::info([
            'message' => 'Уведомление успешно создано и маршрутизировано асинхронно',
            'eventType' => $this->eventType,
            'sourceId' => $event->getSourceId(),
        ], 'notification.job');
    }
}
