<?php

namespace common\chip\notification\events;

use common\chip\event\core\BaseEvent;

/**
 * Событие системной ошибки
 */
class SystemErrorEvent extends BaseEvent
{
    private string $errorType;
    private string $service;
    private string $errorMessage;
    private string $severity;
    private array $affectedProjects;
    private array $errorContext;

    public function __construct(
        string $errorType,
        string $service,
        string $errorMessage,
        string $severity = 'normal',
        array $affectedProjects = [],
        array $errorContext = []
    ) {
        parent::__construct();
        
        $this->errorType = $errorType;
        $this->service = $service;
        $this->errorMessage = $errorMessage;
        $this->severity = $severity;
        $this->affectedProjects = $affectedProjects;
        $this->errorContext = $errorContext;
        
        $this->setContextData('errorType', $errorType);
        $this->setContextData('service', $service);
        $this->setContextData('errorMessage', $errorMessage);
        $this->setContextData('severity', $severity);
        $this->setContextData('affectedProjects', $affectedProjects);
        $this->setContextData('errorContext', $errorContext);
    }

    public function getErrorType(): string
    {
        return $this->errorType;
    }

    public function getService(): string
    {
        return $this->service;
    }

    public function getErrorMessage(): string
    {
        return $this->errorMessage;
    }

    public function getSeverity(): string
    {
        return $this->severity;
    }

    public function getAffectedProjects(): array
    {
        return $this->affectedProjects;
    }

    public function getErrorContext(): array
    {
        return $this->errorContext;
    }

    public function getDescription(): string
    {
        return "Системная ошибка: {$this->errorType}";
    }

    public function getType(): string
    {
        return 'system.error';
    }
}
