<?php
namespace common\chip\project\entities\dto\ProjectFile;

use common\chip\project\entities\dto\ProjectDto;
use common\helpers\ProjectHelper;
use yii\helpers\ArrayHelper;
use yii\web\Request;

class ProjectMasterFileDto extends AbstractProjectFileDto
{
    public function __construct(array $data = [])
    {
        parent::__construct($data);
        $this->setFileType(ProjectHelper::FILE_TYPE_ORIGINAL);
    }
}