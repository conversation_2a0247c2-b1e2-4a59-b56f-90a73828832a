<?php
namespace common\chip\project\entities\dto\ProjectFile;

use common\chip\project\entities\dto\ProjectDto;
use yii\helpers\ArrayHelper;
use yii\web\Request;

class AbstractProjectFileDto extends ProjectDto implements ProjectFileDtoInterface
{

    /**
     * @var array|mixed|object
     */
    private mixed $id;
    private mixed $type;
    private mixed $orig;
    private mixed $file_type;
    private mixed $project_id;
    private mixed $hash;
    private mixed $can_download;

    public function __construct(array $data)
    {
        $this->id = ArrayHelper::getValue($data,'id', null);
        $this->type = ArrayHelper::getValue($data,'type', null);
        $this->orig = ArrayHelper::getValue($data,'orig', null);
        $this->project_id = ArrayHelper::getValue($data,'project_id', null);
        $this->hash = ArrayHelper::getValue($data,'hash', null);
        $this->can_download = ArrayHelper::getValue($data,'can_download', null);
    }

    public static function createByRequest(Request $request)
    {
        $data = [];
        if ($request->post('id')) {
            $data['id'] = $request->post('id');
        }
        if ($request->post('value')) {
            $data['value'] = $request->post('value');
        }
        if ($request->post('fileVer')) {
            $data['file_ver'] = $request->post('fileVer');
        }
        if ($request->post('comment')) {
            $data['comment'] = $request->post('comment');
        }
        if ($request->post('option')) {
            $data['option'] = $request->post('option');
        }
        return new AbstractProjectFileDto($data);
    }


    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'orig' => $this->orig,
            'file_type' => $this->file_type,
            'project_id' => $this->project_id,
            'hash' => $this->hash,
            'can_download' => $this->can_download,
        ];
    }

    /**
     * @return mixed
     */
    public function getId(): mixed
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId(mixed $id): void
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getType(): mixed
    {
        return $this->type;
    }

    /**
     * @param mixed $type
     */
    public function setType(mixed $type): void
    {
        $this->type = $type;
    }

    /**
     * @return mixed
     */
    public function getOrig(): mixed
    {
        return $this->orig;
    }

    /**
     * @param mixed $orig
     */
    public function setOrig(mixed $orig): void
    {
        $this->orig = $orig;
    }

    /**
     * @return mixed
     */
    public function getFileType(): mixed
    {
        return $this->file_type;
    }

    /**
     * @param mixed $file_type
     */
    public function setFileType(mixed $file_type): void
    {
        $this->file_type = $file_type;
    }

    /**
     * @return mixed
     */
    public function getProjectId(): mixed
    {
        return $this->project_id;
    }

    /**
     * @param mixed $project_id
     */
    public function setProjectId(mixed $project_id): void
    {
        $this->project_id = $project_id;
    }

    /**
     * @return mixed
     */
    public function getHash(): mixed
    {
        return $this->hash;
    }

    /**
     * @param mixed $hash
     */
    public function setHash(mixed $hash): void
    {
        $this->hash = $hash;
    }

    /**
     * @return mixed
     */
    public function getCanDownload(): mixed
    {
        return $this->can_download;
    }

    /**
     * @param mixed $can_download
     */
    public function setCanDownload(mixed $can_download): void
    {
        $this->can_download = $can_download;
    }

}