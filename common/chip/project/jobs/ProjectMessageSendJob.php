<?php

namespace common\chip\project\jobs;

use common\chip\project\interfaces\ProjectMessageDtoInterface;
use common\chip\project\services\MessageService;
use Yii;
use yii\base\BaseObject;
use yii\base\InvalidConfigException;
use yii\di\NotInstantiableException;
use yii\httpclient\Exception;

class ProjectMessageSendJob extends BaseObject implements \yii\queue\JobInterface
{

    public ProjectMessageDtoInterface $messageDto;

    /**
     * @param $queue
     * @return void
     * @throws InvalidConfigException
     * @throws NotInstantiableException
     * @throws Exception
     */
    public function execute($queue): void
    {
        $service = Yii::$container->get(MessageService::class);
        try {
            $service->startSendMessage($this->messageDto);
        } catch (InvalidConfigException $e) {
            $service->logService->info($e->getMessage());
        } catch (Exception $e) {
            $service->logService->info($e->getMessage());
        }
    }
}