<?php
namespace common\chip\project\builders;

use common\chip\project\entities\dto\ProjectNoteDto;
use common\chip\project\interfaces\ProjectNoteDtoInterface;

class ProjectNoteExtender
{
    private ProjectNoteDtoInterface $noteDto;

    public function __construct(ProjectNoteDtoInterface $noteDto)
    {
        $this->noteDto = $noteDto;
    }

    public function withFileName(string $fileName): ProjectNoteExtender
    {
        $this->noteDto->setFileName($fileName);
        return $this;
    }
    public function withNoteId(int $noteId): ProjectNoteExtender
    {
        $this->noteDto->setNoteId($noteId);
        return $this;
    }

    public function withPath(string $path): ProjectNoteExtender
    {
        $this->noteDto->setPath($path);
        return $this;
    }

    public function withSys(int $sys): ProjectNoteExtender
    {
        $this->noteDto->setSys($sys);
        return $this;
    }

    public function getNote(): ProjectNoteDtoInterface
    {
        return $this->noteDto;
    }
}