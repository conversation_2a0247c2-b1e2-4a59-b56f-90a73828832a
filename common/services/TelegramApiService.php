<?php

namespace common\services;

use common\models\TelegramAccount;
use common\models\User;
use Yii;
use yii\base\Component;

/**
 * Сервис для работы с Telegram API
 */
class TelegramApiService extends Component
{
    private $telegramComponent;
    
    public function init()
    {
        parent::init();
        $this->telegramComponent = Yii::$app->telegram;
    }
    
    /**
     * Отправляет сообщение в Telegram
     *
     * @param string|int $chatId ID чата
     * @param string $message Текст сообщения
     * @param array $options Дополнительные параметры
     * @return bool Успешность отправки
     */
    public function sendMessage($chatId, string $message, array $options = []): bool
    {
        try {
            $params = array_merge([
                'chat_id' => $chatId,
                'text' => $message,
            ], $options);
            
            $response = $this->telegramComponent->sendMessage($params);
            
            // Логируем успешную отправку
            Yii::info([
                'message' => 'Telegram сообщение отправлено',
                'chat_id' => $chatId,
                'text_length' => mb_strlen($message),
                'options' => $options,
                'response' => $response
            ], 'telegram');
            
            return true;
        } catch (\Exception $e) {
            // Логируем ошибку
            Yii::error([
                'message' => 'Ошибка отправки Telegram сообщения',
                'chat_id' => $chatId,
                'text' => $message,
                'error' => $e->getMessage(),
                'options' => $options
            ], 'telegram');
            
            return false;
        }
    }
    
    /**
     * Отправляет сообщение всем Telegram аккаунтам пользователя
     *
     * @param int $userId ID пользователя
     * @param string $message Текст сообщения
     * @param array $options Дополнительные параметры
     * @return array Результаты отправки ['success' => int, 'failed' => int, 'details' => array]
     */
    public function sendMessageToUser(int $userId, string $message, array $options = []): array
    {
        $results = [
            'success' => 0,
            'failed' => 0,
            'details' => []
        ];
        
        try {
            // Получаем все активные Telegram аккаунты пользователя
            $telegramAccounts = $this->getUserTelegramAccounts($userId);
            
            if (empty($telegramAccounts)) {
                Yii::warning([
                    'message' => 'У пользователя нет активных Telegram аккаунтов',
                    'userId' => $userId
                ], 'telegram');
                
                return $results;
            }
            
            // Отправляем сообщение каждому аккаунту
            foreach ($telegramAccounts as $account) {
                $chatId = $account->chat_id;
                $accountInfo = $this->parseAccountInfo($account->tg_account);
                
                $success = $this->sendMessage($chatId, $message, $options);
                
                if ($success) {
                    $results['success']++;
                } else {
                    $results['failed']++;
                }
                
                $results['details'][] = [
                    'chat_id' => $chatId,
                    'account_info' => $accountInfo,
                    'success' => $success
                ];
            }
            
            Yii::info([
                'message' => 'Отправка Telegram сообщений пользователю завершена',
                'userId' => $userId,
                'total_accounts' => count($telegramAccounts),
                'success' => $results['success'],
                'failed' => $results['failed']
            ], 'telegram');
            
        } catch (\Exception $e) {
            Yii::error([
                'message' => 'Ошибка при отправке Telegram сообщений пользователю',
                'userId' => $userId,
                'error' => $e->getMessage()
            ], 'telegram');
            
            $results['failed']++;
        }
        
        return $results;
    }
    
    /**
     * Получает все активные Telegram аккаунты пользователя
     *
     * @param int $userId ID пользователя
     * @return TelegramAccount[]
     */
    public function getUserTelegramAccounts(int $userId): array
    {
        try {
            return TelegramAccount::find()
                ->where(['user_id' => $userId])
                ->andWhere(['or', ['isDeleted' => 0], ['isDeleted' => null]])
                ->andWhere(['!=', 'chat_id', ''])
                ->andWhere(['is not', 'chat_id', null])
                ->all();
        } catch (\Exception $e) {
            Yii::error([
                'message' => 'Ошибка получения Telegram аккаунтов пользователя',
                'userId' => $userId,
                'error' => $e->getMessage()
            ], 'telegram');
            
            return [];
        }
    }
    
    /**
     * Получает все chat_id пользователя
     *
     * @param int $userId ID пользователя
     * @return array Массив chat_id
     */
    public function getUserChatIds(int $userId): array
    {
        $accounts = $this->getUserTelegramAccounts($userId);
        return array_map(function($account) {
            return $account->chat_id;
        }, $accounts);
    }
    
    /**
     * Проверяет, есть ли у пользователя активные Telegram аккаунты
     *
     * @param int $userId ID пользователя
     * @return bool
     */
    public function hasActiveTelegramAccounts(int $userId): bool
    {
        return !empty($this->getUserTelegramAccounts($userId));
    }
    
    /**
     * Парсит информацию об аккаунте из JSON
     *
     * @param string|null $tgAccount JSON с данными аккаунта
     * @return array
     */
    private function parseAccountInfo(?string $tgAccount): array
    {
        if (empty($tgAccount)) {
            return [];
        }
        
        try {
            $data = json_decode($tgAccount, true);
            return [
                'phone_number' => $data['phone_number'] ?? '',
                'first_name' => $data['first_name'] ?? '',
                'last_name' => $data['last_name'] ?? '',
                'username' => $data['username'] ?? ''
            ];
        } catch (\Exception $e) {
            Yii::warning('Ошибка парсинга данных Telegram аккаунта: ' . $e->getMessage(), 'telegram');
            return [];
        }
    }
    
    /**
     * Отправляет сообщение с клавиатурой
     *
     * @param string|int $chatId ID чата
     * @param string $message Текст сообщения
     * @param array $keyboard Клавиатура
     * @param array $options Дополнительные параметры
     * @return bool
     */
    public function sendMessageWithKeyboard($chatId, string $message, array $keyboard, array $options = []): bool
    {
        $options['reply_markup'] = json_encode(['inline_keyboard' => $keyboard]);
        return $this->sendMessage($chatId, $message, $options);
    }
    
    /**
     * Устанавливает webhook для Telegram бота
     *
     * @param string $url URL для webhook
     * @return bool
     */
    public function setWebhook(string $url): bool
    {
        try {
            $response = $this->telegramComponent->setWebhook(['url' => $url]);
            
            Yii::info([
                'message' => 'Telegram webhook установлен',
                'url' => $url,
                'response' => $response
            ], 'telegram');
            
            return true;
        } catch (\Exception $e) {
            Yii::error([
                'message' => 'Ошибка установки Telegram webhook',
                'url' => $url,
                'error' => $e->getMessage()
            ], 'telegram');
            
            return false;
        }
    }
    
    /**
     * Получает информацию о webhook
     *
     * @return array|null
     */
    public function getWebhookInfo(): ?array
    {
        try {
            return $this->telegramComponent->getWebhookInfo();
        } catch (\Exception $e) {
            Yii::error('Ошибка получения информации о webhook: ' . $e->getMessage(), 'telegram');
            return null;
        }
    }
    
    /**
     * Получает статистику по Telegram аккаунтам
     *
     * @return array
     */
    public function getAccountsStats(): array
    {
        try {
            $totalAccounts = TelegramAccount::find()
                ->andWhere(['or', ['isDeleted' => 0], ['isDeleted' => null]])
                ->count();
                
            $activeAccounts = TelegramAccount::find()
                ->andWhere(['or', ['isDeleted' => 0], ['isDeleted' => null]])
                ->andWhere(['!=', 'chat_id', ''])
                ->andWhere(['is not', 'chat_id', null])
                ->count();
                
            $usersWithTelegram = TelegramAccount::find()
                ->select('user_id')
                ->andWhere(['or', ['isDeleted' => 0], ['isDeleted' => null]])
                ->andWhere(['!=', 'chat_id', ''])
                ->andWhere(['is not', 'chat_id', null])
                ->distinct()
                ->count();
            
            return [
                'total_accounts' => $totalAccounts,
                'active_accounts' => $activeAccounts,
                'users_with_telegram' => $usersWithTelegram
            ];
        } catch (\Exception $e) {
            Yii::error('Ошибка получения статистики Telegram аккаунтов: ' . $e->getMessage(), 'telegram');
            return [
                'total_accounts' => 0,
                'active_accounts' => 0,
                'users_with_telegram' => 0
            ];
        }
    }
}
