<?php

namespace common\jobs;

use common\helpers\slave\file\File;
use common\models\ChipTarifs;
use common\models\ChipVehicle;
use yii\base\BaseObject;

class SlaveFilesJob extends BaseObject implements \yii\queue\JobInterface
{
    public $url;
    public $file;

    public function execute($queue)
    {
        $notFinishedSlaveFiles = File::getNotFinishedSlaveFiles();
        $result = [];
        foreach ($notFinishedSlaveFiles as $notFinishedSlaveFile) {
            $result[$notFinishedSlaveFile->id] = (new File($notFinishedSlaveFile))->startProcess();
        }
        var_dump($result);
    }
}