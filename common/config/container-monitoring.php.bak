<?php

use common\chip\core\interfaces\LoggerInterface;
use common\chip\monitoring\components\QueryProfiler;
use common\chip\monitoring\interfaces\ApiMonitoringServiceInterface;
use common\chip\monitoring\interfaces\MonitoringServiceInterface;
use common\chip\monitoring\interfaces\QueueMonitoringServiceInterface;
use common\chip\monitoring\services\ApiMonitoringService;
use common\chip\monitoring\services\MonitoringService;
use common\chip\monitoring\services\QueueMonitoringService;
use Yii;

return [
    // Регистрация интерфейсов и их реализаций
    MonitoringServiceInterface::class => function () {
        return new MonitoringService(
            Yii::$container->get(LoggerInterface::class),
            'ecu',
            'memory'
        );
    },
    QueueMonitoringServiceInterface::class => function () {
        return new QueueMonitoringService(
            Yii::$container->get(LoggerInterface::class),
            Yii::$container->get(MonitoringServiceInterface::class)
        );
    },
    ApiMonitoringServiceInterface::class => function () {
        return new ApiMonitoringService(
            Yii::$container->get(LoggerInterface::class),
            Yii::$container->get(MonitoringServiceInterface::class)
        );
    },
    QueryProfiler::class => function () {
        return new QueryProfiler(
            Yii::$container->get(LoggerInterface::class),
            Yii::$container->get(MonitoringServiceInterface::class)
        );
    },
];
