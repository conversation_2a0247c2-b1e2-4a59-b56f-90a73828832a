<?php

use common\chip\externalIntegrations\alientech\Application\Decoding\Handler\StartDecodingHandler;
use common\chip\externalIntegrations\alientech\Application\Encoding\Handler\StartEncodingHandler;

use common\chip\externalIntegrations\alientech\Domain\Decoding\Repository\DecodingRepositoryInterface;
use common\chip\externalIntegrations\alientech\Domain\Decoding\Service\DecodingDomainService;
use common\chip\externalIntegrations\alientech\Domain\Encoding\Repository\EncodingRepositoryInterface;
use common\chip\externalIntegrations\alientech\Domain\Encoding\Service\EncodingDomainService;

use common\chip\externalIntegrations\alientech\Infrastructure\Repository\DecodingRepository;
use common\chip\externalIntegrations\alientech\Infrastructure\Repository\EncodingRepository;
use common\chip\externalIntegrations\alientech\Infrastructure\ExternalService\AlientechApiClient;
use common\chip\externalIntegrations\alientech\Application\Decoding\Handler\AlientechApiClientInterface;

use common\chip\alientech\services\AlientechService;
use common\chip\event\EventDispatcher;

return [
    // ========================================
    // CORE INTERFACES
    // ========================================

    DecodingRepositoryInterface::class => DecodingRepository::class,
    EncodingRepositoryInterface::class => EncodingRepository::class,
    AlientechApiClientInterface::class => AlientechApiClient::class,

    // ========================================
    // DOMAIN SERVICES
    // ========================================

    DecodingDomainService::class => function ($container) {
        return new DecodingDomainService(
            $container->get(DecodingRepositoryInterface::class)
        );
    },

    EncodingDomainService::class => function ($container) {
        return new EncodingDomainService(
            $container->get(EncodingRepositoryInterface::class)
        );
    },

    // ========================================
    // INFRASTRUCTURE REPOSITORIES
    // ========================================

    DecodingRepository::class => function () {
        return new DecodingRepository();
    },

    EncodingRepository::class => function () {
        return new EncodingRepository();
    },

    // ========================================
    // INFRASTRUCTURE EXTERNAL SERVICES
    // ========================================

    AlientechApiClient::class => function ($container) {
        return new AlientechApiClient(
            $container->get(AlientechService::class)
        );
    },

    // ========================================
    // APPLICATION HANDLERS
    // ========================================

    StartDecodingHandler::class => function ($container) {
        return new StartDecodingHandler(
            $container->get(DecodingDomainService::class),
            $container->get(DecodingRepositoryInterface::class),
            $container->get(AlientechApiClientInterface::class),
            $container->get(EventDispatcher::class)
        );
    },

    StartEncodingHandler::class => function ($container) {
        return new StartEncodingHandler(
            $container->get(EncodingDomainService::class),
            $container->get(EncodingRepositoryInterface::class),
            $container->get(AlientechApiClientInterface::class),
            $container->get(EventDispatcher::class)
        );
    },

    // ========================================
    // LEGACY SERVICES (используем существующие)
    // ========================================

    AlientechService::class => function () {
        return new AlientechService();
    },

    EventDispatcher::class => function () {
        return Yii::$container->get(EventDispatcher::class);
    },

    // ========================================
    // FACADES (для удобства использования)
    // ========================================

    'alientech.decoding.facade' => function ($container) {
        return new class($container->get(StartDecodingHandler::class)) {
            public function __construct(
                private readonly StartDecodingHandler $startHandler
            ) {
            }

            public function startDecoding(int $projectId, int $fileId, string $filePath, ?string $callbackUrl = null): \common\chip\externalIntegrations\alientech\Domain\Decoding\Entity\DecodingOperation
            {
                $command = new \common\chip\externalIntegrations\alientech\Application\Decoding\Command\StartDecodingCommand(
                    projectId: $projectId,
                    fileId: $fileId,
                    filePath: $filePath,
                    callbackUrl: $callbackUrl
                );

                return $this->startHandler->handle($command);
            }
        };
    },

    'alientech.encoding.facade' => function ($container) {
        return new class($container->get(StartEncodingHandler::class)) {
            public function __construct(
                private readonly StartEncodingHandler $startHandler
            ) {
            }

            public function startEncoding(int $projectId, int $fileId, array $filePaths, ?string $callbackUrl = null): \common\chip\externalIntegrations\alientech\Domain\Encoding\Entity\EncodingOperation
            {
                $command = new \common\chip\externalIntegrations\alientech\Application\Encoding\Command\StartEncodingCommand(
                    projectId: $projectId,
                    fileId: $fileId,
                    filePaths: $filePaths,
                    callbackUrl: $callbackUrl
                );

                return $this->startHandler->handle($command);
            }
        };
    },

    // ========================================
    // TESTING DEPENDENCIES
    // ========================================

    'alientech.test.decoding_repository' => function () {
        return new DecodingRepository();
    },

    'alientech.test.encoding_repository' => function () {
        return new EncodingRepository();
    },

    'alientech.test.mock_api_client' => function () {
        return new class implements AlientechApiClientInterface {
            public function startDecoding(string $filePath, string $callbackUrl, array $userInfo): array
            {
                return [
                    'guid' => 'test_guid_' . uniqid(),
                    'slotGUID' => 'test_slot_' . uniqid(),
                ];
            }

            public function startEncoding(array $filePaths, string $callbackUrl, array $userInfo): array
            {
                return [
                    'guid' => 'test_guid_' . uniqid(),
                    'slotGUID' => 'test_slot_' . uniqid(),
                ];
            }

            public function getOperationStatus(string $externalOperationId): array
            {
                return [
                    'status' => 'in_progress',
                    'isCompleted' => false,
                ];
            }

            public function cancelOperation(string $externalOperationId): bool
            {
                return true;
            }
        };
    },
];
