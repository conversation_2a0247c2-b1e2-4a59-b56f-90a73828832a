<?php

use common\chip\event\core\FileProcessingErrorEvent;
use common\chip\event\core\FileUploadedEvent;
use common\chip\event\core\ProjectCreatedEvent;
use common\chip\notification\events\NoteAddedEvent;
use common\chip\notification\events\ProjectStatusChangedEvent;
use common\chip\notification\events\SystemErrorEvent;
use common\chip\notification\handlers\UniversalNotificationHandler;

return [
    'eventBus' => [
        'handlers' => [
            // Один обработчик для всех событий уведомлений
            UniversalNotificationHandler::class => [
                NoteAddedEvent::class,
                ProjectCreatedEvent::class,
                ProjectStatusChangedEvent::class,
                FileUploadedEvent::class,
                FileProcessingErrorEvent::class,
                SystemErrorEvent::class,
            ],
        ],
    ],
];
