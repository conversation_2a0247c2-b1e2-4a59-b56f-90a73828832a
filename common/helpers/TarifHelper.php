<?php

namespace common\helpers;

use common\models\ChipTarifs;

class TarifHelper
{
    const DEFAULT_TARIF_ID = 1;
    const DEFAULT_TARIF_PRICE = 1;

    /**
     * @return int
     */
    public static function getDefaultTarifId()
    {
        if(($defTarif = self::getDefaultTarif()) !== null) {
            return $defTarif->id;
        } else {
            return self::DEFAULT_TARIF_ID;
        }
    }

    /**
     * @return ChipTarifs|null
     */
    private static function getDefaultTarif() {
            return ChipTarifs::findOne(['isDefault' => 1]);
    }

    /**
     * @return int
     */
    public static function getDefaultTarifPrice()
    {
        if(($defTarif = self::getDefaultTarif()) !== null) {
            return $defTarif->id;
        } else {
            return self::DEFAULT_TARIF_ID;
        }
    }
}