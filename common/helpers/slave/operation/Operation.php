<?php
namespace common\helpers\slave\operation;

use common\models\SlaveOperation;

class Operation
{
    public $guid;
    public $asyncOperationType;
    public $slotGUID;
    public $status;
    public $isCompleted;
    public $recommendedPollingInterval;
    public $startedOn;
    public $completedOn;
    public $isSuccessful;
    public $hasFailed;
    public $error;
    public $result;

    public $file_id;
    public $errors;
    private $operationModel;

    /**
     * @return mixed
     */
    public function getOperationModel()
    {
        return $this->operationModel;
    }

    /**
     * Operation constructor.
     * @param $guid
     * @param $asyncOperationType
     * @param $slotGUID
     * @param $status
     * @param $isCompleted
     * @param $recommendedPollingInterval
     * @param $startedOn
     * @param $completedOn
     * @param $isSuccessful
     * @param $hasFailed
     * @param $error
     * @param $result
     */
    public function __construct($guid, $asyncOperationType, $slotGUID, $status, $isCompleted, $recommendedPollingInterval, $startedOn, $completedOn, $isSuccessful, $hasFailed, $error, $result)
    {
        $this->guid = $guid;
        $this->asyncOperationType = $asyncOperationType;
        $this->slotGUID = $slotGUID;
        $this->status = $status;
        $this->isCompleted = $isCompleted;
        $this->recommendedPollingInterval = $recommendedPollingInterval;
        $this->startedOn = $startedOn;
        $this->completedOn = $completedOn;
        $this->isSuccessful = $isSuccessful;
        $this->hasFailed = $hasFailed;
        $this->error = $error;
        $this->result = $result;
    }

    public function save(){
        if (($slaveOperation = SlaveOperation::find()->where(['guid' => $this->guid])->one()) == null){
            $slaveOperation = new SlaveOperation();
        }
        $slaveOperation->setOperation($this);
        $slaveOperation->setAttributes($this->toArray());
        if (!$slaveOperation->save()) {
            $this->errors = $slaveOperation->errors;
        }
        $this->operationModel = $slaveOperation;
    }


    public function toArray()
    {
        return get_object_vars($this);
    }
    /**
     * @return mixed
     */
    public function getIsCompleted()
    {
        return (int)$this->isCompleted;
    }

    /**
     * @param mixed $isCompleted
     */
    public function setIsCompleted($isCompleted): void
    {
        $this->isCompleted = (bool)$isCompleted;
    }

    /**
     * @return mixed
     */
    public function getIsSuccessful()
    {
        return (int)$this->isSuccessful;
    }

    /**
     * @param mixed $isSuccessful
     */
    public function setIsSuccessful($isSuccessful): void
    {
        $this->isSuccessful = (bool)$isSuccessful;
    }

    /**
     * @return mixed
     */
    public function getHasFailed()
    {
        return (int)$this->hasFailed;
    }

    /**
     * @param mixed $hasFailed
     */
    public function setHasFailed($hasFailed): void
    {
        $this->hasFailed = (bool)$hasFailed;
    }

    /**
     * @return mixed
     */
    public function getFileId()
    {
        return $this->file_id;
    }

    /**
     * @param mixed $file_id
     */
    public function setFileId($file_id): void
    {
        $this->file_id = $file_id;
    }

    /**
     * @return mixed
     */
    public function getResult()
    {
        return $this->result;
    }

    /**
     * @param mixed $result
     */
    public function setResult($result): void
    {
        $this->result = $result;
    }
    /**
     * @return mixed
     */
    public function getError()
    {
        return $this->error;
    }

    /**
     * @param mixed $error
     */
    public function setError($error): void
    {
        $this->error = $error;
    }

}
