<?php


namespace common\helpers\slave\services;


use common\helpers\MessageHelper;
use common\helpers\ProjectHelper;
use common\helpers\slave\operation\Operation;
use common\models\ProjectFiles;
use Yii;
use yii\httpclient\Client;

class OperationBaseService implements IOperationService
{
    const MAXIMUM_KESS_FILE_SLOTS = 3;
    const MAXIMUM_KTAG_FILE_SLOTS = 3;

    const KtagFileTypeReadBackup = 1;
    const KtagFileTypeDecodedMicro = 3;
    const KtagFileTypeDecodedFlash = 4;
    const KtagFileTypeDecodedEEPROM = 5;
    const KtagFileTypeDecodedMap = 6;
    const KtagFileTypeModifiedMicro = 7;
    const KtagFileTypeModifiedFlash = 8;
    const KtagFileTypeModifiedEEPROM = 9;
    const KtagFileTypeModifiedMap = 10;
    const KtagFileTypeEncodedBackup = 11;

    const FileTypeEncoded = 'EncodedBackup';

    const FileTypeModified = 'Modified';
    const ERROR_26 = '-26';

    const ERROR_KTAG_Z_LOAD = 'KTAG_Z_LOAD';
    const ERROR_KV2_INVALID_FILE = 'KV2_INVALID_FILE';
    const operationTypeKess = 36;
    const operationTypeKtag = 37;

    protected $userPerformed = 'CL365';

    private $data;

    protected $interval;

    private $token;
    private $request;
    protected static $apiUrl = 'https://encodingapi.alientech.to';
    private $client;
    protected $slots;
    protected $file;
    public $slaveFile;
    public $projectFile;
    protected $slotGuid;
    protected $errors = [];

    public static $clientApplicationGUID = 'c7f43304-624b-42b9-99f8-9d36c9f234d3';
    public static $secretKey = '?,;NTnD8XD>5X9-b25YnTg';
    protected $accessToken = '';

    public function getOperationData()
    {
    }

    public function start()
    {
        $this->beforeStart();

        if (!$this->checkSlots()) {
            $this->log('slots is empty');
            $this->closeAllSlots();
            self::start();
        }

        try {
            $processOperationData = $this->processOperation();
            $this->log('$processOperationData');
            $this->log($processOperationData);
            if ($processOperationData) {
                return $this->successFinish();
            }
            return $this->errorStart();
        } catch(\Exception $exception) {
            $this->log('catch(\Exception $exception)');
            $this->log($exception->getMessage());
            return $this->errorStart();
        }
    }

    public function beforeStart()
    {
        $this->log('beforeStart////');
        $this->authenticate();
        $this->createClient();
        $this->createRequest();
        $this->log('////beforeStart');
    }

    public function successFinish()
    {
        $this->log('successFinish////');
        $this->slaveFile->setAttribute('finished', 1);
        $this->slaveFile->setAttribute('slot_guid', $this->slotGuid);
        $this->slaveFile->save();
        $this->log('////successFinish');
        return true;
    }

    public function errorStart()
    {
        $this->log('errorStart////');

        $this->log(json_encode($this->errors));

//        $this->slaveFile->setAttribute('finished', 0);

        if (count($this->errors) > 0) {
            $this->log('count($this->errors) > 0');
            $errorMessageToProjectSended = false;
            foreach($this->errors as $key => $error) {
//                var_dump($key);
//                var_dump($error);
//                var_dump(array_key_exists('isSuccessful_false', $error));
                if (is_array($error)) {
                    if (array_key_exists('isSuccessful_false', $error)) {
                        $this->log('array_key_exists_isSuccessful_false');
                        $this->changeProjectByError($error['isSuccessful_false']->errorName);
                        $this->restartFile();
                    }
                }
            }
        }

        $this->slaveFile->updateCounters(['errors_count' => 1]);

        // если ошибок у файла более 2 - то принудительно завершаем файл, чтобы он не крутился бесконечно
        if ($this->slaveFile->errors_count > 2) {
            $this->log($this->slaveFile->errors_count);
            $this->slaveFile->setAttribute('finished', 1);
            $this->setMessageToProject("error", "sleep", json_encode($this->errors));
            $this->makeProjectIsError();
        }
        $this->log('////errorStart');

        return $this->slaveFile->save();
    }

    private function restartFile(){
        $this->slaveFile->setAttribute('finished', 0);
        $this->slaveFile->setAttribute('blocked', 0);
        $this->slaveFile->save();
    }

    public function checkOperationReadmethodErrors()
    {
        $this->log('checkOperationReadmethodErrors////');

        if (is_null($this->slaveOperation->error)) {
            $this->log('is_null($this->data->error)');
            return null;
        }

        $this->log($this->slaveOperation->error);

        if (!isset($this->slaveOperation->error->errorName)) {
            $this->log('!isset($this->data->error->errorName)');
            return null;
        }

        $this->changeProjectByError($this->slaveOperation->error->errorName);

        $this->log('////checkOperationReadmethodErrors');
    }

    private function changeProjectByError($errorName)
    {
        $this->log('changeProjectByError////');
        $project = $this->projectFile->project;

        $this->log('$project==='. $project->id);

        $this->log('$project old readmethod_id==='. $project->readmethod_id);

        if ($errorName == self::ERROR_26) {
            $project->setAttribute('readmethod_id', self::operationTypeKtag);
        }

        if ($errorName == self::ERROR_KTAG_Z_LOAD) {
            $project->setAttribute('readmethod_id', self::operationTypeKess);
        }

        if ($errorName == self::ERROR_KV2_INVALID_FILE) {
            $project->setAttribute('readmethod_id', self::operationTypeKtag);
        }

        $this->log('$project new readmethod_id==='. $project->readmethod_id);
        if ($project->save(false)){
            $this->log('$project->save(false)');
            $this->slaveFile->setAttribute('blocked', 0);
            $this->slaveFile->save();
        }

        $this->log('////changeProjectByError');
    }

    protected function createOperation($data): Operation
    {
        return new Operation(
            $data->guid,
            $data->asyncOperationType,
            $data->slotGUID,
            $data->status,
            $data->isCompleted,
            $data->recommendedPollingInterval,
            $data->startedOn,
            $data->completedOn,
            $data->isSuccessful,
            $data->hasFailed,
            $data->error,
            $data->result
        );
    }

    private function createClient(){
        $this->client = new Client([
            'baseUrl' => self::$apiUrl,
            'contentLoggingMaxSize' => 1000000,
            'responseConfig' => [
                'format' => Client::FORMAT_JSON
            ],
        ]);
    }

    private function createRequest()
    {
        $this->request = $this->client
            ->createRequest()
            ->setHeaders(['X-Alientech-ReCodAPI-LLC' => $this->accessToken]);

    }

    protected function checkSlots() : bool
    {
        $this->slots = $this->getSlots();
        return $this->hasOpenFileSlots();
    }

    private function getSlots()
    {
        if (!$this->authenticate()) {
            $this->addError('auth_error');
            return null;
        }
        $response = $this->getRequest()
            ->setMethod('GET')
            ->setUrl('/api/'.$this->apiFileTypePart.'/file-slots')
            ->send();

        return $this->checkResponce($response);

    }
    public function closeAllSlots(): array
    {
        $this->log('closeAllSlots////');
        $result = [];
        if (count($this->slots) > 0) {
            foreach ($this->slots as $slot) {
                if (!$slot->isClosed) {
                    $startedOn = new \DateTime($slot->createdOn);
                    $dateNow = new \DateTime('now');
                    $result['dateDiff'] = date_diff($dateNow, $startedOn);
                    $result['closeSlot'] = $this->closeSlot($slot->guid);
                }
            }
        }
        $this->log('////closeAllSlots');
        return $result;
    }

    public function checkOperationData()
    {
        if (!$this->authenticate()) {
            $this->addError('auth_error');
            return null;
        }
        $url = '/api/async-operations/'.$this->slaveOperation->guid;

        $response = $this->request
            ->setMethod('GET')
            ->setUrl($url)
            ->send();

        return $this->checkResponce($response);
    }

    /**
     * REV-4 OPERATIONS Decode a file
     */
    public function decodeFile()
    {
        $this->log('decodeKessFile////');

        if (!$this->authenticate()) {
            $this->addError('auth_error');
            return null;
        }

        $url = '/api/'.$this->apiFileTypePart.'/decode-read-file/'.$this->userPerformed;

        $response = $this->request
            ->setMethod('POST')
            ->setUrl($url)
            ->addFile('readFile', $this->projectFile->path)
            ->send();

        $this->log('////decodeKessFile');

        return $this->checkResponce($response);

    }

    public function encodeKessFile($FileGUID)
    {
        $this->log('encodeKessFile////');
        if (!$this->authenticate()) {
            $this->addError('auth_error');
            return null;
        }
        if (is_null($this->openSlot($this->slaveFile->slot_guid))){
            $this->log($this->getErrors());
        }

        $url = '/api/'.$this->apiFileTypePart.'/encode-file';

        $response = $this->client->createRequest()
            ->setHeaders(['X-Alientech-ReCodAPI-LLC' => $this->accessToken])
            ->setFormat(Client::FORMAT_JSON)
            ->setMethod('POST')
            ->setUrl($url)
            ->setData(['userCustomerCode' => $this->userPerformed, 'kessv2FileSlotGUID' => $this->slaveFile->slot_guid, 'modifiedFileGUID' => $FileGUID])
            ->send();

        if (is_null($this->closeSlot($this->slaveFile->slot_guid))){
            $this->log($this->getErrors());
        }

        $this->log('////encodeKessFile');

        return $this->checkResponce($response);
    }

    public function encodeKtagFiles($files)
    {
        $this->log('encodeKtagFiles////');

        if (!$this->authenticate()) {
            $this->addError('auth_error');
            return null;
        }
        if (is_null($this->openSlot($this->slaveFile->slot_guid))){
            $this->log($this->getErrors());
        }

        $data = ['userCustomerCode' => $this->userPerformed, 'ktagFileSlotGUID' => $this->slaveFile->slot_guid];

        $url = '/api/ktag/encode-file';
        foreach ($files as $key => $filePart) {
            if (isset($filePart->guid)) {
                $data[$key] = $filePart->guid;
            }
        }

        $response = $this->client->createRequest()
            ->setHeaders(['X-Alientech-ReCodAPI-LLC' => $this->accessToken])
            ->setFormat(Client::FORMAT_JSON)
            ->setMethod('POST')
            ->setUrl($url)
            ->setData($data)
            ->send();

        if (is_null($this->closeSlot($this->slaveFile->slot_guid))){
            $this->log($this->getErrors());
        }

        $this->log('////encodeKtagFiles');

        return $this->checkResponce($response);
    }

    protected function saveFileToStorage($filePath, $fileBase64Data): bool
    {
        $data_decoded = base64_decode ($fileBase64Data);

        $savedFile = fopen ($filePath,'w');

        if (fwrite ($savedFile, $data_decoded)) {
            fclose ($savedFile);
            return true;
        }
        return false;
    }

    public function setMessageToProject($errorTitle, $errorMessage, $content)
    {
        ProjectHelper::createProjectMessage(
            $this->projectFile->project_id,
            $errorTitle,
            $errorMessage,
            $content,
            1, 0, 0, 0, MessageHelper::TYPE_SYS, ['send_to' => 'admin']);

    }

    public function setMessagesForDecodedFile($projectFile, $messageStart)
    {
        $fileInfo = (array)$this->slaveOperation->result->information;

        $content = '';

        foreach ($fileInfo as $key => $value) {
            $content .= $key . ' : ' . $value . '<br>';
        }

        ProjectHelper::createProjectMessage(
            $projectFile->project_id,
            $messageStart,
            $messageStart . $projectFile->filename,
            $content,
            1, 0, 0, 0, MessageHelper::TYPE_SYS, ['send_to' => 'admin']);

        $project = $projectFile->project;

        ProjectHelper::createProjectMessage(
            $project->id,
            $messageStart,
            $messageStart
            . $project->brand->title. ' '
            . $project->model->title. ' '
            . $project->year. ' '
            . $project->engine->title,
            $projectFile->title . (!empty($projectFile->extension)? '.' . $projectFile->extension : ''),
            0, 0, 0 , 0, MessageHelper::TYPE_NOTE, ['send_to' => 'admin']);

        $project->setAttributes([
            'status_admin' => ProjectHelper::STATUS_CHANGED,
            'updated_by' => $project->created_by,
        ]);

        $project->save(false);

        ProjectHelper::createProjectMessage($project->id, 'Reload', 'Reload', 'Reload', 0, 0, 0 , 0, MessageHelper::TYPE_RELOAD, ['alientech' => true, 'send_to' => 'admin']);

        return true;
    }

    public function setMessagesForEncodedFile($project, $projectFile, $messageStart)
    {
        ProjectHelper::createProjectMessage(
            $project->id,
            'File uploaded',
            $messageStart. ' '
            . $project->brand->title. ' '
            . $project->model->title. ' '
            . $project->year. ' '
            . $project->engine->title,
            $projectFile->filename,
            0, 0, 0 , 1, MessageHelper::TYPE_NOTE);

        $project->setAttributes(['status' => ProjectHelper::STATUS_CHANGED]);
        $project->save(false);

        ProjectHelper::createProjectMessage($project->id, 'Reload', 'Reload', 'Reload', 0, 0, 0 , 0, MessageHelper::TYPE_RELOAD);
        ProjectHelper::createProjectMessage($project->id, 'Reload', 'Reload', 'Reload', 0, 0, 0 , 0, MessageHelper::TYPE_RELOAD, ['send_to' => 'admin']);

        return true;
    }
    /**
     * REV-4 скачивание файла
     * @param string $fileUrl
     * @return
     */
    public function downloadFile($fileUrl = '')
    {
        if (!$this->authenticate()) {
            $this->addError('authenticate_false');
            $this->log($this->getErrors());
            return null;
        }

        if (is_null($this->openSlot($this->slaveFile->slot_guid))){
            $this->log($this->getErrors());
        }

        $response = $this->request
            ->setMethod('GET')
            ->setUrl($fileUrl)
            ->send();

        if (is_null($this->closeSlot($this->slaveFile->slot_guid))){
            $this->log($this->getErrors());
        }

        return $this->checkResponce($response);
    }

    /**
     * @param $response
     * @return mixed
     */
    protected function checkResponce(yii\httpclient\Response $response)
    {
        $this->log('checkResponce////');

        if (!$response->isOk){
            $this->addError('response_status='.$response->getStatusCode());
            $this->addError('response='.$response->toString());
            $this->addError($response->getContent());
            $this->log($this->getErrors());
            return null;
        }

        $content = $response->getContent();

        $this->log('////checkResponce');
        return json_decode($content);
    }

    protected function getFileBySlot($fileType = 'Read')
    {
        $this->log('getFileBySlot////');

        if (!$this->authenticate()) {
            $this->addError('authenticate_false');
            $this->log($this->getErrors());
            return null;
        }
        if (is_null($this->openSlot($this->slaveFile->slot_guid))){
            $this->log($this->getErrors());
        }

        $options = ['fileType' => $fileType];

        $url = '/api/'.$this->apiFileTypePart.'/file-slots/'.$this->slaveFile->slot_guid.'/files/?'.http_build_query($options);

        $response = $this->getRequest()
            ->setMethod('GET')
            ->setUrl($url)
            ->send();

        if (is_null($this->closeSlot($this->slaveFile->slot_guid))){
            $this->log($this->getErrors());
        }

        $this->log('////getFileBySlot');

        return $this->checkResponce($response);
    }

    protected function uploadModFile($url, $slotGuid, $projectFile)
    {
        $this->log('uploadModFile////');

        $client = new Client([
            'baseUrl' => self::$apiUrl,
            'contentLoggingMaxSize' => 1000000,
            'responseConfig' => [
                'format' => Client::FORMAT_JSON
            ],
        ]);

        if (is_null($this->openSlot($slotGuid))){
            $this->log($this->getErrors());
        }

        $request = $client->createRequest()
            ->setHeaders(['X-Alientech-ReCodAPI-LLC' => $this->accessToken])
            ->setMethod('PUT')
            ->setUrl($url)
            ->addFile('file', $projectFile->path);

        $response = $request->send();

        if (is_null($this->closeSlot($slotGuid))){
            $this->log($this->getErrors());
        }

        $this->log('////uploadModFile');

        return $this->checkResponce($response);

    }

    protected function openSlot($slotGuid = '')
    {
        $this->log('openSlot////');
        if (empty($slotGuid)) {
            $this->addError('openSlot_slot_is_empty');
            $this->log($this->getErrors());
            return null;
        }

        if (!$this->authenticate()) {
            $this->addError('authenticate_false');
            $this->log($this->getErrors());
            return null;
        }

        $url = '/api/'.$this->apiFileTypePart.'/file-slots/'.$slotGuid.'/reopen';

        $response = $this->request
            ->setMethod('POST')
            ->setUrl($url)
            ->send();

        $this->log('////openSlot');
        return $this->checkResponce($response);
    }

    protected function closeSlot($slotGuid = '')
    {
        $this->log('closeSlot////');

        if (empty($slotGuid)) {
            $this->addError('closeSlot_slotGuid_is_empty');
            $this->log($this->getErrors());
            return null;
        }

        if (!$this->authenticate()) {
            $this->addError('auth_error');
            return null;
        }

        $url = '/api/'.$this->apiFileTypePart.'/file-slots/'.$slotGuid.'/close';

        $this->log($url);

        $response = $this->request
            ->setMethod('POST')
            ->setUrl($url)
            ->send();

        $this->log('////closeSlot');

        return $this->checkResponce($response);
    }

    public function authenticate():bool
    {
        if ($this->accessToken) {
            return true;
        }

        $client = new Client([
            'baseUrl' => self::$apiUrl,
            'contentLoggingMaxSize' => 1000000,
            'responseConfig' => [
                'format' => Client::FORMAT_JSON
            ],
        ]);

        $client->requestConfig = [
            'format' => Client::FORMAT_JSON
        ];

        $response = $client->createRequest()
            ->setMethod('POST')
            ->setUrl('/api/access-tokens/request')
            ->setData(['clientApplicationGUID' => self::$clientApplicationGUID, 'secretKey' => self::$secretKey])
            ->send();

        $respData = $this->checkResponce($response);
        if (isset($respData->accessToken) && !empty($respData->accessToken)) {
            $this->accessToken = $respData->accessToken;
            return true;
        }

        return false;

    }

    /**
     * REV-4 Проверка количества доступных слотов
     * @param $slots
     * @return bool
     */
    protected function hasOpenFileSlots():bool
    {
        $result = true;

        $openedSlots = 0;

        if (!is_null($this->slots) && (count($this->slots) >0)) {
            foreach ($this->slots as $slot) {
                if (!$slot->isClosed) {
                    $openedSlots++;
                }
            }

            if ($openedSlots >= self::MAXIMUM_KESS_FILE_SLOTS) {
                $result = false;
            }
        }

        return $result;
    }

    /**
     * @return Client
     */
    public function getClient(): Client
    {
        return $this->client;
    }

    /**
     * @return mixed
     */
    public function getRequest()
    {
        return $this->request;
    }

    public function setFile(\common\helpers\slave\file\File $file)
    {
        $this->file = $file;
    }

    public function getFile() : \common\helpers\slave\file\File
    {
        return $this->file;
    }

    /**
     * @return mixed
     */
    public function getData()
    {
        return $this->data;
    }

    /**
     * @param mixed $data
     */
    public function setData($data): void
    {
        $this->data = $data;
    }

    public function addError($error): void
    {
        $this->errors[] = $error;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * @param mixed $slaveFile
     */
    public function setSlaveFile($slaveFile): void
    {
        $this->slaveFile = $slaveFile;
    }

    public function log($string){
//        error_log(date('Y-m-d H:i:s').' -- '.$string."\r\n", 3, Yii::getAlias('@storage') . '/startalientechConsole.log');
        echo date('Y-m-d H:i:s').' -- '.json_encode($string)."\r\n";
    }

    protected function getInterval()
    {
        return $this->interval;
    }

    /**
     * @param mixed $interval
     */
    public function setInterval($interval): void
    {
        $this->interval = $interval;
    }

    protected function makeProjectIsError()
    {
        $this->log('makeProjectIsError////');
        $project = $this->projectFile->project;

        $this->log('$project==='. $project->id);

        $project->setAttribute('status_admin', ProjectHelper::STATUS_ERROR);

        if ($project->save(false)){
            $this->log('$project->save(false)');
            $this->slaveFile->setAttribute('blocked', 1);
            $this->slaveFile->save();
        }
        $this->log('////makeProjectIsError');

    }


}
