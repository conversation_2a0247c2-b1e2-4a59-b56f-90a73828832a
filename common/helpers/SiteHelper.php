<?php

namespace common\helpers;

use common\models\ProjectMessages;
use common\models\User;
use Yii;
use yii\helpers\Url;
use yii\httpclient\Client;

class SiteHelper
{


    public static function workTime($days)
    {
        $i=1;
        foreach ($days as $hours) {
            $workTimeArray[$i] = $hours;
            $i++;
        }


        $now = new \DateTime('now', new \DateTimeZone('Europe/Kiev'));
        $today = (int)$now->format('w');
        $currentHour = (int)$now->format('G');
//        var_dump($currentHour);die;
        if (key_exists($today, $workTimeArray)) {
            $todayHours = $workTimeArray[$today];
            if ($currentHour >= (int)$todayHours[0] && $currentHour < (int)$todayHours[1]) {
                return true;
            }
        }
//        die;
        return false;
    }
}