<?php

namespace common\helpers\alientech\steps;
use common\helpers\alientech\steps\Step;
use common\helpers\alientech\steps\StepClosed;
use common\helpers\AlientechApiHelper;
use common\models\AlientechOperation;

class StepEncodedFileDownloaded extends Step
{
    public function __construct(AlientechOperation $operation)
    {
        $this->nextStep = new StepClosed($operation);
        $this->number = AlientechApiHelper::OperationStep_encoded_file_downloaded;
        parent::__construct($operation);
    }

    public function handleKess(): void
    {
        echo "EncodedFileDownloaded kess!";
    }

    public function handleKtag(): void
    {
        echo "EncodedFileDownloaded ktag!";
    }
}
