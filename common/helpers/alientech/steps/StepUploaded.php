<?php

namespace common\helpers\alientech\steps;
use common\helpers\alientech\steps\Step;
use common\helpers\alientech\steps\StepEncoding;
use common\helpers\AlientechApiHelper;
use common\models\AlientechOperation;

class StepUploaded extends Step
{
    public function __construct(AlientechOperation $operation)
    {
        $this->nextStep = new StepEncoding($operation);
        $this->number = AlientechApiHelper::OperationStep_uploaded_mod;
        parent::__construct($operation);
    }

    public function handleKess(): void
    {
        echo "Uploaded kess!";
    }

    public function handleKtag(): void
    {
        echo "Uploaded ktag!";
    }
}
