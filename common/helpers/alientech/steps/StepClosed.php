<?php

namespace common\helpers\alientech\steps;
use common\helpers\alientech\steps\Step;
use common\helpers\AlientechApiHelper;
use common\models\AlientechOperation;

class StepClosed extends Step
{
    public function __construct(AlientechOperation $operation)
    {
        $this->nextStep = $this;
        $this->number = AlientechApiHelper::OperationStep_closed;
        parent::__construct($operation);
    }

    public function handleKess(): void
    {
        echo "Closed kess!";
    }

    public function handleKtag(): void
    {
        echo "Closed ktag!";
    }
}
