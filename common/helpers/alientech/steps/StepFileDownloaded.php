<?php

namespace common\helpers\alientech\steps;
use common\helpers\alientech\steps\Step;
use common\helpers\alientech\steps\StepUploaded;
use common\helpers\AlientechApiHelper;
use common\models\AlientechOperation;

class FileDownloaded extends Step
{
    public function __construct(AlientechOperation $operation)
    {
        $this->nextStep = new StepUploaded($operation);
        $this->number = AlientechApiHelper::OperationStep_file_downloaded;
        parent::__construct($operation);
    }

    public function handleKess(): void
    {
        echo "FileDownloaded kess!";
    }

    public function handleKtag(): void
    {
        echo "FileDownloaded ktag!";
    }
}
