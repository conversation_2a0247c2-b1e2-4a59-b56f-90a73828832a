<?php

namespace common\helpers\alientech\steps;
use common\helpers\alientech\steps\Step;
use common\helpers\alientech\steps\StepEncodedFileDownloaded;
use common\helpers\AlientechApiHelper;
use common\models\AlientechOperation;

class StepEncoded extends Step
{
    public function __construct(AlientechOperation $operation)
    {
        $this->nextStep = new StepEncodedFileDownloaded($operation);
        $this->number = AlientechApiHelper::OperationStep_encoded;
        parent::__construct($operation);
    }

    public function handleKess(): void
    {
        echo "Encoded kess!";
    }

    public function handleKtag(): void
    {
        echo "Encoded ktag!";
    }
}
