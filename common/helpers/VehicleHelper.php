<?php

namespace common\helpers;

use common\models\ChipTarifs;
use common\models\ChipVehicle;

class VehicleHelper
{
    const DEFAULT_VEHICLE_ID = 1;

    public static function getDefaultVehicleId()
    {
        if($defVehicle = ChipVehicle::findOne(['isDefault' => 1]) !== null) {
            return $defVehicle->id;
        } else {
            return self::DEFAULT_VEHICLE_ID;
        }
    }

    public static function getStatusLabel($type, $default = null)
    {
        $types = static::getStatuses();
        return isset($types[$type]) ? $types[$type] : $default;
    }
}