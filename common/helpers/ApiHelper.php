<?php

namespace common\helpers;

use common\models\ChipEcu;
use common\models\ProjectMessages;
use common\models\SeoChipPage;
use common\models\User;
use Yii;
use yii\helpers\Inflector;
use yii\helpers\Url;
use yii\httpclient\Client;

class ApiHelper
{

    public static function findClient1C($name = '')
    {
        $result = [
            'data' => [],
            'status' => 'error',
        ];
        if ($name) {
            $client = new Client([
                'baseUrl' => 'http://1c.msgroup.ua/master_service83/hs/Get/FindPartner/',
                'requestConfig' => [
                    'format' => Client::FORMAT_JSON
                ],
                'responseConfig' => [
                    'format' => Client::FORMAT_JSON
                ],
            ]);

            $response = $client->createRequest()
                ->setMethod('POST')
//                ->setUrl('articles/search')
                ->addHeaders(['Authorization' => 'Basic '.base64_encode('ROBOT:iX8OFewnS9YP5StzZQ9C')])
                ->setData(['search_txt' => $name])
                ->send();

//            print_r($response->getContent());
//            die;
            if (!empty($response->getContent() && null !== json_decode($response->getContent()) && isset(json_decode($response->getContent())->Answer))) {
                $answer = json_decode($response->getContent())->Answer;
                if (!empty($answer)) {
                    $result['results'] = $answer;
                    $result['status'] = 'success';

//                    foreach ($answer as $clientData) {
//                        var_dump($clientData);
//                    }
                }
//                var_dump(json_decode($response->getContent())->Answer);

            }
//            var_dump($response);
//            die;
        } else {
            $result['error'] = 'name empty';
        }
        return $result;
    }

    public static function sendTelegramMessage($message = null, $sender = null, $recipient = null, $projectMessageSend = null)
    {
        if ($message && $sender && $recipient && $projectMessageSend) {
            Yii::$app->telegram->sendMessage([
                'chat_id' => $recipient->tg_chat_id,
                'text' => $message->content,
            ]);

        $projectMessageSend->setAttribute('telegram_sended', 1);
        $projectMessageSend->save(false);

        }
    }

    public static function sendSms($message = null, $sender = null, $recipient = null, $projectMessageSend = null)
    {
//        https://pbx.msgroup.ua/api/smssend.php?login=msua&password=hJw1an263&command=send&from=MasterServ&to=380982841515&source=site&user=chiptuning-ms.business&message=Hello,%20World
        if ($message && $sender && $recipient && $projectMessageSend && !empty($recipient->phone)) {
            $client = new Client();
            $response = $client->createRequest()
                ->setMethod('GET')
                ->setFormat(Client::FORMAT_URLENCODED)
                ->setUrl('https://pbx.msgroup.ua/api/smssend.php')
                ->setData([
                    'login' => 'msua',
                    'password' => 'hJw1an263',
                    'command' => 'send',
                    'from' => 'MasterServ',
                    'to' => $recipient->phone,
                    'source' => 'site',
                    'user' => 'chiptuning-ms.business',
                    'message' => $message->content,
                ])
                ->send();
            if ($response->isOk) {
                $projectMessageSend->setAttribute('sms_sended', 1);
                $projectMessageSend->save(false);
            }
        }
    }

    private static function logMessage($message = null, $sender = null, $recipient = null)
    {
        if ($message && $sender && $recipient) {
            Yii::debug($message->title.''.$message->comment .''.$message->content  );
        }

    }

    public static function send(\common\models\ProjectMessagesSend $projectMessageSend)
    {
        $message = ProjectMessages::findOne($projectMessageSend->message_id);
        $user = \Yii::$app->user;
        $sender = null;
        $recipient = null;
        if (isset($message->project) && $message->project->created_by !== $user->id) {
//          Действие производил не создатель проекта, отправка уведомления создателю проекта
//              Отправитель будет админ сайта
                $sender = User::findOne(1);
                $recipient = $message->project->creator;

//                if ($user->can('manager')) {
//              Проект создал не менеджер, отправка уведомления создателю проекта
//            } else {
////          Пользователь
//                $sender = $message->project->creator;
//                $recipient = User::findOne(1);
//            }

        } else {

//          Действие производил создатель проекта, отправка уведомления администрации
            $sender = $message->project->creator;
            $recipient = User::findOne(1);

        }
        if ($projectMessageSend->sms) {
            self::sendSms($message, $sender, $recipient, $projectMessageSend);
        }
        if ($projectMessageSend->mail) {
            self::sendMailMessage($message, $sender, $recipient, $projectMessageSend);
        }
        if ($projectMessageSend->telegram) {
            self::sendTelegramMessage($message, $sender, $recipient, $projectMessageSend);
        }
        self::logMessage($message, $sender, $recipient, $projectMessageSend);
    }

    /**
     * Метод поиска клиента по номеру заказ-наряда
     * @param $orderNum
     * @return array
     * @throws \yii\base\InvalidConfigException
     */
    public static function findClient1CByOrderNum($orderNum)
    {
        $result = [
            'data' => [],
            'status' => 'error',
        ];
        if ($orderNum) {
            $client = new Client([
                'baseUrl' => 'http://1c.msgroup.ua/master_service83/hs/Get/GetPartnerByOrderNum/steering.com.ua',
                'requestConfig' => [
                    'format' => Client::FORMAT_JSON
                ],
                'contentLoggingMaxSize' => 1000000,
                'responseConfig' => [
                    'format' => Client::FORMAT_JSON
                ],
            ]);

            $response = $client->createRequest()
                ->setMethod('POST')
//                ->setUrl('articles/search')
                ->addHeaders(['Authorization' => 'Basic '.base64_encode('ROBOT:iX8OFewnS9YP5StzZQ9C')])
                ->setData(['Num' => $orderNum])
                ->send();
            if (!empty($response->getContent())) {
//                $answer = json_decode($response->getContent())->Answer;
//                print_r($response->getContent());
//                die;

//                if (!empty($answer)) {
                return json_decode($response->getContent());

//                }
            }
        } else {
            $result['error'] = 'num empty';
        }
        return $result;
    }


    /**
     * Метод получения данных заказ-наряда из 1С
     * @param $orderNum
     * @return array
     * @throws \yii\base\InvalidConfigException
     */
    public static function findOrderData1CByOrderNum($orderNum)
    {
        $result = [
            'data' => [],
            'status' => 'error',
        ];
        if ($orderNum) {
            $client = new Client([
                'baseUrl' => 'http://1c.msgroup.ua/master_service83/hs/Get/CheckOrderByNum/steering.com.ua',
                'requestConfig' => [
                    'format' => Client::FORMAT_JSON
                ],
                'contentLoggingMaxSize' => 1000000,
                'responseConfig' => [
                    'format' => Client::FORMAT_JSON
                ],
            ]);

            $response = $client->createRequest()
                ->setMethod('POST')
//                ->setUrl('articles/search')
                ->addHeaders(['Authorization' => 'Basic '.base64_encode('ROBOT:iX8OFewnS9YP5StzZQ9C')])
                ->setData(['Num' => $orderNum])
                ->send();
            if (!empty($response->getContent() && null !== json_decode($response->getContent()) && isset(json_decode($response->getContent())->Answer))) {
//                $answer = json_decode($response->getContent())->Answer;
//                var_dump(json_decode($response->getContent()));
//                die;

//                if (!empty($answer)) {
                $result['result'] = json_decode($response->getContent());
                $result['status'] = 'success';

//                }
            }
        } else {
            $result['error'] = 'num empty';
        }
        return $result;
    }

    public static function generateSeoChipPage(){
        $res = [];
        $emptyUrls = ChipEcu::find()->where('id not in (select chip_ecu_id from  seo_chip_page)')->all();
        foreach( $emptyUrls as $chipEcu){
            if (($seoChipPage = SeoChipPage::findOne(['chip_ecu_id' => $chipEcu->id]) == null)) {
                $seoChipPage = new SeoChipPage();
                $titleString = $chipEcu->brand->title.' '.$chipEcu->model->title.' '.$chipEcu->generation->title.' '.$chipEcu->engine->title;
                $url = Inflector::slug($titleString, '-');
                $seoChipPage->setAttributes([
                    'chip_ecu_id' => $chipEcu->id,
                    'url' => $url,
                    'h1_ua' => 'Чіп-тюнінг '.$titleString,
                    'title_ua' => 'Чіп-тюнінг '.$titleString.' | Master Service ',
                    'description_ua' => 'Чіп-тюнінг '.$titleString.'  ➔ Київ, Харків, Львів, Дніпро ➔ Збільшення потужності двигуна '.$chipEcu->brand->title,
                    'h1_ru' => 'Чип-тюнинг '.$titleString,
                    'title_ru' => 'Чип-тюнинг '.$titleString.' | Master Service ',
                    'description_ru' => 'Чіп-тюнінг '.$titleString.'  ➔ Київ, Харків, Львів, Дніпро ➔ Увеличение мощности двигателя '.$chipEcu->brand->title,
                ]);

                $seoChipPage = self::validateUrl($seoChipPage, $url);

                if($seoChipPage->save()) {
                    $res[] = $url;

                } else {
                    $res[] = $seoChipPage->errors;
                }

            }
        }
        return $res;
    }

    private static function validateUrl(SeoChipPage $seoChipPage, string $url)
    {
        if (!$seoChipPage->validate('url')){
            $url = $url.'-';
            $seoChipPage->setAttribute('url', $url);
            self::validateUrl($seoChipPage, $url);
        }
        return $seoChipPage;
    }

}
