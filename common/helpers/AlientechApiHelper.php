<?php

namespace common\helpers;

use common\models\AlientechLog;
use common\models\AlientechOperation;
use common\models\ChipEcu;
use common\models\ProjectFileDecoded;
use common\models\ProjectFiles;
use common\models\ProjectMessages;
use common\models\Projects;
use common\models\SeoChipPage;
use common\models\User;
use Yii;
use yii\helpers\Inflector;
use yii\helpers\Url;
use yii\httpclient\Client;

class AlientechApiHelper
{

    const FileTypeOrigDec = 'orig_dec';
    const FileTypeModEnc = 'mod_enc';
    const ALIENTECH_USER_ID = 99;
    const WEBMASTER_TEST_USER_ID = 2066;

    const MAXIMUM_KESS_FILE_SLOTS = 3;
    const MAXIMUM_KTAG_FILE_SLOTS = 3;

    public static $clientApplicationGUID = 'c7f43304-624b-42b9-99f8-9d36c9f234d3';
    public static $secretKey = '?,;NTnD8XD>5X9-b25YnTg';
    public static $apiUrl = 'https://encodingapi.alientech.to';

    public static $accessToken = '';
    public static $isAuthorised = false;
    public static $userPerformed = 'CL365';


    const OperationTypes_KESSv2_Decoding = 1;
    const OperationTypes_KESSv2_Encoding = 2;
    const OperationTypes_K_TAG_Decoding = 3;
    const OperationTypes_K_TAG_Encoding = 4;

    const OperationStatus_InProgress = 0;
    const OperationStatus_Completed = 1;

    const OperationStep_decoding = 0;
    const OperationStep_decoded = 1;
    const OperationStep_file_downloaded = 2;
    const OperationStep_uploaded_mod = 3;
    const OperationStep_uploaded_mod_alien = 9;
    const OperationStep_encoding_mod_ready = 4;
    const OperationStep_encoding_mod_started = 8;
    const OperationStep_encoded = 5;
    const OperationStep_encoded_file_downloaded = 6;
    const OperationStep_closed = 7;



    const operationTypeKess = 36;
    const operationTypeKtag = 37;

    const NOT_PROCESSED = 0;
    const ERROR_KTAG_Z_LOAD = 'KTAG_Z_LOAD';
    const ERROR_KV2_INVALID_FILE = 'KV2_INVALID_FILE';
//-------------------------------------ref-2--------------------------------------------------------------
    /**
     * REV-2 Проверка на авторизацию
     * @return bool
     */
    public static function authorise()
    {
//        self::$accessToken = Yii::$app->session->get('accessToken', '');

        if (!empty(self::$accessToken)) {
            return true;
        } else {
            return self::autenticate();
        }
    }

    /**
     * REV-2 метод логирования всех обращений к апи алиентеч
     * @param null $guid
     * @param null $url
     * @param null $data
     */
    public static function logAlientechApi($guid = null, $url = null, $data = null):void
    {
        self::logToFile(' --- logAlientechApi --  ---'.$guid.$url);

        if (!is_string($data)) {
            $data = json_encode($data);
        }

        $operationLog = new AlientechLog();
        $operationLog->setAttributes([
            'guid' => $guid ?? '',
            'url' => $url ?? '',
            'resp_data' => $data ?? '',
        ]);

        $operationLog->save();

        self::logToFile(' --- $operationLog->save() --  ---'.$operationLog->id);

    }


    /**
     * REV-2 Метод авторизации
     * @return bool
     * @throws \yii\base\InvalidConfigException
     */
    public static function autenticate():bool
    {
        $client = self::getClient();

        $client->requestConfig = [
            'format' => Client::FORMAT_JSON
        ];

        $response = $client->createRequest()
            ->setMethod('POST')
            ->setUrl('/api/access-tokens/request')
            ->setData(['clientApplicationGUID' => self::$clientApplicationGUID, 'secretKey' => self::$secretKey])
            ->send();

        self::checkErrors($response);

        $resp = $response->getContent();

        self::logAlientechApi('autenticate', '/api/access-tokens/request', $resp);

        $respData = json_decode($resp);

        if (isset($respData->accessToken) && !empty($respData->accessToken)) {
            Yii::$app->session->set('accessToken', $respData->accessToken);
            self::$accessToken = $respData->accessToken;
            return true;
        }

        return false;

    }

    /**
     * REV-2 Получение клиента запроса
     * @return Client
     */
    public static function getClient():Client{
        $client = new Client([
            'baseUrl' => self::$apiUrl,
            'contentLoggingMaxSize' => 1000000,
            'responseConfig' => [
                'format' => Client::FORMAT_JSON
            ],
        ]);
        return $client;
    }

    /**
     * REV-2 Получение клиента запроса
     * @return Client
     */
    public static function logToFile($logString = '', $operation = null):void
    {
        if (!is_null($operation)) {
            error_log(date('Y-m-d H:i:s').' -- '.$operation->id.' -- '.$logString."\r\n", 3, Yii::getAlias('@storage') . '/startalientech.log');
//            echo date('Y-m-d H:i:s').' -- '.$operation->id.' -- '.$logString."\r\n";
        } else {
            error_log(date('Y-m-d H:i:s').' -- '.$logString."\r\n", 3, Yii::getAlias('@storage') . '/startalientech.log');
//            echo date('Y-m-d H:i:s').' --  -- '.$logString."\r\n";
        }
    }

    /**
     * REV-2 Метод запускает проверку всех незавершенных операций по работе с сервером Alientech
     * @return array
     */
    public static function checkAlientechOperations()
    {
        $result = [];
        $operations = AlientechOperation::find()
            ->where(['processed' => 0])
            ->andWhere(['NOT IN', 'step',  [self::OperationStep_encoded_file_downloaded, self::OperationStep_file_downloaded, self::OperationStep_closed]])
            ->all(); // todo change

        if (count($operations) == 0) {
            $slotsKess = AlientechApiKessHelper::getKessFileSlots();
            if (count($slotsKess) > 0) {
                foreach ($slotsKess as $slot) {
                    if (!$slot->isClosed) {
//                        $startedOn = new \DateTime($slot->createdOn);
//                        $dateNow = new \DateTime('now');
//                        $dateDiff = date_diff($dateNow, $startedOn);
//                        $result[] = $dateDiff;
//                        if ($dateDiff->d >= self::CLOSE_OPERATION_INTERVAL || $all == 1) {
                            $result[] = AlientechApiKessHelper::closeKessFileSlot($slot->guid);
//                        }
                    }
                }
            }
            $slotsKtag = AlientechApiKtagHelper::getFileSlots();
            if (count($slotsKtag) > 0) {
                foreach ($slotsKtag as $slot) {
                    if (!$slot->isClosed) {
//                        $startedOn = new \DateTime($slot->createdOn);
//                        $dateNow = new \DateTime('now');
//                        $dateDiff = date_diff($dateNow, $startedOn);
//                        $result[] = $dateDiff;
//                        if ($dateDiff->d >= self::CLOSE_OPERATION_INTERVAL || $all == 1) {
                        $result[] = AlientechApiKtagHelper::closeKtagFileSlot($slot->guid);
//                        }
                    }
                }
            }

            self::logToFile(' --- closeAllSlots -- $operations---');
            return $result;
        }

        self::logToFile(' --- checkAlientechOperations -- $operations---'.count($operations));

        if ($operations) {
            foreach($operations as $operation){
                self::updateOperation($operation, ['processed' => 1]);
                $operationResult = self::checkOperation($operation);
                $result[$operation->id] = $operationResult;
            }
        }
        return $result;
    }

    /**
     * REV-2 Обновляет данные об операции в бд
     * @param null $operation
     * @param null $respData
     * @return bool
     */
    public static function updateOperation(AlientechOperation $operation = null, $data = []):array
    {
        self::logToFile(' --- updateOperation --- ', $operation);

        $result = [];

        if (is_null($operation)) {
            return $result;
        }

        $operation->setAttributes($data);

        if (!$operation->save()) {
            self::logToFile(' --- updateOperation -save-error- '.json_encode($operation->errors), $operation);
            return $operation->errors;
        }

        return ['status' => 'success'];
    }

    /**
     * REV-2 Метод проверки одной операции по работе с файлами
     * Если операция завершена - обновляем запись в бд
     * @param AlientechOperation $operation
     * @return bool|mixed
     * @throws \Exception
     */
    public static function checkOperation(AlientechOperation $operation)
    {

        $result = ['success' => false];

        self::logToFile(' --- checkOperation --  step---'.$operation->step, $operation);

        if (empty($operation->result_data)) {

            self::logToFile(' --- checkOperation --  OperationStatus_InProgress--result_data-empty-'.$operation->id, $operation);

            if (!empty($operation->operation_data)) {
                $operationData = json_decode($operation->operation_data);

                if(!$operationData->isCompleted) {

                    self::logToFile(' --- checkOperation --  OperationStatus_InProgress---'.$operation->id, $operation);

                    if (self::tryCheckAlientechOperation($operation, $operationData)){
                        $result = ['tryCheckAlientechOperation' => true];
                    }
                } else {
                    if (isset($operationData->error)) {
                        if ($operationData->error->errorName) {
                            $errorName = $operationData->error->errorName;
                            if (!empty($errorName) && ($errorName == self::ERROR_KTAG_Z_LOAD || $errorName == self::ERROR_KV2_INVALID_FILE)) {
                                $project = $operation->project;
                                $shouldSaveProject = false;
                                if (($project->readmethod_id == $operation->type) && ($project->readmethod_id == self::operationTypeKess)) {
                                    $project->setAttribute('readmethod_id', self::operationTypeKtag);
                                    $shouldSaveProject = true;
                                } elseif(($project->readmethod_id == $operation->type) && ($project->readmethod_id == self::operationTypeKtag)) {
                                    $project->setAttribute('readmethod_id', self::operationTypeKess);
                                    $shouldSaveProject = true;
                                }
                                if ($shouldSaveProject){
                                    if($project->save(false)){
                                        ProjectFiles::updateAll(['alientech_operation_id' => null], 'alientech_operation_id = '.$operation->id);
                                        AlientechOperation::deleteAll(['id' => $operation->id]);
                                    }
                                }
                            }
                        }
                    }
                }
            }

        } else {

            $resultData = json_decode($operation->result_data);

            if ($operation->step == self::OperationStep_decoded){

                self::logToFile(' --- checkOperation --  OperationStep_decoded---'.$operation->id, $operation);

                if (!$resultData->isSuccessful && $resultData->hasFailed) {
                    self::logToFile(' --- checkOperation --  !$resultData->isSuccessful && $resultData->hasFailed---'.$operation->id, $operation);
                    return $result;
                }
//                if ($operation->type == self::operationTypeKess) {
//                    if (!AlientechApiKessHelper::checkCountOpenKessFileSlots()) {
//                        self::setMessageToOperationFile($operation, 'TOO_MANY_OPEN_KESSV2_FILE_SLOTS', true);
//                        $result['message'][] = 'TOO_MANY_OPEN_KESSV2_FILE_SLOTS';
//                        $result['closeKessFileSlot'] = AlientechApiKessHelper::closeKessFileSlot($resultData->slotGUID, $operation);
//                        self::updateOperation($operation, ['processed' => 0]);
//                        self::logToFile(' --- checkOperation --  TOO_MANY_OPEN_KESSV2_FILE_SLOTS---'.$operation->id, $operation);
//                        return $result;
//                    }
//                }
//                if ($operation->type == self::operationTypeKtag) {
//                    if (!AlientechApiKtagHelper::checkCountOpenKtagFileSlots()) {
//                        self::setMessageToOperationFile($operation, 'TOO_MANY_OPEN_K_TAG_FILE_SLOTS');
//                        $result['message'][] = 'TOO_MANY_OPEN_K_TAG_FILE_SLOTS';
//                        $result['closeKtagFileSlot'] = AlientechApiKtagHelper::closeKtagFileSlot($resultData->slotGUID);
//                        self::updateOperation($operation, ['processed' => 0]);
//                        self::logToFile(' --- checkOperation --  TOO_MANY_OPEN_K_TAG_FILE_SLOTS---'.$operation->id, $operation);
//                        return $result;
//                    }
//                }


                if (!self::reOpenFileSlot($operation->type, $resultData->slotGUID, $operation)) {
                    self::setMessageToOperationFile($operation, 'slot reopen false');
                    $result['message'][] = 'reopen false';
                    self::updateOperation($operation, ['processed' => 0]);
                    self::logToFile(' --- checkOperation --  slot reopen false---'.$operation->id, $operation);
                    return $result;
                }

                $result['fileData'] = self::downloadDecodedFiles($operation, $resultData);

                self::tryCloseSlot($operation);
            }

            //        модифицированный файл загружен и надо запустить его отправку на сервер
//            if ($operation->step == self::OperationStep_uploaded_mod) {
//                $result = self::uploadModFileToAlientech($operation);
//            }

            //        модифицированный файл загружен и надо запустить его кодировку на сервере
            if ($operation->step == self::OperationStep_encoding_mod_ready) {
                self::logToFile(' --- checkOperation --  OperationStep_encoding_mod_ready---'.$operation->id, $operation);
                if ($operation->completed == self::OperationStep_uploaded_mod) {
                    self::logToFile(' --- checkOperation --  OperationStep_encoding_mod_ready-completed == self::OperationStep_uploaded_mod--', $operation);
                    $result['uploadModFilesToAlientech'] = self::uploadModFilesToAlientech($operation);
                    $result['encodeModFiles'] = self::encodeModFiles($operation);
                }
                self::tryCloseSlot($operation);
            }

            //        модифицированный файл кодируется на сервере и проверяется операция
            if ($operation->step == self::OperationStep_encoding_mod_started){
                self::logToFile(' --- checkOperation --  OperationStep_encoding_mod_started---'.$operation->id, $operation);

                if (self::timeoutOperationCheck($resultData)) {
                    $respData = self::checkAlientechOperation($resultData->guid);
                    if (!$respData->isSuccessful || $respData->hasFailed) {
                        self::setMessageToOperationFile($operation, $respData);
                    } else { //&& $respData->asyncOperationType !== self::OperationTypes_KESSv2_Decoding todo может быть ошибка, надо проверять
                        if ($respData->status == self::OperationStatus_Completed && $respData->isCompleted && $respData->asyncOperationType !== self::OperationTypes_KESSv2_Decoding && $respData->asyncOperationType !== self::OperationTypes_K_TAG_Decoding) {
                            self::completeAlientechOperation($operation, $respData, self::OperationStep_encoded);
                        } else {
                            $result[] = $respData;
                        }
                    }
                }
                self::tryCloseSlot($operation);
            }

//        модифицированный файл закодирован сервером
            if ($operation->step == self::OperationStep_encoded){

                self::logToFile(' --- checkOperation --  OperationStep_encoded---'.$operation->id, $operation);

                if ($resultData->isSuccessful) {
                    $result = self::downloadEncodedFiles($operation);
                }
                self::tryCloseSlot($operation);
            }

        }


        self::updateOperation($operation, ['processed' => 0]);

        self::logToFile(' --- checkOperation-end-operation-'.$operation->id, $operation);

        return $result;

    }
    /**
     * REV-2  проверка, можно ли дергать операцию по таймауту
     * @param $operationData
     * @return bool
     * @throws \Exception
     */
    public static function tryCloseSlot(AlientechOperation $operation):bool
    {
        $resultData = json_decode($operation->result_data);
        if ($operation->type == self::operationTypeKess) {
            return AlientechApiKessHelper::closeKessFileSlot($resultData->slotGUID, $operation);
        }
        if ($operation->type == self::operationTypeKtag) {
            return AlientechApiKtagHelper::closeKtagFileSlot($resultData->slotGUID, $operation);
        }
    }

    /**
     * REV-2 Дергаем операцию
     * @param AlientechOperation|null $operation
     * @param null $operationData
     * @return bool
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\httpclient\Exception
     */
    public static function tryCheckAlientechOperation(AlientechOperation $operation = null, $operationData = null)
    {
        if (self::timeoutOperationCheck($operationData)) {

            self::logToFile(' --- checkOperation -timeoutOperationCheck-  OperationStatus_InProgress---'.$operation->id, $operation);

            $respData = self::checkAlientechOperation($operationData->guid);

            if (!$respData->isSuccessful || $respData->hasFailed) {
                self::updateOperation($operation, ['operation_data' => json_encode($respData), 'processed' => 0]);
                self::setMessageToOperationFile($operation, $respData, true);
                return false;
            } elseif ($respData->status == self::OperationStatus_Completed && $respData->isCompleted) {
                self::updateOperation($operation, ['operation_data' => json_encode($respData), 'processed' => 0]);
                self::setMessageToOperationFile($operation, 'Decoding...', true);
                self::completeAlientechOperation($operation, $respData);
                return true;
            }
            return false;
        }
        return false;
    }

    /**
     * REV-2  проверка, можно ли дергать операцию по таймауту
     * @param $operationData
     * @return bool
     * @throws \Exception
     */
    public static function timeoutOperationCheck($operationData):bool
    {
        $startedOn = new \DateTime($operationData->startedOn);
        $dateNow = new \DateTime('now');
        $dateDiff = date_diff($dateNow, $startedOn);
        if ($dateDiff->s >= $operationData->recommendedPollingInterval) {
            return true;
        }
        return false;
    }

    /**
     * REV-2 Метод проверяет операцию с запросом информации на сервере Alientech
     * @param string $guid
     * @return bool|mixed
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\httpclient\Exception
     */
    public static function checkAlientechOperation($guid = '')
    {
        $result = null;

        if (empty($guid)) {
            return $result;
        }

        if (self::authorise()) {
            $url = '/api/async-operations/'.$guid;
            $client = self::getClient();

            $response = $client->createRequest()
                ->setMethod('GET')
                ->setHeaders(['X-Alientech-ReCodAPI-LLC' => self::$accessToken])
                ->setUrl($url)
                ->send();

            $resp = $response->getContent();

            $respData = json_decode($resp);

            self::logAlientechApi($guid, $url, $resp);

            $result = $respData;
        }

        return $result;
    }

    /**
     * REV-2 Отправка уведомления в проект
     * @param AlientechOperation $operation
     * @param null $respData
     */
    public static function setMessageToOperationFile(AlientechOperation $operation = null, $respData = null, $onlySys = true)
    {
        if (!is_null($operation)) {
            self::logToFile(' --- setMessageToOperationFile--operation-'.$operation->id, $operation);

            $fileMessage = '';

            if (!empty($respData->error)) {
                $fileMessage = $respData->error->errorName;
            } else {
                $fileMessage = $respData;
            }

            $projectFile = ProjectFiles::findOne($operation->project_file_id);

            if (!empty($fileMessage)) {

                ProjectHelper::createProjectMessage($operation->project_id,
                    'Alientech Message',
                    $fileMessage,
                    $fileMessage,
                    1, 0, 0, 0, MessageHelper::TYPE_SYS, ['send_to' => 'admin']);
                self::logToFile(' --- createProjectMessage-TYPE_SYS-operation-'.$operation->id, $operation);
            }

            if (!$onlySys) {

                if (!empty($respData->error)) {
                    $projectFile->setAttribute('params', $fileMessage);
                    //                $options['send_to'] = MessageHelper::getAdministrators();
                } else {
                    $projectFile->setAttribute('params', $fileMessage);
                }

                if ($projectFile->save(false)) {
                    self::logToFile(' --- $projectFile->save(false)-operation-'.$operation->id, $operation);
                } else {
                    self::logToFile(' --- $projectFile->save(false)-error-operation-');
                }

                $project = Projects::findOne($operation->project_id);
                $project->setAttribute('status_admin', ProjectHelper::STATUS_CHANGED);

                if ($project->save(false)) {
                    self::logToFile(' --- $project->save(false)-STATUS_CHANGED-operation-'.$operation->id, $operation);
                } else {
                    self::logToFile(' --- $project->save(false)-STATUS_CHANGED-error-operation-'.json_encode($project->errors), $operation);
                }

                //            ProjectHelper::createProjectMessage($operation->project_id, 'Reload', 'Reload', 'Reload', 0, 0, 0 , 0, MessageHelper::TYPE_RELOAD, ['send_to' => 'admin']);
                self::logToFile(' --- createProjectMessage-TYPE_RELOAD-operation-'.$operation->id, $operation);

            }

        } else {
            self::logToFile(' --- setMessageToOperationFile--operation-isNull');

        }

    }

    /**
     * REV-2 Обновляет данные об операции в бд
     * @param null $operation
     * @param null $respData
     * @return bool
     */
    public static function completeAlientechOperation(AlientechOperation $operation = null, $respData = null, $step = self::OperationStep_decoded)
    {
        self::logToFile(' --- completeAlientechOperation--step-'.$step, $operation);

        $operation->setAttributes([
            'result_data' => json_encode($respData),
            'completed' => 1,
            'step' => $step,
        ]);

        if ($operation->save(false)) {
            self::logToFile(' --- $operation->save(false)-completed-operation-'.$operation->id, $operation);
        } else {
            self::logToFile(' --- $operation->save(false)-completed-error-operation-'.json_encode($operation->errors), $operation);
        }
    }


    /**
     * REV-2 переоткрываем закрытый слот
     * @param $type
     * @param $slotGuid
     * @return bool
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\httpclient\Exception
     */
    public static function reOpenFileSlot($type, $slotGuid, $operation):bool
    {
        self::logToFile(' --- reOpenFileSlot--$type-'.$type, $operation);

        if ($type == self::operationTypeKess) {
            return AlientechApiKessHelper::tryReOpenKessFileSlot($slotGuid, $operation);
        }

        if ($type == self::operationTypeKtag) {
            return AlientechApiKtagHelper::tryReOpenKtagFileSlot($slotGuid, $operation);
        }
    }

    /**
     * REV-2 Скачиваем декодированный файл
     * @param AlientechOperation $operation
     * @param $resultData
     * @return array
     * @throws \yii\base\Exception
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\httpclient\Exception
     */
    public static function downloadDecodedFiles(AlientechOperation $operation, $resultData):array
    {

        self::logToFile(' --- downloadDecodedFiles--$operation-'.$operation->id, $operation);

        $result = [];

        if ($resultData->isSuccessful) {

            $step = self::OperationStep_file_downloaded;

            $fileData = $resultData->result;

            $downloadedFileData = [];

            if ($operation->type == self::operationTypeKess) {
                $downloadedFileData = AlientechApiKessHelper::downloadKessFile($operation, $fileData, null, $fileData->decodedFileURL, $step);
            }

            if ($operation->type == self::operationTypeKtag) {
                $downloadedFileData = AlientechApiKtagHelper::downloadKtagFile($operation, $fileData, null, null, $step);
            }

            $result = $downloadedFileData;

            if($downloadedFileData['success']) {

                $fileInfo = (array)$fileData->information;

                $content = '';

                foreach ($fileInfo as $key => $value) {
                    $content .= $key . ' : ' . $value . '<br>';
                }

                $messageStart = 'Decoded file downloaded ';

                $result['downloadData'] = $downloadedFileData;

                $systemNotes = 0;

                foreach ($downloadedFileData['filesData'] as $downloadedFile) {
                    $projectFile = new ProjectFiles();
                    $projectFile->setAttributes([
                        'type' => 'external',
                        'title' => $downloadedFile['fileName'],
                        'component_name' => $downloadedFile['component'] ?? null,
                        'project_id' => $operation->project_id,
                        'file_id' => $operation->project_file_id,
                        'alientech_operation_id' => $operation->id,
                        'file_type' => ProjectHelper::FILE_TYPE_ORIGINAL_DECODED,
                        'path' => $downloadedFile['filePath'],
                        'filename' => $downloadedFile['fileName'],
                        'hash' => yii::$app->security->generateRandomString(12),
                    ]);

                    if ($projectFile->save(false)) {

                        self::logToFile(' ---downloadDecodedFiles $projectFile->save--$projectFile-'.$projectFile->id, $operation);

                        $result['projectFiles'][] = $projectFile->id;

                        self::setMessageToOperationFile($operation, 'Decoding... Success!', true);

                        self::logToFile(' ---downloadDecodedFiles $parentFile->save--$parentFile-'.$operation->project_file_id, $operation);

                        if (empty($systemNotes)) {
                            ProjectHelper::createProjectMessage($operation->project_id,
                                $messageStart,
                                $messageStart . $downloadedFile['fileName'],
                                $content,
                                1, 0, 0, 0, MessageHelper::TYPE_SYS, ['send_to' => 'admin']);

                            self::logToFile(' ---downloadDecodedFiles ProjectHelper::createProjectMessage-TYPE_SYS-project_id-'.$operation->project_id);
                        }

                        $project = Projects::findOne($operation->project_id);

                        ProjectHelper::createProjectMessage(
                            $project->id,
                            $messageStart,
                            $messageStart
//                            . $project->registration_num. ' '
                            . $project->brand->title. ' '
                            . $project->model->title. ' '
                            . $project->year. ' '
                            . $project->engine->title,
                            $projectFile->title . (!empty($projectFile->extension)? '.' . $projectFile->extension : ''),
//                            todo change for send sms and year instead generation
                            0, 0, 0 , 0, MessageHelper::TYPE_NOTE, ['send_to' => 'admin']);

                        self::logToFile(' ---downloadDecodedFiles ProjectHelper::createProjectMessage-TYPE_NOTE-project_id-'.$operation->project_id, $operation);

                        $result['ProjectMessages'][] = $messageStart . $downloadedFile['fileName'] .' downloaded';

                        $systemNotes++;

                    } else {
                        $result['errors'] = $projectFile->errors;
                        return $result;
                    }

                }


                self::updateOperation($operation, ['step' => self::OperationStep_file_downloaded]);

                self::logToFile(' ---downloadDecodedFiles $operation->save-OperationStep_file_downloaded-$operation-'.$operation->id, $operation);

                $project->setAttributes([
                    'status_admin' => ProjectHelper::STATUS_CHANGED,
                    'updated_by' => $project->created_by,
                ]);
                $project->save(false);

                self::logToFile(' ---downloadDecodedFiles $project->save--project_id-'.$project->id, $operation);

                ProjectHelper::createProjectMessage($operation->project_id, 'Reload', 'Reload', 'Reload', 0, 0, 0 , 0, MessageHelper::TYPE_RELOAD, ['alientech' => true, 'send_to' => 'admin']);

                self::logToFile(' ---downloadDecodedFiles ProjectHelper::createProjectMessage-TYPE_RELOAD-project_id-'.$operation->project_id, $operation);

            }
        } else {
            if ($resultData->hasFailed) {

                $result['message'] = $resultData->error->errorName;

                ProjectHelper::createProjectMessage(
                    $operation->project_id,
                    $operation->projectFile->title . ' DECODE ERROR',
                    $resultData->error->errorName,
                    $resultData->error->errorName,
                    1, 0, 0, 0, MessageHelper::TYPE_SYS, ['alientech' =>true, 'send_to' => 'admin']);

                self::logToFile(' ---downloadDecodedFiles $resultData->hasFailed ProjectHelper::createProjectMessage-project_id-'.$operation->project_id, $operation);

                $project = Projects::findOne($operation->project_id);

                $project->setAttributes([
                    'status_admin' => ProjectHelper::STATUS_CHANGED,
                ]);
                $project->save();

                self::logToFile(' ---downloadDecodedFiles $project->save--project_id-'.$project->id, $operation);

                self::setMessageToOperationFile($operation, 'Decoding... Error!', true);

                self::logToFile(' ---downloadDecodedFiles hasFailed-operation-'.$operation->id, $operation);

                self::logToFile(' ---downloadDecodedFiles  $resultData->hasFailed $operation->save--$operation-'.$operation->id, $operation);

                ProjectHelper::createProjectMessage($operation->project_id, 'Reload', 'Reload', 'Reload', 0, 0, 0 , 0, MessageHelper::TYPE_RELOAD, ['alientech' => true, 'send_to' => 'admin']);

                self::logToFile(' ---downloadDecodedFiles $resultData->hasFailed ProjectHelper::createProjectMessage-Reload-project_id-'.$operation->project_id, $operation);

            }
        }

        return $result;

    }

    /**
     * REV-2 Проверка на возможность кодирования загруженного ранее файла
     * @param AlientechOperation|null $operation
     * @return array
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\httpclient\Exception
     */
    public static function encodeModFiles(?AlientechOperation $operation)
    {
        self::logToFile(' --- encodeModFiles -- $operation ---'.$operation->id, $operation);

        $result = ['status' => 'error'];

        $resultData = json_decode($operation->result_data);
        if (!$resultData->isCompleted) {
            $result['data'] = self::checkAlientechOperation($resultData->guid);
            self::updateOperation($operation, ['result_data' => json_encode($result['data']), 'processed' => 0]);
            return $result;
        }

        if (!$resultData->isSuccessful && $resultData->hasFailed) {
            $result['data'] = $resultData;
            return $result;
        }

//        if ($operation->type == self::operationTypeKess) {
//            if (!AlientechApiKessHelper::checkCountOpenKessFileSlots()) {
//                self::setMessageToOperationFile($operation, 'TOO_MANY_OPEN_KESSV2_FILE_SLOTS', true);
//                $result['message'][] = 'TOO_MANY_OPEN_KESSV2_FILE_SLOTS';
//                self::logToFile(' --- checkOperation --  TOO_MANY_OPEN_KESSV2_FILE_SLOTS---'.$operation->id, $operation);
//                AlientechApiKessHelper::closeKessFileSlot($resultData->slotGUID, $operation);
//                self::updateOperation($operation, ['processed' => 0]);
//                return $result;
//            }
//        }
//        if ($operation->type == self::operationTypeKtag) {
//            if (!AlientechApiKtagHelper::checkCountOpenKtagFileSlots()) {
//                self::setMessageToOperationFile($operation, 'TOO_MANY_OPEN_K_TAG_FILE_SLOTS');
//                $result['message'][] = 'TOO_MANY_OPEN_K_TAG_FILE_SLOTS';
//                self::logToFile(' --- checkOperation --  TOO_MANY_OPEN_K_TAG_FILE_SLOTS---'.$operation->id, $operation);
//                AlientechApiKtagHelper::closeKtagFileSlot($resultData->slotGUID);
//                self::updateOperation($operation, ['processed' => 0]);
//                return $result;
//            }
//        }

        if (!self::reOpenFileSlot($operation->type, $resultData->slotGUID, $operation)) {
            $result['message'][] = 'reopen false';
            self::setMessageToOperationFile($operation, 'reopen false');
            return $result;
        }

        $result['fileData'] = self::encodeModFile($operation, $resultData);

        $result['status'] = 'success';

        return $result;

    }

    /**
     * REV-2 Кодирование загруженного ранее файла
     * @param AlientechOperation $operation
     * @param $resultData
     * @return array
     * @throws \yii\base\InvalidConfigException
     */
    private static function encodeModFile(AlientechOperation $operation, $resultData)
    {
        self::logToFile(' --- encodeModFile -- $operation ---'.$operation->id, $operation);

        $result = ['status' => 'error'];

        if ($operation->type == self::operationTypeKess) {

            $fileData = $resultData->result;

            $encodeResult = AlientechApiKessHelper::encodeModKessFile($fileData, $operation);

            $result['encodeResult'] = $encodeResult;

            $result['closedSlot'] = AlientechApiKessHelper::closeKessFileSlot($encodeResult->slotGUID, $operation);

        }

        if ($operation->type == self::operationTypeKtag) {

            $fileResult = $resultData->result;

            $encodeResult = AlientechApiKtagHelper::encodeModKtagFile($fileResult, $operation);

            $result['encodeResult'] = $encodeResult;

            $result['closedSlot'] = AlientechApiKtagHelper::closeKtagFileSlot($fileResult->ktagFileSlotGUID, $operation);

        }

        $operation->result_data = json_encode($encodeResult);
        $operation->step = self::OperationStep_encoding_mod_started;
        $operation->save(false);
        return $result;
    }

    /**
     * REV-2 Запуск скачивания кодированного ранее файла
     * @param AlientechOperation $operation
     * @return mixed
     */
    private static function downloadEncodedFiles(AlientechOperation $operation)
    {
        if ($operation->type == self::operationTypeKess) {
            return AlientechApiKessHelper::downloadEncodedFile($operation);
        }
        if ($operation->type == self::operationTypeKtag) {
            return AlientechApiKtagHelper::downloadEncodedFile($operation);
        }
    }

    /**
     * REV-2 Создание кодированного файла и добавление в проект
     * @param AlientechOperation $operation
     * @param array $downloadedFileData
     * @return ProjectFiles
     * @throws \yii\base\Exception
     */
    public static function createModEncodedFile(AlientechOperation $operation, array $downloadedFileData)
    {
        $projectFile = new ProjectFiles();
        $projectFile->setAttributes([
            'type' => 'external',
            'title' => $downloadedFileData['fileName'],
            'project_id' => $operation->project_id,
            'file_id' => $operation->project_file_id,
            'can_download' => 1,
            'alientech_operation_id' => $operation->id,
            'file_type' => ProjectHelper::FILE_TYPE_MODIFIED_ENCODED,
            'path' => $downloadedFileData['filePath'],
            'filename' => $downloadedFileData['fileName'],
            'hash' => yii::$app->security->generateRandomString(12),
        ]);
        $projectFile->save(false);

        self::logToFile(' --- createModEncodedFile--operation-'.$operation->id, $operation);

    }


    /**
     * REV-2 Загрузка мод файла в проект
     * @param ProjectFiles $projectFile
     * @return array|bool|mixed
     * @throws \yii\base\InvalidConfigException
     */
    public static function uploadModProjectFile(ProjectFiles $projectFile)
    {

        self::logToFile(' --- uploadModFile -- $projectFile---'.$projectFile->id, $projectFile->alientechOperation);
//        $result = [];
//        $result['status'] = 'error';
        self::setMessageToOperationFile($projectFile->alientechOperation, '');

        return self::updateOperation($projectFile->alientechOperation, ['step' => self::OperationStep_uploaded_mod, 'completed' => self::OperationStep_uploaded_mod, 'processed' => self::NOT_PROCESSED]);

//        if ($projectFile->alientechOperation->type == self::operationTypeKess) {
//            $result = AlientechApiKessHelper::uploadModKessFile($projectFile);
//        }
//
//        if ($projectFile->alientechOperation->type == self::operationTypeKtag) {
//            $result = AlientechApiKtagHelper::uploadModKtagComponentFile($projectFile);
//        }
//
//        if (!empty($result)) {
//
//            self::updateOperation($projectFile->alientechOperation, [
//                'step' => self::OperationStep_uploaded_mod,
//                'processed' => 0,
//            ]);
//
//            self::logToFile(' --- uploadModFile -- $projectFile->alientechOperation->save(false)---'.$projectFile->alientechOperation->id);
//
//        }

//        return $result;

    }

    public static function uploadModFile(ProjectFiles $projectFile)
    {

        if ($projectFile->alientechOperation->type == self::operationTypeKess) {
            $result = AlientechApiKessHelper::uploadModKessFile($projectFile, $projectFile->alientechOperation);
        }

        if ($projectFile->alientechOperation->type == self::operationTypeKtag) {
            $result = AlientechApiKtagHelper::uploadModKtagComponentFile($projectFile, $projectFile->alientechOperation);
        }

        if (!empty($result)) {

            self::updateOperation($projectFile->alientechOperation, [
                'step' => self::OperationStep_encoding_mod_ready,
                'processed' => 0,
                'completed' => self::OperationStep_encoding_mod_ready,
            ]);

            self::logToFile(' --- uploadModFile -- $projectFile->alientechOperation->save(false)---'.$projectFile->alientechOperation->id, $projectFile->alientechOperation);

        }

        return $result;

    }

    public static function uploadModFilesToAlientech(AlientechOperation $operation)
    {
        $result = [];

        $files = ProjectFiles::find()
            ->where([
                'alientech_operation_id' =>$operation->id,
                'file_type' => ProjectHelper::FILE_TYPE_MODIFIED_DECODED,
                'isDeleted' => 0
            ])
            ->all();
        if (count($files) > 0) {
            foreach ($files as $file) {
                $result[] = self::uploadModFile($file);
            }
        }
        return $result;
    }
    /**
     * REV-2 Загрузка мод файла на сервер
     * @param ProjectFiles $projectFile
     * @return array|bool|mixed
     * @throws \yii\base\InvalidConfigException
     */
//    public static function uploadModFileToAlientech(AlientechOperation $operation)
//    {
//        self::logToFile(' --- uploadModFileToAlientech -- $operation---'.$operation->id, $operation);
//
//        $result = [];
//        $result['status'] = 'error';
//
//        if ($operation->type == self::operationTypeKess) {
//            $result = AlientechApiKessHelper::uploadModKessFile($operation);
//        }
//
//        if ($operation->type == self::operationTypeKtag) {
//            $result = AlientechApiKtagHelper::uploadModKtagComponentFile($operation);
//        }
//
//        if (!empty($result)) {
//
//            self::updateOperation($operation, [
//                'step' => self::OperationStep_encoding_mod_ready,
//                'processed' => 0,
//                'completed' => self::OperationStep_encoding_mod_ready,
//            ]);
//
//            self::logToFile(' --- uploadModFileToAlientech -- $projectFile->alientechOperation->save(false)---'.$operation->id, $operation);
//
//        }
//
//        return $result;
//
//    }

    /**
     * REV-2 Обработка ответа от сервера
     * @param Client $client
     * @param \yii\httpclient\Response $response
     * @param AlientechOperation $operation
     * @param array $data
     * @return mixed|null
     */
    public static function checkAlientechResponse(Client $client, \yii\httpclient\Response $response, AlientechOperation $operation = null, $data = [], $needResponse = true, $onliSys = true)
    {
        self::logToFile(' --- checkAlientechResponse --', $operation);

        $resp = $response->getContent();

        self::logAlientechApi($data[0], $data[1], $resp);

        $respData = json_decode($resp);

        if($needResponse && !isset($respData->guid)) {
            self::setMessageToOperationFile($operation, $resp, $onliSys);
            return null;
        }

        return $respData;

    }

    /**
     * REV-2 метод получения всех оригинальных кодированных файлов в незакрытых проектах которые созданы с типом софта слейв
     * @return array
     */
    public static function getOrigEncFiles():array
    {
        $files =
            ProjectFiles::find()
                ->joinWith(['project' => function ($q) {
                    $q->where('projects.status !='.ProjectHelper::STATUS_CLOSED);
                }])
                ->joinWith(['originalDecodedFiles decoded' => function ($q) {
                    $q->where('decoded.id is null');
                }])
                ->with('project')
                ->notDeleted()
                ->where(['project_files.file_type' => ProjectHelper::FILE_TYPE_ORIGINAL_ENCODED])
                ->andWhere('project_files.alientech_operation_id is null')
                ->andWhere(['!=','projects.status', ProjectHelper::STATUS_CLOSED])
                ->andWhere(['!=','projects.isDeleted', 1])
                ->limit(5)
//                ->createCommand()->getRawSql();
                ->all();
        return $files ?? [];
    }


    /**
     * REV-2 Метод стартует декодирование новых оригинальных кодированных файлов
     * берем файл из новосозданного проекта если он оригинальный кодированный
     * это мы узнаем из проекта при создании по типу слейв устройства
     * @param array $files
     * @return array
     */
    public static function startDecodeFiles(array $files):array
    {
        self::logToFile(' --- startDecodeFiles -- ---');

        $result = [];
        foreach($files as $file) {
//       todo     проверить есть ли уже слот по этому проекту чтобы открыть его (в случае если клиент подгрузил еще один оригинальный файл)
//            if (($operation = AlientechOperation::find()->where(['project_id' => $file->project_id])->one()) !== null) {
//
//            }
//            $result[] = $file->project->readmethod_id == self::operationTypeKess;
            self::logToFile(' --- startDecodeFiles -- $file ---'.$file->id);

            if ($file->project->readmethod_id == self::operationTypeKess) {
                self::logToFile(' --- startDecodeFiles -- $file -operationTypeKess--'.$file->id);

                $result[] = AlientechApiKessHelper::decodeKessFile($file);
//                if (AlientechApiKessHelper::checkCountOpenKessFileSlots()) {
////                    self::setMessageToOperationFile();
////                    $file->setAttribute('params', 'TOO_MANY_OPEN_KESSV2_FILE_SLOTS');
////                    $file->save(false);
//
////                    $project = Projects::findOne($file->project_id);
////                    $project->setAttributes([
////                        'status_admin' => ProjectHelper::STATUS_CHANGED,
////                        'updated_by' => $project->created_by,
////                    ]);
////                    $project->save(false);
////                    ProjectHelper::createProjectMessage($project->id,
////                        'Alientech Message',
////                        $file->params,
////                        $file->params,
////                        1, 0, 0, 0, MessageHelper::TYPE_SYS, ['send_to' => 'admin']);
////
////                    ProjectHelper::createProjectMessage($project->id, 'Reload', 'Reload', 'Reload', 0, 0, 0 , 0, MessageHelper::TYPE_RELOAD, ['send_to' => 'admin']);
//
////                    return ['TOO_MANY_OPEN_KESSV2_FILE_SLOTS'];
//                } else {
//                    self::logToFile(' --- startDecodeFiles -- $file -TOO_MANY_OPEN_KESSV2_FILE_SLOTS--'.$file->id);
//                    return ['TOO_MANY_OPEN_KESSV2_FILE_SLOTS'];
//                }

            }

            if ($file->project->readmethod_id == self::operationTypeKtag) {
                self::logToFile(' --- startDecodeFiles -- $file -operationTypeKtag--'.$file->id);
                $result[] = AlientechApiKtagHelper::decodeKtagFile($file);
//                if (AlientechApiKtagHelper::checkCountOpenKtagFileSlots()) {
////                    $file->setAttribute('params', 'TOO_MANY_OPEN_Ktag_FILE_SLOTS');
////                    $file->save(false);
////                    $project = Projects::findOne($file->project_id);
////                    $project->setAttributes([
////                        'status_admin' => ProjectHelper::STATUS_CHANGED,
////                        'updated_by' => $project->created_by,
////                    ]);
////                    $project->save(false);
////                    $project->save(false);
////                    ProjectHelper::createProjectMessage($project->id,
////                        'Alientech Message',
////                        $file->params,
////                        $file->params,
////                        1, 0, 0, 0, MessageHelper::TYPE_SYS, ['send_to' => 'admin']);
////
////                    ProjectHelper::createProjectMessage($project->id, 'Reload', 'Reload', 'Reload', 0, 0, 0 , 0, MessageHelper::TYPE_RELOAD, ['send_to' => 'admin']);
////
////                    return ['TOO_MANY_OPEN_Ktag_FILE_SLOTS'];
//                } else {
//                    self::logToFile(' --- startDecodeFiles -- $file -operationTypeKtag-TOO_MANY_OPEN_Ktag_FILE_SLOTS-'.$file->id);
//                    return ['TOO_MANY_OPEN_Ktag_FILE_SLOTS'];
//                }

            }
        }

        return $result;
    }

    /**
     * REV-2 Метод проверки результатов запроса на сервер
     * @param $response
     * @return array
     */
    public static function checkErrors($response)
    {
        self::logToFile(' --- checkErrors -- ---');
        $resp = $response->getContent();

        if (empty($resp)) {
            return [
                'status' => 'error',
                'message' => 'empty response'
            ];
        }

        if ($resp == 'TOO_MANY_OPEN_KESSV2_FILE_SLOTS') {
            return [
                'status' => 'error',
                'message' => $resp
            ];

        }
        if ($resp == 'KESSV2_FILE_SLOT_IS_CLOSED') {
            return [
                'status' => 'error',
                'message' => $resp
            ];

        }
        return [
            'status' => 'success',
            'message' => ''
        ];

    }

    //------------------------------------/-ref-2--------------------------------------------------------------




//    public static function closeProjectData($project)
//    {
////        $operations = AlientechOperation::find()->where(['project_id' => $project->id])->all();
////        foreach($operations as $operation) {
//////            self::updateOperation($operation, ['step' => self::OperationStep_closed, 'processed' => 1]);
////        }
////        $closSlotData = self::closeFileSlotByProject($project);
//    }





//    public static function processOperation(AlientechOperation $operation = null)
//    {
//        if (!empty($operation)) {
//            $operation->setAttribute('processed', 1);
//            $operation->save(false);
//        }
//        return true;
//    }



    //---------------------------------------------------------------------------------------------------------------------//










    /**
     * метод с которого все начинается,
     * берем файл из новосозданного проекта если он оригинальный кодированный
     * это мы узнаем из проекта при создании по типу слейв устройства
     * @return array
     * @throws \yii\base\InvalidConfigException
     */
//    public static function startDecodeKess(){
//        $result = [];
//        $projectFiles = ProjectFiles::find()->where(['file_type' => 'orig_enc'])->all();
//        foreach ($projectFiles as $projectFile) {
//            $result[] =  self::decodeKessFile($projectFile);
//        }
//        return $result;
//    }

//    public static function testDecodeKess(){
//        $result = [];
//        $projectFiles = ProjectFiles::find()->where(['file_type' => 'orig_enc'])->all();
//        foreach ($projectFiles as $projectFile) {
//            $result[] =  self::decodeKessFile($projectFile);
//        }
//        return $result;
//    }

//    public static function testUploadModKessFiles(){
//        $result = [];
//        $projectFiles = ProjectFiles::find()->where(['file_type' => 'mod_dec'])->all();
//        foreach ($projectFiles as $projectFile) {
//            $uploadResult = self::uploadModKessFile($projectFile);
//            $result[] =  $uploadResult;
//        }
//        return $result;
//    }



    /**
     * KESSV2 OPERATIONS
     * Get a specific KESSv2 File
     * @param AlientechOperation|null $operation
     * @param null $fileData
     * @return array
     * @throws \yii\base\Exception
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\httpclient\Exception
     */
//    public static function downloadKessFile(
//        AlientechOperation $operation = null,
//        $fileData = null,
//        $fileSuffix = null,
//        $fileUrl = null,
//        $step = self::OperationStep_file_downloaded
//    )
//    {
//        $result = [];
//
//        $result['success'] = false;
//
//        if (is_null($operation) || is_null($fileData)) {
//            return $result;
//        }
//
//        if (self::authorise()) {
//            $fileSuffix = $fileSuffix ?? '_DECODED';
//
//            $fileName = $fileData->name.$fileSuffix;
//
//            $result['fileName'] = $fileName;
//
//            $client = self::getClient();
//
//            $response = $client->createRequest()
//                ->setMethod('GET')
//                ->setHeaders(['X-Alientech-ReCodAPI-LLC' => self::$accessToken])
//                ->setUrl($fileUrl)
//                ->send();
//
//            $resp = $response->getContent();
//
//            self::checkErrors($response);
//
//            self::logAlientechApi($operation->guid, $fileUrl, $resp);
//
//            $respData = json_decode($resp);
//
//            if($respData->data) {
//
//                $filePath = Yii::getAlias('@storage') . '/web/projects/files/' . $fileName;
//
//                $result['filePath'] = $filePath;
//
//                $data_decoded = base64_decode ($respData->data);
//
//                $savedFile = fopen ($filePath,'w');
//
//                if (fwrite ($savedFile, $data_decoded)) {
//                    fclose ($savedFile);
//                    $result['success'] = true;
//                }
//
//            }
//
//            $operation->setAttributes([
//                'processed' => 1,
//                'step' => $step,
//            ]);
//
//            $operation->save();
//
//        }
//
//        return $result;
//
//    }

    /**
     * KESSV2 FILE SLOTS AND FILES
     * Get the list of KESSv2 File Slots
     * @param null $projectFile
     * @return bool
     * @throws \yii\base\InvalidConfigException
     */
//    public static function getKessFileSlots()
//    {
//        $result = false;
//
//        if (self::authorise()) {
//            $client = self::getClient();
//
//            $response = $client->createRequest()
//                ->setMethod('GET')
//                ->setHeaders(['X-Alientech-ReCodAPI-LLC' => self::$accessToken])
//                ->setUrl('/api/kessv2/file-slots')
//                ->send();
//
//            $resp = $response->getContent();
//
//            $operationLog = new AlientechLog();
//            $operationLog->setAttributes([
//                'guid' => 'file-slots',
//                'url' => '/api/kessv2/file-slots',
//                'resp_data' => $resp,
//            ]);
//            $operationLog->save();
//
//            if ($resp) {
//                $respData = json_decode($resp);
//                $result = $respData;
////                if (is_array($respData)) {
//                    foreach($respData as $fileSlot){
//////                        var_dump($fileSlot);
////                        self::closeKessFileSlot($fileSlot->guid);
//                    }
////                }
//            }
////            die;
////            $result = $resp;
//        }
//
//        return $result;
//    }

    /**
     * K-TAG FILE SLOTS AND FILES
     * Get the list of K-TAG File Slots
     * @param null $projectFile
     * @return bool
     * @throws \yii\base\InvalidConfigException
     */
//    public static function getKtagFileSlots()
//    {
//        $result = false;
//
//        if (self::authorise()) {
//            $client = self::getClient();
//
//            $response = $client->createRequest()
//                ->setMethod('GET')
//                ->setHeaders(['X-Alientech-ReCodAPI-LLC' => self::$accessToken])
//                ->setUrl('/api/ktag/file-slots')
//                ->send();
//
//            $resp = $response->getContent();
//
//            self::logAlientechApi('file-slots', '/api/ktag/file-slots', $resp);
//
//            if (!empty($resp)) {
//                $result =  json_decode($resp);
//            } else {
//                $result =  $response;
//            }
//
//        }
//
//        return $result;
//    }

    /**
     * KESSV2 FILE SLOTS AND FILES
     * Get the list of KESSv2 File Slots
     * @param null $projectFile
     * @return bool
     * @throws \yii\base\InvalidConfigException
     */
//    public static function getKessFileSlot($kessv2FileSlotGUID = '')
//    {
//        $result = null;
//
//        if (self::authorise()) {
//            $client = self::getClient();
//
//            $response = $client->createRequest()
//                ->setMethod('GET')
//                ->setHeaders(['X-Alientech-ReCodAPI-LLC' => self::$accessToken])
//                ->setUrl('/api/kessv2/file-slots/'.$kessv2FileSlotGUID)
//                ->send();
//
//            $resp = $response->getContent();
//
//            self::logAlientechApi('file-slots', '/api/kessv2/file-slots', $resp);
//
//            if (!empty($resp)) {
//                $result =  json_decode($resp);
//            } else {
//                $result =  $response;
//            }
//        }
//
//        return $result;
//    }

    /**
     * K-TAG FILE SLOTS AND FILES
     * Get a K-TAG File Slot
     * @param null $projectFile
     * @return bool
     * @throws \yii\base\InvalidConfigException
     */
//    public static function getKtagFileSlot($ktagFileSlotGUID = '')
//    {
//        $result = false;
//
//        if (self::authorise()) {
//            $client = self::getClient();
//
//            $response = $client->createRequest()
//                ->setMethod('GET')
//                ->setHeaders(['X-Alientech-ReCodAPI-LLC' => self::$accessToken])
//                ->setUrl('/api/ktag/file-slots/'.$ktagFileSlotGUID)
//                ->send();
//
//            $resp = $response->getContent();
//
//            self::logAlientechApi('file-slots', '/api/ktag/file-slots', $resp);
//
//            if (!empty($resp)) {
//                $result =  json_decode($resp);
//            } else {
//                $result =  $response;
//            }
//        }
//
//        return $result;
//    }

    /**
     * KESSV2 FILE SLOTS AND FILES
     * Get a KESSv2 File from a KESSv2 File Slot
     * @param null $projectFile
     * @return bool
     * @throws \yii\base\InvalidConfigException
     */
//    public static function getKessFileBySlot($kessv2FileSlotGUID = '', $fileType = 'Read')
//    {
//        $result = false;
//
//        if (self::authorise()) {
//            $client = self::getClient();
//
//            if (empty($kessv2FileSlotGUID)) {
//                return $result;
//            }
//
//            $options = ['fileType' => $fileType];
//
//            $response = $client->createRequest()
//                ->setMethod('GET')
//                ->setHeaders(['X-Alientech-ReCodAPI-LLC' => self::$accessToken])
//                ->setUrl('/api/kessv2/file-slots/'.$kessv2FileSlotGUID.'/files/?'.http_build_query($options))
//                ->send();
//
//            $resp = $response->getContent();
//
//            self::logAlientechApi('file-slots', '/api/kessv2/file-slots', $resp);
//
//            if (!empty($resp)) {
//                $result =  json_decode($resp);
//            } else {
//                $result =  $response;
//            }
//        }
//
//        return $result;
//    }

    /**
     * K-TAG FILE SLOTS AND FILES
     * Get a K-TAG File from a K-TAG File Slot
     * @param null $projectFile
     * @return bool
     * @throws \yii\base\InvalidConfigException
     */
//    public static function getKtagFileBySlot($ktagFileSlotGUID = '', $fileType = 'ReadBackup')
//    {
//        $result = false;
//
//        if (self::authorise()) {
//            $client = self::getClient();
//
//            if (empty($ktagFileSlotGUID)) {
//                return $result;
//            }
//
//            $options = ['fileType' => $fileType];
//
//            $response = $client->createRequest()
//                ->setMethod('GET')
//                ->setHeaders(['X-Alientech-ReCodAPI-LLC' => self::$accessToken])
//                ->setUrl('/api/ktag/file-slots/'.$ktagFileSlotGUID.'/files/?'.http_build_query($options))
//                ->send();
//
//            $resp = $response->getContent();
//
//            self::logAlientechApi('file-slots', '/api/ktag/file-slots', $resp);
//
//            if (!empty($resp)) {
//                $result =  json_decode($resp);
//            } else {
//                $result =  $response;
//            }
//        }
//
//        return $result;
//    }

    /**
     * KESSV2 FILE SLOTS AND FILES
     * Get a specific KESSV2 File
     * @param $file
     * @return bool|mixed|\yii\httpclient\Response
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\httpclient\Exception
     */
//    public static function getKessFile($file)
//    {
//        $result = false;
//
//        if (empty($file)) {
//            return $result;
//        }
//
//        if (self::authorise()) {
//            $client = self::getClient();
//            $response = $client->createRequest()
//                ->setMethod('GET')
//                ->setHeaders(['X-Alientech-ReCodAPI-LLC' => self::$accessToken])
//                ->setUrl('/api/kessv2/file-slots/'.$file->slotGUID.'/files/'.$file->guid)
//                ->send();
//
//            $resp = $response->getContent();
//
//            self::logAlientechApi('file', '/api/kessv2/file-slots/'.$file->slotGUID.'/'.$file->guid, $resp);
//
//            if (!empty($resp)) {
//                $result =  json_decode($resp);
//            } else {
//                $result =  $response;
//            }
//        }
//        return $result;
//    }

    /**
     * K-TAG FILE SLOTS AND FILES
     * Get a specific K-TAG File
     * @param $file
     * @return bool|mixed|\yii\httpclient\Response
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\httpclient\Exception
     */
//    public static function getKtagFile($file)
//    {
//        $result = false;
//
//        if (empty($file)) {
//            return $result;
//        }
//
//        if (self::authorise()) {
//            $client = self::getClient();
//
//            $response = $client->createRequest()
//                ->setMethod('GET')
//                ->setHeaders(['X-Alientech-ReCodAPI-LLC' => self::$accessToken])
//                ->setUrl('/api/ktag/file-slots/'.$file->slotGUID.'/files/'.$file->guid)
//                ->send();
//
//            $resp = $response->getContent();
//
//            self::logAlientechApi('file', '/api/ktag/file-slots/'.$file->slotGUID.'/'.$file->guid, $resp);
//
//            if (!empty($resp)) {
//                $result =  json_decode($resp);
//            } else {
//                $result =  $response;
//            }
//        }
//        return $result;
//    }

    /**
     * KESSV2 FILE SLOTS AND FILES
     * Get the list of KESSv2 File Slots
     * @param null $projectFile
     * @return bool
     * @throws \yii\base\InvalidConfigException
     */
//    public static function searchKessFilesBySlot($kessv2FileSlotGUID = '', $fileType = '')
//    {
//        $result = false;
//
//        if (self::authorise()) {
//            $client = self::getClient();
//
//            if (empty($kessv2FileSlotGUID)) {
//                return $result;
//            }
//
//            $options = ['i_kessv2FileSlotGUID' => $kessv2FileSlotGUID];
//
//            if (!empty($fileType)) {
//                $options = ['i_fileType' => $fileType];
//            }
//
//            $response = $client->createRequest()
//                ->setMethod('GET')
//                ->setHeaders(['X-Alientech-ReCodAPI-LLC' => self::$accessToken])
//                ->setUrl('/api/kessv2/files/?'.http_build_query($options))
//                ->send();
//
//            $resp = $response->getContent();
//
//            self::logAlientechApi('file-slots', '/api/kessv2/file-slots', $resp);
//
//            if (!empty($resp)) {
//                $result =  json_decode($resp);
//            } else {
//                $result =  $response;
//            }
//        }
//
//        return $result;
//    }

    /**
     * K-TAG FILE SLOTS AND FILES
     * Search for K-TAG Files
     * @param null $projectFile
     * @return bool
     * @throws \yii\base\InvalidConfigException
     */
//    public static function searchKtagFilesBySlot($ktagFileSlotGUID = '', $fileType = '')
//    {
//        $result = false;
//
//        if (self::authorise()) {
//            $client = self::getClient();
//
//            if (empty($ktagFileSlotGUID)) {
//                return $result;
//            }
//
//            $options = ['i_ktagFileSlotGUID' => $ktagFileSlotGUID];
//
//            if (!empty($fileType)) {
//                $options = ['i_fileType' => $fileType];
//            }
//
//            $response = $client->createRequest()
//                ->setMethod('GET')
//                ->setHeaders(['X-Alientech-ReCodAPI-LLC' => self::$accessToken])
//                ->setUrl('/api/ktag/files/?'.http_build_query($options))
//                ->send();
//
//            $resp = $response->getContent();
//
//            self::logAlientechApi('file-slots', '/api/ktag/files/?'.http_build_query($options), $resp);
//
//            if (!empty($resp)) {
//                $result =  json_decode($resp);
//            } else {
//                $result =  $response;
//            }
//        }
//
//        return $result;
//    }

    /**
     * закрываем слот
     * @param Projects $project
     * @return bool
     * @throws \yii\base\InvalidConfigException
     */
//    public static function closeFileSlotByProject(Projects $project)
//    {
//        $operation = AlientechOperation::find()->where(['project_id' => $project->id])->one();
//        if ($operation !== null) {
//            switch ($operation->type){
//                case self::operationTypeKess:
//                    $operationData = json_decode($operation->operation_data);
//
//                    $operation->setAttribute('step', AlientechApiHelper::OperationStep_closed);
//                    $operation->save(false);
//
//                    if (isset($operationData->slotGUID) && !empty($operationData->slotGUID)) {
//                        return self:: closeKessFileSlot($operationData->slotGUID);
//                    }
//                    break;
//                case self::operationTypeKtag:
//                    $operationData = json_decode($operation->operation_data);
//
//                    $operation->setAttribute('step', AlientechApiHelper::OperationStep_closed);
//                    $operation->save(false);
//
//                    if (isset($operationData->slotGUID) && !empty($operationData->slotGUID)) {
//                        return self:: closeKtagFileSlot($operationData->slotGUID);
//                    }
//                    break;
//            }
//        }
//    }

    /**
     * KESSV2 FILE SLOTS AND FILES
     * Close an open KESSV2 File Slot
     * @param null $projectFile
     * @return bool
     * @throws \yii\base\InvalidConfigException
     */
//    public static function closeKessFileSlot($kessv2FileSlotGUID = '')
//    {
//        $result = false;
//
//        if (self::authorise()) {
//            $client = self::getClient();
//
//            if (empty($kessv2FileSlotGUID)) {
//                return $result;
//            }
//
//            $response = $client->createRequest()
//                ->setMethod('POST')
//                ->setHeaders(['X-Alientech-ReCodAPI-LLC' => self::$accessToken])
//                ->setUrl('/api/kessv2/file-slots/'.$kessv2FileSlotGUID.'/close')
//                ->send();
//
//            $resp = $response->getContent();
//
//            self::logAlientechApi('close-file-slots', '/api/kessv2/file-slots/'.$kessv2FileSlotGUID.'/close', $resp);
//
//            $result = $resp;
//        }
//
//        return $result;
//    }

    /**
     * K-TAG FILE SLOTS AND FILES
     * Close an open K-TAG File Slot
     * Get the list of KESSv2 File Slots
     * @param null $projectFile
     * @return bool
     * @throws \yii\base\InvalidConfigException
     */
//    public static function closeKtagFileSlot($ktagFileSlotGUID = '')
//    {
//        $result = false;
//
//        if (self::authorise()) {
//            $client = self::getClient();
//
//            if (empty($ktagFileSlotGUID)) {
//                return $result;
//            }
//
//            $response = $client->createRequest()
//                ->setMethod('POST')
//                ->setHeaders(['X-Alientech-ReCodAPI-LLC' => self::$accessToken])
//                ->setUrl('/api/kessv2/file-slots/'.$ktagFileSlotGUID.'/close')
//                ->send();
//
//            $resp = $response->getContent();
//
//            self::logAlientechApi('close-file-slots', '/api/kessv2/file-slots/'.$ktagFileSlotGUID.'/close', $resp);
//
//            $result = $resp;
//        }
//
//        return $result;
//    }


    /**
     * Re-open a closed KESSv2 File Slot
     * @param string $fileSlotGUID
     * @return bool
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\httpclient\Exception
     */
//    public static function tryReOpenKessFileSlot($fileSlotGUID = ''):bool
//    {
//        $result = null;
//
//        $slotInfo = self::getKessFileSlot($fileSlotGUID);
//
//        if (!$slotInfo->isClosed) {
//            $result = true;
//            return $result;
//        }
//
//        if (self::authorise()) {
//            $client = self::getClient();
//
//            if (empty($fileSlotGUID)) {
//                return $result;
//            }
//
//            $response = $client->createRequest()
//                ->setMethod('POST')
//                ->setHeaders(['X-Alientech-ReCodAPI-LLC' => self::$accessToken])
//                ->setUrl('/api/kessv2/file-slots/'.$fileSlotGUID.'/reopen')
//                ->send();
//
//            $resp = $response->getContent();
//
//            self::logAlientechApi('reopen-file-slots', '/api/kessv2/file-slots/'.$fileSlotGUID.'/reopen', $resp);
//
//            $result = true;
//        }
//
//        return $result;
//
//    }

    /**
     * Re-open a closed K-TAG File Slot
     * @param string $fileSlotGUID
     * @return bool
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\httpclient\Exception
     */
//    public static function tryReOpenKtagFileSlot($fileSlotGUID = ''):bool
//    {
//        $result = null;
//
//        $slotInfo = self::getKessFileSlot($fileSlotGUID);
//
//        if (!$slotInfo->isClosed) {
//            $result = true;
//            return $result;
//        }
//
//        if (self::authorise()) {
//            $client = self::getClient();
//
//            if (empty($fileSlotGUID)) {
//                return $result;
//            }
//
//            $response = $client->createRequest()
//                ->setMethod('POST')
//                ->setHeaders(['X-Alientech-ReCodAPI-LLC' => self::$accessToken])
//                ->setUrl('/api/ktag/file-slots/'.$fileSlotGUID.'/reopen')
//                ->send();
//
//            $resp = $response->getContent();
//
//            self::logAlientechApi('reopen-file-slots', '/api/kessv2/file-slots/'.$fileSlotGUID.'/reopen', $resp);
//
//            $result = true;
//        }
//
//        return $result;
//
//    }





//---------------------------------------------------------------------------------------------------










//    public static function testDownloadKessFiles(string $string)
//    {
//
//
////        $slot = self::getKessFileSlot($string);
//
////        $file = self::getKessFileBySlot($string, 'Read');
//
////        $files = self::searchKessFilesBySlot($string);
//
////        var_dump($slot);
////        var_dump($file);
////0af83fc5-150e-4d09-b2c2-b7cce0b5a8a6
////0af83fc5-150e-4d09-b2c2-b7cce0b5a8a6
//        if (!empty($files) && is_array($files)) {
//            foreach ($files as $file) {
//                $fileData = self::getKessFile($file);
//
////                $filePath = Yii::getAlias('@storage') . '/web/projects/files/' . $fileData->name.'__'.$fileData->fileType;
//////Decode pdf content
////                $data_decoded = base64_decode ($fileData->data);
//////Write data back to pdf file
////                $savedFile = fopen ($filePath,'w');
////                fwrite ($savedFile,$data_decoded);
//////close output file
////                fclose ($savedFile);
////                echo 'Done';
//                var_dump($fileData);
//            }
//        }
//        die;
//    }




//    public static function checkCountOpenKessFileSlots():bool
//    {
//        $slots = AlientechApiKessHelper::getKessFileSlots();
//
//        $result = true;
//
//        $openedSlots = 0;
//
//        if (count($slots) >0) {
//            foreach ($slots as $slot) {
//                if (!$slot->isClosed) {
//                    $openedSlots++;
//                }
////                self::closeKessFileSlot($slot->guid);
//            }
//            if ($openedSlots >= self::MAXIMUM_KESS_FILE_SLOTS) {
//                $result = false;
//            }
//        }
//        return $result;
//    }


//    public static function reOpenKessFileSlot($kessv2FileSlotGUID = ''):bool
//    {
//        $result = false;
//
//        if (self::authorise()) {
//            $client = self::getClient();
//
//            if (empty($kessv2FileSlotGUID)) {
//                return $result;
//            }
//
//            $response = $client->createRequest()
//                ->setMethod('POST')
//                ->setHeaders(['X-Alientech-ReCodAPI-LLC' => self::$accessToken])
//                ->setUrl('/api/kessv2/file-slots/'.$kessv2FileSlotGUID.'/reopen')
//                ->send();
//
//            $resp = $response->getContent();
//
//            self::logAlientechApi('reopen-file-slots', '/api/kessv2/file-slots/'.$kessv2FileSlotGUID.'/reopen', $resp);
//
//            $result = true;
//        }
//
//        return $result;
//
//    }




}
