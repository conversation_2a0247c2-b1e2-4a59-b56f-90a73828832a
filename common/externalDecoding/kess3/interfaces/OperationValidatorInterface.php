<?php

namespace common\externalDecoding\kess3\interfaces;

use common\externalDecoding\kess3\dto\ValidationResult;
use common\models\ProjectFiles;

/**
 * Интерфейс для валидации операций
 * 
 * Обеспечивает проверку данных перед выполнением операций
 */
interface OperationValidatorInterface
{
    /**
     * Валидирует файл для операции
     * 
     * @param ProjectFiles $file Файл для валидации
     * @return ValidationResult Результат валидации
     */
    public function validateFile(ProjectFiles $file): ValidationResult;

    /**
     * Валидирует команду операции
     * 
     * @param OperationCommandInterface $command Команда для валидации
     * @return ValidationResult Результат валидации
     */
    public function validateCommand(OperationCommandInterface $command): ValidationResult;

    /**
     * Валидирует проект для операции
     * 
     * @param int $projectId Идентификатор проекта
     * @return ValidationResult Результат валидации
     */
    public function validateProject(int $projectId): ValidationResult;

    /**
     * Валидирует права пользователя на операцию
     * 
     * @param int $userId Идентификатор пользователя
     * @param int $operationType Тип операции
     * @param array $context Контекст операции
     * @return ValidationResult Результат валидации
     */
    public function validateUserPermissions(int $userId, int $operationType, array $context = []): ValidationResult;

    /**
     * Валидирует системные ресурсы для операции
     * 
     * @param int $operationType Тип операции
     * @return ValidationResult Результат валидации
     */
    public function validateSystemResources(int $operationType): ValidationResult;

    /**
     * Валидирует лимиты операций для пользователя
     * 
     * @param int $userId Идентификатор пользователя
     * @param int $operationType Тип операции
     * @return ValidationResult Результат валидации
     */
    public function validateOperationLimits(int $userId, int $operationType): ValidationResult;
}
