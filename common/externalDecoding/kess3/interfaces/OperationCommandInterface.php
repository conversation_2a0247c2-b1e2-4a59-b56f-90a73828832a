<?php

namespace common\externalDecoding\kess3\interfaces;

use common\chip\alientech\entities\dto\AsyncOperationDto;
use common\externalDecoding\kess3\dto\OperationResult;

/**
 * Интерфейс для команд операций (паттерн Command)
 * 
 * Унифицирует выполнение всех типов операций KESS3:
 * - Декодинг файлов
 * - Кодинг OBD
 * - Кодинг BOOT
 */
interface OperationCommandInterface
{
    /**
     * Выполняет операцию
     * 
     * @return OperationResult Результат выполнения операции
     * @throws \Exception При ошибке выполнения
     */
    public function execute(): OperationResult;

    /**
     * Проверяет, может ли команда быть выполнена
     * 
     * @return bool Возможность выполнения
     */
    public function canExecute(): bool;

    /**
     * Возвращает тип операции
     * 
     * @return int Тип операции (константы из AsyncOperationDto)
     */
    public function getOperationType(): int;

    /**
     * Возвращает контекст операции
     * 
     * @return array Контекст с данными для выполнения
     */
    public function getContext(): array;

    /**
     * Возвращает приоритет операции
     * 
     * @return int Приоритет (1 - высокий, 10 - низкий)
     */
    public function getPriority(): int;

    /**
     * Возвращает максимальное время выполнения в секундах
     * 
     * @return int Таймаут операции
     */
    public function getTimeout(): int;

    /**
     * Проверяет, поддерживает ли команда повторные попытки
     * 
     * @return bool Поддержка retry
     */
    public function isRetryable(): bool;

    /**
     * Возвращает максимальное количество попыток
     * 
     * @return int Количество попыток
     */
    public function getMaxRetries(): int;
}
