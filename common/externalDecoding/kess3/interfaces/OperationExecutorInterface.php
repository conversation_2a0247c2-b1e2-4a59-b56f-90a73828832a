<?php

namespace common\externalDecoding\kess3\interfaces;

use common\externalDecoding\kess3\dto\OperationResult;

/**
 * Интерфейс для выполнения операций
 * 
 * Отвечает за координацию выполнения команд операций
 */
interface OperationExecutorInterface
{
    /**
     * Выполняет команду операции
     * 
     * @param OperationCommandInterface $command Команда для выполнения
     * @return OperationResult Результат выполнения
     * @throws \Exception При ошибке выполнения
     */
    public function execute(OperationCommandInterface $command): OperationResult;

    /**
     * Выполняет команду асинхронно через очередь
     * 
     * @param OperationCommandInterface $command Команда для выполнения
     * @return string Идентификатор задачи в очереди
     */
    public function executeAsync(OperationCommandInterface $command): string;

    /**
     * Отменяет выполнение операции
     * 
     * @param string $operationId Идентификатор операции
     * @return bool Успешность отмены
     */
    public function cancel(string $operationId): bool;

    /**
     * Получает статус выполнения операции
     * 
     * @param string $operationId Идентификатор операции
     * @return OperationResult|null Результат операции или null
     */
    public function getStatus(string $operationId): ?OperationResult;

    /**
     * Проверяет, может ли команда быть выполнена
     * 
     * @param OperationCommandInterface $command Команда для проверки
     * @return bool Возможность выполнения
     */
    public function canExecute(OperationCommandInterface $command): bool;
}
