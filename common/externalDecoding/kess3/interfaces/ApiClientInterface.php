<?php

namespace common\externalDecoding\kess3\interfaces;

use common\chip\alientech\entities\dto\AsyncOperationDto;
use common\chip\alientech\entities\dto\Kess3FileDto;

/**
 * Интерфейс для работы с API Alientech
 * 
 * Инкапсулирует всю логику взаимодействия с внешним API
 */
interface ApiClientInterface
{
    /**
     * Отправляет файл на декодинг
     * 
     * @param string $filePath Путь к файлу
     * @param array $userInfo Информация пользователя
     * @param string $callbackUrl URL для callback
     * @return AsyncOperationDto Операция декодинга
     * @throws \Exception При ошибке API
     */
    public function startDecoding(string $filePath, array $userInfo, string $callbackUrl): AsyncOperationDto;

    /**
     * Отправляет запрос на кодинг
     * 
     * @param array $encodeData Данные для кодинга
     * @param string $callbackUrl URL для callback
     * @return AsyncOperationDto Операция кодинга
     * @throws \Exception При ошибке API
     */
    public function startEncoding(array $encodeData, string $callbackUrl): AsyncOperationDto;

    /**
     * Загружает модифицированный файл
     * 
     * @param string $filePath Путь к файлу
     * @param string $slotGuid GUID слота
     * @return array Результат загрузки
     * @throws \Exception При ошибке API
     */
    public function uploadModifiedFile(string $filePath, string $slotGuid): array;

    /**
     * Скачивает файл по URL
     * 
     * @param string $url URL файла
     * @return Kess3FileDto Скачанный файл
     * @throws \Exception При ошибке загрузки
     */
    public function downloadFile(string $url): Kess3FileDto;

    /**
     * Получает статус операции
     * 
     * @param string $operationGuid GUID операции
     * @return AsyncOperationDto Обновленная операция
     * @throws \Exception При ошибке API
     */
    public function getOperationStatus(string $operationGuid): AsyncOperationDto;

    /**
     * Закрывает слот файла
     * 
     * @param string $slotGuid GUID слота
     * @return bool Успешность закрытия
     */
    public function closeFileSlot(string $slotGuid): bool;

    /**
     * Закрывает все открытые слоты
     * 
     * @return bool Успешность закрытия
     */
    public function closeAllFileSlots(): bool;

    /**
     * Проверяет доступность API
     * 
     * @return bool Доступность API
     */
    public function isApiAvailable(): bool;

    /**
     * Получает информацию о лимитах API
     * 
     * @return array Информация о лимитах
     */
    public function getApiLimits(): array;
}
