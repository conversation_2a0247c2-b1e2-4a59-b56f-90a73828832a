<?php

namespace common\externalDecoding\kess3\interfaces;

use common\externalDecoding\kess3\dto\ErrorHandlingResult;
use common\externalDecoding\kess3\exceptions\OperationException;

/**
 * Интерфейс для обработки ошибок операций
 * 
 * Централизованная обработка ошибок с поддержкой retry логики
 */
interface ErrorHandlerInterface
{
    /**
     * Обрабатывает ошибку операции
     * 
     * @param OperationException $exception Исключение операции
     * @return ErrorHandlingResult Результат обработки
     */
    public function handleError(OperationException $exception): ErrorHandlingResult;

    /**
     * Определяет, нужно ли повторить операцию
     * 
     * @param OperationException $exception Исключение операции
     * @return bool Необходимость повтора
     */
    public function shouldRetry(OperationException $exception): bool;

    /**
     * Возвращает задержку перед повтором
     * 
     * @param int $attemptNumber Номер попытки
     * @param OperationException $exception Исключение операции
     * @return int Задержка в секундах
     */
    public function getRetryDelay(int $attemptNumber, OperationException $exception): int;

    /**
     * Возвращает максимальное количество попыток
     * 
     * @param OperationException $exception Исключение операции
     * @return int Максимальное количество попыток
     */
    public function getMaxRetries(OperationException $exception): int;

    /**
     * Классифицирует ошибку по серьезности
     * 
     * @param OperationException $exception Исключение операции
     * @return string Уровень серьезности
     */
    public function classifyError(OperationException $exception): string;

    /**
     * Логирует ошибку
     * 
     * @param OperationException $exception Исключение операции
     * @param array $context Дополнительный контекст
     * @return void
     */
    public function logError(OperationException $exception, array $context = []): void;

    /**
     * Отправляет уведомление об ошибке
     * 
     * @param OperationException $exception Исключение операции
     * @param array $context Дополнительный контекст
     * @return void
     */
    public function notifyError(OperationException $exception, array $context = []): void;
}
