<?php

namespace common\externalDecoding\kess3\exceptions;

use common\externalDecoding\kess3\enums\ErrorSeverity;
use common\externalDecoding\kess3\enums\OperationType;

/**
 * Исключение для ошибок операций KESS3
 * 
 * Содержит дополнительную информацию о контексте операции
 */
class OperationException extends \Exception
{
    public function __construct(
        string $message,
        private ?string $operationGuid = null,
        private ?OperationType $operationType = null,
        private ?ErrorSeverity $severity = null,
        private array $context = [],
        int $code = 0,
        ?\Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        
        // Автоматически определяем серьезность если не указана
        if ($this->severity === null) {
            $this->severity = ErrorSeverity::fromException($this);
        }
    }

    public function getOperationGuid(): ?string
    {
        return $this->operationGuid;
    }

    public function getOperationType(): ?OperationType
    {
        return $this->operationType;
    }

    public function getSeverity(): ErrorSeverity
    {
        return $this->severity ?? ErrorSeverity::HIGH;
    }

    public function getContext(): array
    {
        return $this->context;
    }

    public function getContextValue(string $key, mixed $default = null): mixed
    {
        return $this->context[$key] ?? $default;
    }

    public function setContext(array $context): self
    {
        $this->context = $context;
        return $this;
    }

    public function addContext(string $key, mixed $value): self
    {
        $this->context[$key] = $value;
        return $this;
    }

    /**
     * Проверяет, можно ли повторить операцию после этой ошибки
     */
    public function isRetryable(): bool
    {
        return $this->getSeverity()->shouldRetry();
    }

    /**
     * Возвращает максимальное количество попыток
     */
    public function getMaxRetries(): int
    {
        return $this->getSeverity()->getMaxRetries();
    }

    /**
     * Возвращает задержку перед повтором
     */
    public function getRetryDelay(int $attemptNumber): int
    {
        return $this->getSeverity()->getRetryDelay($attemptNumber);
    }

    /**
     * Проверяет, требует ли ошибка немедленного уведомления
     */
    public function requiresImmediateNotification(): bool
    {
        return $this->getSeverity()->requiresImmediateNotification();
    }

    /**
     * Создает исключение для ошибки аутентификации
     */
    public static function authenticationFailed(
        string $message = 'Authentication failed',
        ?string $operationGuid = null,
        array $context = []
    ): self {
        return new self(
            message: $message,
            operationGuid: $operationGuid,
            severity: ErrorSeverity::CRITICAL,
            context: $context
        );
    }

    /**
     * Создает исключение для ошибки API
     */
    public static function apiError(
        string $message,
        ?string $operationGuid = null,
        ?OperationType $operationType = null,
        array $context = []
    ): self {
        return new self(
            message: $message,
            operationGuid: $operationGuid,
            operationType: $operationType,
            severity: ErrorSeverity::HIGH,
            context: $context
        );
    }

    /**
     * Создает исключение для ошибки валидации
     */
    public static function validationFailed(
        string $message,
        ?string $operationGuid = null,
        ?OperationType $operationType = null,
        array $context = []
    ): self {
        return new self(
            message: $message,
            operationGuid: $operationGuid,
            operationType: $operationType,
            severity: ErrorSeverity::MEDIUM,
            context: $context
        );
    }

    /**
     * Создает исключение для таймаута операции
     */
    public static function timeout(
        string $operationGuid,
        OperationType $operationType,
        int $timeoutSeconds,
        array $context = []
    ): self {
        return new self(
            message: "Operation timeout after {$timeoutSeconds} seconds",
            operationGuid: $operationGuid,
            operationType: $operationType,
            severity: ErrorSeverity::HIGH,
            context: array_merge($context, ['timeout_seconds' => $timeoutSeconds])
        );
    }

    /**
     * Создает исключение для превышения лимитов
     */
    public static function limitExceeded(
        string $message,
        ?string $operationGuid = null,
        ?OperationType $operationType = null,
        array $context = []
    ): self {
        return new self(
            message: $message,
            operationGuid: $operationGuid,
            operationType: $operationType,
            severity: ErrorSeverity::HIGH,
            context: $context
        );
    }

    /**
     * Создает исключение для ошибки файла
     */
    public static function fileError(
        string $message,
        ?string $operationGuid = null,
        ?OperationType $operationType = null,
        array $context = []
    ): self {
        return new self(
            message: $message,
            operationGuid: $operationGuid,
            operationType: $operationType,
            severity: ErrorSeverity::MEDIUM,
            context: $context
        );
    }

    /**
     * Преобразует в массив для логирования
     */
    public function toArray(): array
    {
        return [
            'message' => $this->getMessage(),
            'code' => $this->getCode(),
            'operationGuid' => $this->operationGuid,
            'operationType' => $this->operationType?->value,
            'severity' => $this->getSeverity()->value,
            'context' => $this->context,
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'trace' => $this->getTraceAsString(),
        ];
    }
}
