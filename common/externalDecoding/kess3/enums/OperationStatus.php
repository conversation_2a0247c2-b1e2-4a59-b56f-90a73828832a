<?php

namespace common\externalDecoding\kess3\enums;

/**
 * Перечисление статусов операций KESS3
 *
 * Соответствует константам из AsyncOperationDto
 */
enum OperationStatus: int
{
    case IN_PROGRESS = 0;
    case COMPLETED = 1;
    case FAILED = 2;
    case CANCELLED = 3;
    case TIMEOUT = 4;

    /**
     * Возвращает человекочитаемое название статуса
     */
    public function getDisplayName(): string
    {
        return match($this) {
            self::IN_PROGRESS => 'В процессе',
            self::COMPLETED => 'Завершена',
            self::FAILED => 'Ошибка',
            self::CANCELLED => 'Отменена',
            self::TIMEOUT => 'Таймаут',
        };
    }

    /**
     * Возвращает описание статуса
     */
    public function getDescription(): string
    {
        return match($this) {
            self::IN_PROGRESS => 'Операция выполняется',
            self::COMPLETED => 'Операция успешно завершена',
            self::FAILED => 'Операция завершена с ошибкой',
            self::CANCELLED => 'Операция была отменена пользователем',
            self::TIMEOUT => 'Операция превысила максимальное время выполнения',
        };
    }

    /**
     * Проверяет, является ли статус финальным
     */
    public function isFinal(): bool
    {
        return match($this) {
            self::IN_PROGRESS => false,
            self::COMPLETED => true,
            self::FAILED => true,
            self::CANCELLED => true,
            self::TIMEOUT => true,
        };
    }

    /**
     * Проверяет, является ли статус успешным
     */
    public function isSuccessful(): bool
    {
        return $this === self::COMPLETED;
    }

    /**
     * Проверяет, является ли статус ошибочным
     */
    public function isError(): bool
    {
        return match($this) {
            self::FAILED => true,
            self::TIMEOUT => true,
            default => false,
        };
    }

    /**
     * Проверяет, можно ли повторить операцию с этим статусом
     */
    public function canRetry(): bool
    {
        return match($this) {
            self::FAILED => true,
            self::TIMEOUT => true,
            self::CANCELLED => false,
            default => false,
        };
    }

    /**
     * Возвращает CSS класс для отображения
     */
    public function getCssClass(): string
    {
        return match($this) {
            self::IN_PROGRESS => 'status-progress',
            self::COMPLETED => 'status-success',
            self::FAILED => 'status-error',
            self::CANCELLED => 'status-warning',
            self::TIMEOUT => 'status-error',
        };
    }

    /**
     * Возвращает цвет для отображения
     */
    public function getColor(): string
    {
        return match($this) {
            self::IN_PROGRESS => '#2196F3', // Синий
            self::COMPLETED => '#4CAF50',   // Зеленый
            self::FAILED => '#F44336',      // Красный
            self::CANCELLED => '#FF9800',   // Оранжевый
            self::TIMEOUT => '#F44336',     // Красный
        };
    }

    /**
     * Создает из целого числа
     */
    public static function fromInt(int $value): ?self
    {
        return match($value) {
            0 => self::IN_PROGRESS,
            1 => self::COMPLETED,
            2 => self::FAILED,
            3 => self::CANCELLED,
            4 => self::TIMEOUT,
            default => null,
        };
    }

    /**
     * Создает из булевых значений AsyncOperationDto
     */
    public static function fromAsyncOperation(
        int $status,
        bool $isCompleted,
        bool $isSuccessful,
        bool $hasFailed
    ): self {
        if ($hasFailed) {
            return self::FAILED;
        }

        if ($isCompleted && $isSuccessful) {
            return self::COMPLETED;
        }

        if ($isCompleted && !$isSuccessful) {
            return self::FAILED;
        }

        return self::IN_PROGRESS;
    }

    /**
     * Возвращает все статусы
     */
    public static function all(): array
    {
        return self::cases();
    }

    /**
     * Возвращает финальные статусы
     */
    public static function finalStatuses(): array
    {
        return [
            self::COMPLETED,
            self::FAILED,
            self::CANCELLED,
            self::TIMEOUT,
        ];
    }

    /**
     * Возвращает статусы ошибок
     */
    public static function errorStatuses(): array
    {
        return [
            self::FAILED,
            self::TIMEOUT,
        ];
    }
}
