<?php

namespace common\externalDecoding\kess3\dto;

use common\models\ProjectFiles;

/**
 * DTO для контекста операции
 * 
 * Содержит все данные, необходимые для выполнения операции
 */
class OperationContext
{
    public function __construct(
        private int $operationType,
        private ?ProjectFiles $file = null,
        private ?int $projectId = null,
        private ?int $fileId = null,
        private ?int $userId = null,
        private array $userInfo = [],
        private array $options = [],
        private ?string $callbackUrl = null,
        private array $metadata = []
    ) {}

    public function getOperationType(): int
    {
        return $this->operationType;
    }

    public function getFile(): ?ProjectFiles
    {
        return $this->file;
    }

    public function setFile(?ProjectFiles $file): self
    {
        $this->file = $file;
        return $this;
    }

    public function getProjectId(): ?int
    {
        return $this->projectId;
    }

    public function setProjectId(?int $projectId): self
    {
        $this->projectId = $projectId;
        return $this;
    }

    public function getFileId(): ?int
    {
        return $this->fileId;
    }

    public function setFileId(?int $fileId): self
    {
        $this->fileId = $fileId;
        return $this;
    }

    public function getUserId(): ?int
    {
        return $this->userId;
    }

    public function setUserId(?int $userId): self
    {
        $this->userId = $userId;
        return $this;
    }

    public function getUserInfo(): array
    {
        return $this->userInfo;
    }

    public function setUserInfo(array $userInfo): self
    {
        $this->userInfo = $userInfo;
        return $this;
    }

    public function getOptions(): array
    {
        return $this->options;
    }

    public function setOptions(array $options): self
    {
        $this->options = $options;
        return $this;
    }

    public function getOption(string $key, mixed $default = null): mixed
    {
        return $this->options[$key] ?? $default;
    }

    public function setOption(string $key, mixed $value): self
    {
        $this->options[$key] = $value;
        return $this;
    }

    public function getCallbackUrl(): ?string
    {
        return $this->callbackUrl;
    }

    public function setCallbackUrl(?string $callbackUrl): self
    {
        $this->callbackUrl = $callbackUrl;
        return $this;
    }

    public function getMetadata(): array
    {
        return $this->metadata;
    }

    public function setMetadata(array $metadata): self
    {
        $this->metadata = $metadata;
        return $this;
    }

    public function getMetadataValue(string $key, mixed $default = null): mixed
    {
        return $this->metadata[$key] ?? $default;
    }

    public function setMetadataValue(string $key, mixed $value): self
    {
        $this->metadata[$key] = $value;
        return $this;
    }

    /**
     * Создает контекст для операции декодинга
     */
    public static function forDecoding(
        ProjectFiles $file,
        string $callbackUrl,
        array $userInfo = [],
        array $options = []
    ): self {
        return new self(
            operationType: 5, // AsyncOperationDto::TYPE_KESS3_DECODING
            file: $file,
            projectId: $file->project_id,
            fileId: $file->id,
            userInfo: $userInfo,
            options: $options,
            callbackUrl: $callbackUrl
        );
    }

    /**
     * Создает контекст для операции кодинга OBD
     */
    public static function forOBDEncoding(
        int $projectId,
        string $callbackUrl,
        array $userInfo = [],
        array $options = []
    ): self {
        return new self(
            operationType: 6, // AsyncOperationDto::TYPE_KESS3_ENCODING_OBD
            projectId: $projectId,
            userInfo: $userInfo,
            options: $options,
            callbackUrl: $callbackUrl
        );
    }

    /**
     * Создает контекст для операции кодинга BOOT
     */
    public static function forBootEncoding(
        int $projectId,
        string $callbackUrl,
        array $userInfo = [],
        array $options = []
    ): self {
        return new self(
            operationType: 7, // AsyncOperationDto::TYPE_KESS3_ENCODING_BOOT
            projectId: $projectId,
            userInfo: $userInfo,
            options: $options,
            callbackUrl: $callbackUrl
        );
    }

    /**
     * Преобразует в массив
     */
    public function toArray(): array
    {
        return [
            'operationType' => $this->operationType,
            'projectId' => $this->projectId,
            'fileId' => $this->fileId,
            'userId' => $this->userId,
            'userInfo' => $this->userInfo,
            'options' => $this->options,
            'callbackUrl' => $this->callbackUrl,
            'metadata' => $this->metadata,
        ];
    }
}
