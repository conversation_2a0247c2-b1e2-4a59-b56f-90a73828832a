<?php

namespace common\externalDecoding\kess3\strategies;

use common\chip\alientech\entities\dto\AsyncOperationDto;
use common\externalDecoding\kess3\dto\OperationContext;
use common\externalDecoding\kess3\dto\ValidationResult;
use common\externalDecoding\kess3\enums\OperationType;
use common\externalDecoding\kess3\exceptions\OperationException;
use common\externalDecoding\kess3\interfaces\ApiClientInterface;
use common\externalDecoding\kess3\interfaces\OperationStateManagerInterface;

/**
 * Стратегия для операции декодинга KESS3
 *
 * Реализует специфичную логику декодинга файлов прошивки
 */
class DecodeOperationStrategy extends AbstractOperationStrategy
{
    public function __construct(
        ApiClientInterface $apiClient,
        OperationStateManagerInterface $stateManager
    ) {
        parent::__construct($apiClient, $stateManager);
    }

    /**
     * Проверяет поддержку типа операции
     */
    public function supports(int $operationType): bool
    {
        return $operationType === OperationType::KESS3_DECODING->value;
    }

    /**
     * Возвращает поддерживаемые типы операций
     */
    public function getSupportedOperationTypes(): array
    {
        return [OperationType::KESS3_DECODING->value];
    }

    /**
     * Подготавливает данные для отправки на API
     */
    public function prepareApiData(OperationContext $context): array
    {
        $file = $context->getFile();
        if (!$file) {
            throw OperationException::validationFailed(
                'File is required for decoding operation',
                context: $context->toArray()
            );
        }

        $filePath = $file->path;
        if (!$filePath || !file_exists($filePath)) {
            throw OperationException::fileError(
                'File not found: ' . ($filePath ?? 'unknown'),
                context: $context->toArray()
            );
        }

        return [
            'filePath' => $filePath,
            'userInfo' => $context->getUserInfo(),
            'callbackUrl' => $context->getCallbackUrl(),
            'options' => $context->getOptions(),
        ];
    }

    /**
     * Отправляет запрос на API для декодинга
     */
    protected function sendApiRequest(array $apiData, OperationContext $context): array
    {
        $this->checkApiAvailability();

        try {
            $operation = $this->apiClient->startDecoding(
                filePath: $apiData['filePath'],
                userInfo: $apiData['userInfo'],
                callbackUrl: $apiData['callbackUrl']
            );

            return [
                'operation' => $operation,
                'success' => true,
            ];

        } catch (\Exception $e) {
            throw OperationException::apiError(
                'Failed to start decoding: ' . $e->getMessage(),
                context: $context->toArray()
            );
        }
    }

    /**
     * Обрабатывает ответ от API
     */
    public function processApiResponse(array $apiResponse, OperationContext $context): AsyncOperationDto
    {
        if (!isset($apiResponse['operation']) || !($apiResponse['operation'] instanceof AsyncOperationDto)) {
            throw OperationException::apiError(
                'Invalid API response: missing operation data',
                context: $context->toArray()
            );
        }

        $operation = $apiResponse['operation'];

        // Дополняем операцию данными из контекста
        $operation->setProjectId($context->getProjectId());
        $operation->setFileId($context->getFileId());
        $operation->setUserId($context->getUserId());

        // Добавляем метаданные
        $metadata = array_merge($context->getMetadata(), [
            'strategy' => $this->getName(),
            'operationType' => 'decoding',
            'fileName' => $context->getFile()?->name,
        ]);
        $operation->setMetadata($metadata);

        return $operation;
    }

    /**
     * Дополнительная валидация для декодинга
     */
    protected function doValidate(OperationContext $context, ValidationResult $result): ValidationResult
    {
        // Проверяем наличие файла
        $file = $context->getFile();
        if (!$file) {
            $result->addError('file', 'File is required for decoding operation');
            return $result;
        }

        // Проверяем существование файла
        $filePath = $file->path;
        if (!$filePath || !file_exists($filePath)) {
            $result->addError('file', 'File does not exist: ' . ($filePath ?? 'unknown'));
            return $result;
        }

        // Проверяем размер файла
        $fileSize = filesize($filePath);
        if ($fileSize === false || $fileSize === 0) {
            $result->addError('file', 'File is empty or cannot be read');
            return $result;
        }

        // Максимальный размер файла - 50MB
        if ($fileSize > 50 * 1024 * 1024) {
            $result->addError('file', 'File is too large (max 50MB)');
            return $result;
        }

        // Проверяем callback URL
        if (!$context->getCallbackUrl()) {
            $result->addError('callbackUrl', 'Callback URL is required');
            return $result;
        }

        // Проверяем информацию о пользователе
        $userInfo = $context->getUserInfo();
        if (empty($userInfo)) {
            $result->addError('userInfo', 'User information is required');
            return $result;
        }

        // Проверяем обязательные поля в userInfo
        $requiredFields = ['username', 'email'];
        foreach ($requiredFields as $field) {
            if (!isset($userInfo[$field]) || empty($userInfo[$field])) {
                $result->addError('userInfo', "Field '{$field}' is required in user information");
            }
        }

        return $result;
    }

    /**
     * Обработка завершенной операции декодинга
     */
    protected function doProcessCompletedOperation(AsyncOperationDto $operation): \common\externalDecoding\kess3\dto\OperationResult
    {
        if (!$operation->isSuccessful()) {
            $error = $operation->getError();
            $errorMessage = is_string($error) ? $error : 'Decoding operation failed';

            return \common\externalDecoding\kess3\dto\OperationResult::failed(
                $errorMessage,
                $operation
            );
        }

        // Для успешного декодинга можно добавить дополнительную обработку
        $resultData = [
            'operationGuid' => $operation->getGuid(),
            'projectId' => $operation->getProjectId(),
            'fileId' => $operation->getProjectFileId(),
            'decodingResult' => $operation->getResult(),
        ];

        // Если есть результат, добавляем его
        if ($operation->getResult()) {
            $resultData['result'] = $operation->getResult();
        }

        return \common\externalDecoding\kess3\dto\OperationResult::success(
            operation: $operation,
            data: $resultData
        );
    }

    /**
     * Возвращает название стратегии
     */
    public function getName(): string
    {
        return 'KESS3 Decoding Strategy';
    }
}
