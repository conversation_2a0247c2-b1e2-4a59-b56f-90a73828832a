<?php

namespace common\externalDecoding\kess3\services;

use common\externalDecoding\kess3\dto\OperationResult;
use common\externalDecoding\kess3\interfaces\OperationCommandInterface;
use common\externalDecoding\kess3\interfaces\OperationExecutorInterface;
use common\externalDecoding\kess3\interfaces\ErrorHandlerInterface;
use common\externalDecoding\kess3\exceptions\OperationException;

/**
 * Менеджер операций KESS3
 * 
 * Координирует выполнение операций и обработку ошибок
 */
class OperationManager implements OperationExecutorInterface
{
    public function __construct(
        private ErrorHandlerInterface $errorHandler
    ) {}

    /**
     * Выполняет команду операции синхронно
     */
    public function execute(OperationCommandInterface $command): OperationResult
    {
        try {
            // Проверяем возможность выполнения
            if (!$this->canExecute($command)) {
                return OperationResult::failed('Command cannot be executed');
            }

            // Выполняем команду
            return $command->execute();

        } catch (OperationException $e) {
            // Обрабатываем ошибку операции
            $errorResult = $this->errorHandler->handleError($e);
            
            if ($errorResult->shouldRetry()) {
                return $this->retryOperation($command, $e, 1);
            }

            return OperationResult::failed($e->getMessage());

        } catch (\Exception $e) {
            // Обрабатываем общие ошибки
            $operationException = OperationException::apiError(
                'Unexpected error during operation execution: ' . $e->getMessage(),
                context: $command->getContext()
            );

            $this->errorHandler->logError($operationException);
            return OperationResult::failed($operationException->getMessage());
        }
    }

    /**
     * Выполняет команду асинхронно через очередь
     */
    public function executeAsync(OperationCommandInterface $command): string
    {
        // TODO: Реализовать асинхронное выполнение через очередь
        // Пока возвращаем уникальный идентификатор
        return uniqid('async_', true);
    }

    /**
     * Отменяет выполнение операции
     */
    public function cancel(string $operationId): bool
    {
        // TODO: Реализовать отмену операции
        return false;
    }

    /**
     * Получает статус выполнения операции
     */
    public function getStatus(string $operationId): ?OperationResult
    {
        // TODO: Реализовать получение статуса
        return null;
    }

    /**
     * Проверяет, может ли команда быть выполнена
     */
    public function canExecute(OperationCommandInterface $command): bool
    {
        try {
            return $command->canExecute();
        } catch (\Exception $e) {
            $this->errorHandler->logError(
                OperationException::validationFailed(
                    'Failed to check if command can be executed: ' . $e->getMessage(),
                    context: $command->getContext()
                )
            );
            return false;
        }
    }

    /**
     * Повторяет выполнение операции
     */
    private function retryOperation(
        OperationCommandInterface $command,
        OperationException $exception,
        int $attemptNumber
    ): OperationResult {
        $maxRetries = $command->getMaxRetries();
        
        if ($attemptNumber > $maxRetries) {
            return OperationResult::failed(
                "Operation failed after {$maxRetries} attempts: " . $exception->getMessage()
            );
        }

        try {
            // Получаем задержку перед повтором
            $errorResult = $this->errorHandler->handleError($exception);
            $delay = $errorResult->getRetryDelay();

            if ($delay > 0) {
                sleep($delay);
            }

            // Повторяем выполнение
            return $command->execute();

        } catch (OperationException $e) {
            // Если снова ошибка, проверяем нужно ли повторить еще раз
            $errorResult = $this->errorHandler->handleError($e);
            
            if ($errorResult->shouldRetry() && $attemptNumber < $maxRetries) {
                return $this->retryOperation($command, $e, $attemptNumber + 1);
            }

            return OperationResult::failed(
                "Operation failed after {$attemptNumber} attempts: " . $e->getMessage()
            );

        } catch (\Exception $e) {
            return OperationResult::failed(
                "Unexpected error during retry {$attemptNumber}: " . $e->getMessage()
            );
        }
    }

    /**
     * Выполняет пакет команд
     */
    public function executeBatch(array $commands): array
    {
        $results = [];

        foreach ($commands as $index => $command) {
            if (!($command instanceof OperationCommandInterface)) {
                $results[$index] = OperationResult::failed('Invalid command at index ' . $index);
                continue;
            }

            try {
                $results[$index] = $this->execute($command);
            } catch (\Exception $e) {
                $results[$index] = OperationResult::failed(
                    'Batch execution failed at index ' . $index . ': ' . $e->getMessage()
                );
            }
        }

        return $results;
    }

    /**
     * Получает статистику выполнения операций
     */
    public function getExecutionStats(): array
    {
        // TODO: Реализовать сбор статистики
        return [
            'totalExecuted' => 0,
            'successful' => 0,
            'failed' => 0,
            'retried' => 0,
            'averageExecutionTime' => 0,
        ];
    }

    /**
     * Проверяет здоровье менеджера операций
     */
    public function healthCheck(): array
    {
        return [
            'status' => 'healthy',
            'timestamp' => date('c'),
            'errorHandler' => $this->errorHandler ? 'available' : 'unavailable',
        ];
    }
}
