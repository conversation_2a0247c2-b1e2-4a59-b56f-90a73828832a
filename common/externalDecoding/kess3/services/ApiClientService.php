<?php

namespace common\externalDecoding\kess3\services;

use common\chip\alientech\entities\dto\AsyncOperationDto;
use common\chip\alientech\entities\dto\Kess3FileDto;
use common\externalDecoding\kess3\interfaces\ApiClientInterface;
use common\externalDecoding\kess3\exceptions\OperationException;
use common\externalDecoding\kess3\enums\OperationType;
use common\externalDecoding\kess3\enums\OperationStatus;

/**
 * Простой API клиент для KESS3
 */
class ApiClientService implements ApiClientInterface
{
    public function __construct(private array $config = []) {}

    public function startDecoding(string $filePath, array $userInfo, string $callbackUrl): AsyncOperationDto
    {
        // Простая заглушка - создаем операцию с правильными полями
        $operationData = (object)[
            'guid' => uniqid('kess3_decode_'),
            'asyncOperationType' => OperationType::KESS3_DECODING->value,
            'status' => OperationStatus::IN_PROGRESS->value,
            'isCompleted' => false,
            'isSuccessful' => false,
            'hasFailed' => false,
            'userInfo' => $userInfo,
        ];

        return new AsyncOperationDto($operationData);
    }

    public function startEncoding(array $encodeData, string $callbackUrl): AsyncOperationDto
    {
        throw new \Exception('Not implemented');
    }

    public function uploadModifiedFile(string $filePath, string $slotGuid): array
    {
        throw new \Exception('Not implemented');
    }

    public function downloadFile(string $url): Kess3FileDto
    {
        throw new \Exception('Not implemented');
    }

    public function getOperationStatus(string $operationGuid): AsyncOperationDto
    {
        throw new \Exception('Not implemented');
    }

    public function closeFileSlot(string $slotGuid): bool
    {
        return true;
    }

    public function closeAllFileSlots(): bool
    {
        return true;
    }

    public function isApiAvailable(): bool
    {
        return true;
    }

    public function getApiLimits(): array
    {
        return ['available' => true];
    }
}
