<?php
/**
 * @link http://www.yiiframework.com/
 * @copyright Copyright (c) 2008 Yii Software LLC
 * @license http://www.yiiframework.com/license/
 */

namespace common\assets;

use yii\web\AssetBundle;

/**
 * Main application asset bundle.
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2.0
 */
class ChipAsset extends AssetBundle
{
    public $basePath = '@webroot';
    public $baseUrl = '@chipassets';
    public $css = [
        'https://fonts.googleapis.com/css?family=Poppins:300,400,500,600',
        'css/bootstrap.min.css',
        'css/sweetalert.css',
        'css/font-awesome.min.css',
        'css/font-awesome.css',
        'css/themify-icons.css',
        'css/icofont.css',
        'css/jquery.mCustomScrollbar.css',
        'css/switchery.min.css',
        'css/list.css',
        'css/jquery.mCustomScrollbar.css',
        'css/component.css',
        'css/main.css',
        'css/pnotify.css',
        'css/pnotify.brighttheme.css',
        'css/pnotify.buttons.css',
        'css/pnotify.history.css',
        'css/pnotify.mobile.css',
        'css/notify.css',
        'css/style.css',
        'css/vue-select.css',
        'css/changes.css',
    ];
    public $js = [

        'js/popper.min.js',
        'js/bootstrap.min.js',
        'js/excanvas.js',
        'js/jquery.slimscroll.js',
        'js/switchery.min.js',

        'js/swithces.js',
        'js/pcoded.min.js',
        'js/session_timeout.min.js',
        'js/vertical-layout.min.js',
        'js/swithces.js',
        'https://unpkg.com/sweetalert/dist/sweetalert.min.js',
        'js/pnotify.js',
        'js/pnotify.desktop.js',
        'js/pnotify.buttons.js',
        'js/pnotify.confirm.js',
        'js/pnotify.callbacks.js',
        'js/pnotify.animate.js',
        'js/pnotify.history.js',
        'js/pnotify.mobile.js',
        'js/pnotify.nonblock.js',

        'js/ekko-lightbox.js',
        'js/lightbox.js',


        'js/notify.js',
        'js/jquery.mCustomScrollbar.concat.min.js',
        'js/classie.js',
        'js/modalEffects.js',
        'js/jquery.mb.YTPlayer.js',
        'js/vegas-youtube.js',
        'js/jquery.thooClock.js',
//        'js/script.js',
    ];
    public $depends = [
        'yii\web\YiiAsset',
        'yii\web\JqueryAsset',
        'yii\bootstrap\BootstrapAsset',
        'frontend\assets\ChipLocalizedAsset',
    ];
}



/* <link rel="icon" href="../assets/images/favicon.ico" type="image/x-icon">
	Line 24:     <link rel="stylesheet" type="text/css" href="../bower_components/bootstrap/css/bootstrap.min.css">
	Line 26:     <link rel="stylesheet" type="text/css" href="../assets/icon/themify-icons/themify-icons.css">
	Line 28:     <link rel="stylesheet" type="text/css" href="../assets/icon/font-awesome/css/font-awesome.min.css">
	Line 30:     <link rel="stylesheet" type="text/css" href="../assets/css/jquery.mCustomScrollbar.css">

	Line 32:     <link rel="stylesheet" href="../assets/pages/chart/radial/css/radial.css" type="text/css" media="all">
	Line 34:     <link rel="stylesheet" type="text/css" href="../assets/css/style.css">
	Line 66:                             <img class="img-fluid" src="../assets/images/logo.png" alt="Theme-Logo" />


	Line 2717:     <script src="../bower_components/jquery/js/jquery.min.js "></script>
	Line 2718:     <script src="../bower_components/jquery-ui/js/jquery-ui.min.js "></script>
	Line 2719:     <script src="../bower_components/popper.js/js/popper.min.js"></script>
	Line 2720:     <script src="../bower_components/bootstrap/js/bootstrap.min.js "></script>

	Line 2721:     <script src="../assets/pages/widget/excanvas.js "></script>
	Line 2723:     <script src="../bower_components/jquery-slimscroll/js/jquery.slimscroll.js "></script>
	Line 2725:     <script src="../bower_components/modernizr/js/modernizr.js "></script>
	Line 2727:     <script src="../assets/js/SmoothScroll.js"></script>
	Line 2728:     <script src="../assets/js/jquery.mCustomScrollbar.concat.min.js "></script>

	Line 2730:     <script src="../bower_components/chart.js/js/Chart.js"></script>
	Line 2731:     <script src="../assets/pages/widget/amchart/amcharts.js"></script>
	Line 2732:     <script src="../assets/pages/widget/amchart/serial.js"></script>
	Line 2733:     <script src="../assets/pages/widget/amchart/light.js"></script>
	Line 2735:     <script src="../assets/js/pcoded.min.js"></script>
	Line 2736:     <script src="../assets/js/vertical/vertical-layout.min.js "></script>
	Line 2738:     <!--<script  src="../assets/pages/dashboard/custom-dashboard.min.js"></script>-->
	Line 2739:     <script src="../assets/pages/dashboard/custom-dashboard.js"></script>
	Line 2740:     <script src="../assets/js/script.js "></script>
 */
