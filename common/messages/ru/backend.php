<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    'Account' => 'Аккаунт',
    'Application' => 'Приложение',
    'Application settings' => 'Настройки приложения',
    'Application timeline' => 'Хроника приложения',
    'Are you sure you want to delete this item?' => 'Вы уверены, что хотите удалить эту запись?',
    'Are you sure you want to flush this cache?' => 'Вы уверены, что хотите сбросить этот кеш?',
    'Article Categories' => 'Категории статей',
    'Articles' => 'Статьи',
    'Backend sidebar collapsed' => 'Скрыть боковую панель',
    'Backend theme' => 'Тема панели управления',
    'Boxed backend layout' => 'Коробочный шаблон панели управления',
    'CPU Usage' => 'Использование CPU',
    'Cache' => 'Кеш',
    'Cache entry has been successfully deleted' => 'Запись была успешно удалена из кеша',
    'Cache has been successfully flushed' => 'Кеш был успешно сброшен',
    'Carousel Widgets' => 'Виджеты карусели',
    'Carousel slide was successfully saved' => 'Слайд был успешно сохранён',
    'Category' => 'Категория',
    'Clear' => 'Очистить',
    'Content' => 'Контент',
    'Count' => 'Количество',
    'Create' => 'Создать',
    'Create {modelClass}' => 'Создание {modelClass}',
    'DB Type' => 'Тип БД',
    'DB Version' => 'Версия БД',
    'Date' => 'Дата',
    'Delete' => 'Удалить',
    'Delete a value with the specified key from cache' => 'Удалить значение по ключу из кеша',
    'Disabled' => 'Неактивно',
    'Edit account' => 'Редактировать аккаунт',
    'Edit profile' => 'Редактировать профиль',
    'Email' => 'Email',
    'Enabled' => 'Активно',
    'Error #{id}' => 'Ошибка #{id}',
    'Error {code}' => 'Ошибка {code}',
    'Event' => 'Событие',
    'External IP' => 'Внешний IP',
    'Female' => 'Женский',
    'File Manager' => 'Менеджер файлов',
    'File Storage' => 'Файловое хранилище',
    'File Storage Items' => 'Записи о файлах',
    'Files in storage' => 'Файлов в хранилище',
    'Fixed backend layout' => 'Фиксированный шаблон панели управления',
    'Flush' => 'Сбросить',
    'Free Swap' => 'Свободно Swap',
    'Free memory' => 'Свободно памяти',
    'Frontend maintenance mode' => 'Сервисный режим фронтенд части',
    'Hello, {username}' => 'Привет, {username}',
    'Hostname' => 'Имя хоста',
    'I18n Messages' => 'I18N переводы',
    'I18n Source Messages' => 'Тексты',
    'ID' => 'ID',
    'If you leave this field empty, the slug will be generated automatically' => 'Если вы оставите это поле пустым, ЧПУ будет сгенерирован автоматически',
    'Incorrect username or password.' => 'Неправильный логин или пароль',
    'Internal IP' => 'Внутренний IP',
    'Invalidate tag' => 'Сбросить тег',
    'Kernel version' => 'Версия ядра',
    'Key' => 'Ключ',
    'Key Storage Items' => 'Записи',
    'Key-Value Storage' => 'Ключ-Значение',
    'Language' => 'Язык',
    'Level' => 'Уровень',
    'Load average' => 'Средняя нагрузка',
    'Log Time' => 'Время события',
    'Logout' => 'Выход',
    'Logs' => 'Журнал',
    'Main' => 'Главная',
    'Male' => 'Мужской',
    'Member since {0, date, short}' => 'Участник с {0, date, short}',
    'Memory' => 'Память',
    'Memory Usage' => 'Использование памяти',
    'Menu Widgets' => 'Виджеты меню',
    'Message' => 'Сообщение',
    'More info' => 'Подробнее',
    'Network' => 'Сеть',
    'New user ({identity}) was registered at {created_at}' => 'Новый пользователь ({identity}) был зарегистрирован {created_at}',
    'No events found' => 'Событий нет',
    'Not Published' => 'Не опубликовано',
    'Number of cores' => 'Кол-во ядер',
    'OS' => 'ОС',
    'OS Release' => 'Версия ОС',
    'Off' => 'Выкл',
    'On' => 'Вкл',
    'Operating System' => 'Операционная система',
    'Other' => 'Остальные',
    'PHP Version' => 'Версия РHP',
    'Pages' => 'Страницы',
    'Password' => 'Пароль',
    'Password Confirm' => 'Подтверждение пароля',
    'Port' => 'Порт',
    'Prefix' => 'Префикс',
    'Processor' => 'Процессор',
    'Processor Architecture' => 'Архитектура процессора',
    'Profile' => 'Профиль',
    'Published' => 'Опубликовано',
    'Real time' => 'В режиме реального времени',
    'Remember Me' => 'Запомнить меня',
    'Reset' => 'Сбросить',
    'Save' => 'Сохранить',
    'Search' => 'Поиск',
    'Select cache' => 'Выберите кеш',
    'Settings was successfully saved' => 'Настройки были успешно сохранены',
    'Sign In' => 'Вход',
    'Sign me in' => 'Войти',
    'Software' => 'ПО',
    'Sorry, application failed to collect information about your system. See {link}.' => 'Извините, но приложению не удалось собрать информацию о вашей системе. Смотрите {link}.',
    'Source Message' => 'Текст',
    'Static pages' => 'Статические страницы',
    'System' => 'Система',
    'System Date' => 'Системная дата',
    'System Information' => 'Информация о системе',
    'System Logs' => 'Системный журнал',
    'System Time' => 'Системное время',
    'Tag' => 'Тег',
    'TagDependency was invalidated' => 'TagDependency был инвалидирован',
    'Text Blocks' => 'Текстовые блоки',
    'Text Widgets' => 'Текстовые виджеты',
    'This email has already been taken.' => 'Этот email уже кем-то используется.',
    'This username has already been taken.' => 'Это имя пользователя уже занято.',
    'Time' => 'Время',
    'Timeline' => 'Хроника',
    'Timezone' => 'Часовой пояс',
    'Toggle navigation' => 'Навигация',
    'Total Swap' => 'Общий Swap',
    'Total memory' => 'Общая память',
    'Translation' => 'Перевод',
    'Update' => 'Редактировать',
    'Update {modelClass}: ' => 'Редактирование {modelClass}: ',
    'Uptime' => 'Uptime',
    'Used size' => 'Использованный размер',
    'User Registrations' => 'Регистраций',
    'Username' => 'Имя пользователя',
    'Users' => 'Пользователи',
    'View all' => 'Смотреть всё',
    'View user' => 'Просмотр пользователя',
    'Web Server' => 'Веб-сервер',
    'Widget Carousel Items' => 'Слайды',
    'Widget Carousels' => 'Виджет карусели',
    'Widget Menus' => 'Виджеты меню',
    'You have new event' => 'У вас новое событие',
    'You have new user!' => 'У вас новый пользователь!',
    'You have {num} log items' => 'У вас {num} записей в журнале',
    'Your account has been successfully saved' => 'Ваш аккаунт был успешно сохранён',
    'Your profile has been successfully saved' => 'Ваш профиль был успешно сохранён',
    'i18n' => 'I18N',
    'i18n Message' => 'Перевод',
    'i18n Source Message' => 'Текст',
    'Catalogs' => 'Справочники',

    'Brands' => 'Марки',
    'Brand ID' => 'Марка',
    'Create Brand' => 'Добавить марку авто',
    'Brand' => 'Марка',

    'Models' => 'Модели',
    'Model ID' => 'Модель',
    'Chip Models' => 'Модели',
    'Create Model' => 'Добавить модель авто',

    'Chip Generations' => 'Поколения',
    'Generations' => 'Поколения',
    'Generation ID' => 'Поколение',
    'Create Chip Generation' => 'Добавить Поколение',

    'Chip Engines' => 'Двигатели',
    'Engines' => 'Двигатели',
    'Engine ID' => 'Двигатель',
    'Create Chip Engine' => 'Добавить Двигатель',

    'Chip Ecus' => 'Список ЭБУ',
    'Chip Ecu' => 'ЭБУ',
    'Ecus' => 'Параметры ЭБУ',
    'Create Chip Ecu' => 'Добавить ЭБУ',
    'Chip Ecu Dicts' => 'Список ЭБУ',
    'Create Chip Ecu Dict' => 'Добавить ЭБУ',

    'Chip Stages' => 'Варианты тюнинга',
    'Stages' => 'Варианты тюнинга',
    'Create Chip Stage' => 'Добавить вариант тюнинга',

    'Title' => 'Наименование',
    'Slug' => 'Алиас',
    'Created At' => 'Создано',
    'Updated At' => 'Обновлено',
    'Deleted At' => 'Удалено',

    'Management' => 'Управление',
    'Projects' => 'Проекты',
    'Chip Additions' => 'Дополнительные параметры',
    'Additions' => 'Доп параметры',
    'Create Chip Addition' => 'Добавить доп параметр тюнинга',

    'Chip Ecu Additions' => 'Настройки Марка ЭБУ Доп параметры',
    'Additions' => 'Доп параметры',
    'Create Chip Ecu Additions' => 'Добавить настройку Марка-ЭБУ',
    'Edit information' => 'Редактирование информации',
    'Edit additions' => 'Дополнительные параметры',
    'Edit tuning additions' => 'Дополнительные параметры тюнинга',
    'Edit tuning stages' => 'Уровни тюнинга',
    'Horse power' => 'Лошадиные силы',
    'Torque' => 'Крутящий момент',
    'On' => 'Вкл',
    'Off' => 'Выкл',
    'Full Ecu Settings' => 'Настройки ЭБУ',
    'Quick Ecu Settings' => 'Быстрые Настройки ЭБУ',
    'Comment' => 'Комментарий',
    'Price' => 'Цена',
    'Price Car' => 'Цена легковые',
    'Price Track' => 'Цена грузовики',
    'Price Moto' => 'Цена мотоциклы',
    'Price Boat' => 'Цена лодки',
    'Price Agro' => 'Цена агротехника',
    'Vehicle type' => 'Тип транспортного средства',
    'Car' => 'Легковые',
    'Track' => 'Грузовые',
    'Moto' => 'Мото',
    'Boat' => 'Водный транспорт',
    'Agrarian' => 'Аграрные',
    'Select' => 'Выберите',
    'Select vehicle' => 'Выберите тип авто',
    'Select brand' => 'Выберите марку',
    'Information' => 'Информация',
    'Financial' => 'Финансы',
    'Send Credits to user' => 'Начислить пользователю Кредиты',
    'Input sum Credits' => 'Введите нужную сумму Кредитов',
    'Balance' => 'Баланс',
    'Live user balance' => 'Текущий баланс пользователя',
    'Are you sure you want to send credits to this client?' => 'Вы действительно хотите передать кредиты пользователю?',
    'Credits success sent' => 'Счет пользователя успешно пополнен',
    'Not enought credits' => 'Недостаточно средств на счете',
    'Error data' => 'Ошибка в отправленных данных',
    'Create Project' => 'Добавить проект',
    'Credits' => 'Кредитов',
    'Vehicle and Client information' => 'Информация о клиенте и транспортном средстве',
    'Vehicle information' => 'Информация о транспортном средстве',
    'Select Brand first' => 'Сначала выберите марку',
    'Select Model first' => 'Сначала выберите модель',
    'Select Generation first' => 'Сначала выберите поколение',
    'Select Engine first' => 'Сначала выберите двигатель',
    'Gearbox list' => 'Трансмиссии',
    'View' => 'Просмотр',
    'Are you sure?' => 'Вы уверены?',
    'Are you sure want to delete this item' => 'Вы действительно хотите удалить этот элемент',
    'Chip Ecus Trash' => 'Корзина ЭБУ',
    'Are you sure want to restore this item and his parents?' => 'Вы действительно хотите восстановить этот элемент и его родительские элементы?',
    'Restore' => 'Восстановить',
    'Vehicle' => 'Двигатель',
    'Ecu' => 'ЭБУ',
    'Tarif' => 'Тариф',
    'Gearbox' => 'Трансмиссия',
    'Year' => 'Год',
    'Vin Number' => 'VIN номер',
    'Hardware Number (recommended)' => 'Номер оборудования (рекомендуется к заполнению)',
    'Soft Number (recommended)' => 'Номер программного обеспечения (рекомендуется к заполнению)',
//    'Registration Number' => 'Регистрационный номер ТС',
    'Engine Hp' => 'Мощность (л.с.)',
    'Engine Kwt' => 'Мощность (кВт)',
    'Kwt' => 'кВт',
    'Master or Slave' => 'Тип оборудования',
    'Readmethod ID' => 'Метод чтения',
    'Status' => 'Status',
    'Client Name' => 'Client Name',
    'Contacts' => 'Контакты',
    'Phone or email' => 'Контактная информация клиента',
    'Stage ID' => 'Уровень',
    'Vehicle types' => 'Типы ТС',
    'BHP' => 'Hp',
    'NM' => 'Nm',
    'Please choose the required timeframe for this fileservice' => 'Выберите срочность обработки вашего проекта',
    'Extra info' => 'Дополнительная информация',
    'Additional options' => 'Дополнительные параметры',
    'Today' => 'Сегодня',
    'Yesterday' => 'Вчера',
    'This Month' => 'Текущий месяц',
    'Last Month' => 'Прошлый месяц',
    'Dinostend file' => 'Результаты проверок на диностенде',



];
