$schema: https://raw.githubusercontent.com/context-hub/generator/refs/heads/main/json-schema.json

documents:
  - 
    title: Messaging System
    description: User and project messaging components
    outputPath: features/messaging.md
    overwrite: true
    sources:
      - 
        type: file
        sourcePaths:
          - common/models/UserMessages.php
          - common/models/UserMessagesUsers.php
          - common/models/UserMessagesFiles.php
          - common/models/ProjectMessages.php
          - common/models/ProjectMessageLog.php
          - common/models/ProjectMessagesSend.php
          - common/helpers/MessageHelper.php

  - 
    title: Project Messaging DTOs
    description: Data Transfer Objects for project messaging
    outputPath: features/messaging-dtos.md
    overwrite: true
    sources:
      - 
        type: file
        sourcePaths:
          - common/chip/project/entities/dto/ProjectMessageDto.php
          - common/chip/project/entities/dto/ProjectMessageNoteDto.php
          - common/chip/project/entities/dto/ProjectMessageSystemNoteDto.php
          - common/chip/project/entities/dto/ProjectMessageTelegramDto.php
          - common/chip/project/entities/dto/ProjectMessageGreenLineDto.php
          - common/chip/project/entities/dto/ProjectMessageDtoCollection.php

  - 
    title: Project Message Services
    description: Services for handling project messages
    outputPath: features/messaging-services.md
    overwrite: true
    sources:
      - 
        type: file
        sourcePaths:
          - common/chip/project/services/MessageService.php
          - common/chip/project/repositories/ProjectMessageRepository.php
          - common/chip/project/repositories/ProjectMessageSendRepository.php
          - common/chip/project/factories/ProjectMessageFactory.php
          - common/chip/project/builders/ProjectMessageBuilder.php
          - common/chip/project/jobs/ProjectMessageSendJob.php
