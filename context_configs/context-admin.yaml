documents:
  - 
    title: Backend Administration
    description: Backend administration components and views
    outputPath: features/admin.md
    overwrite: true
    sources:
      - 
        type: file
        sourcePaths:
          - backend/controllers/UserController.php
          - backend/controllers/ProjectsController.php
          - backend/controllers/ChipController.php
          - backend/models/UserForm.php
          - backend/models/AccountForm.php
          - backend/models/LoginForm.php
          - backend/views/layouts/admin.php

  - 
    title: RBAC Authorization
    description: Role-Based Access Control system
    outputPath: features/rbac.md
    overwrite: true
    sources:
      - 
        type: file
        sourcePaths:
          - backend/modules/rbac/models/RbacAuthItem.php
          - backend/modules/rbac/models/RbacAuthItemChild.php
          - backend/modules/rbac/models/RbacAuthRule.php
          - backend/modules/rbac/models/RbacAuthAssignment.php
          - backend/modules/rbac/controllers/RbacAuthItemController.php
          - backend/modules/rbac/controllers/RbacAuthItemChildController.php
          - common/rbac/rule/OwnModelRule.php

  - 
    title: System Management
    description: System management and configuration components
    outputPath: features/system.md
    overwrite: true
    sources:
      - 
        type: file
        sourcePaths:
          - backend/modules/system/controllers/SettingsController.php
          - backend/modules/system/controllers/InformationController.php
          - backend/modules/system/controllers/CacheController.php
          - backend/modules/system/controllers/KeyStorageController.php
          - backend/modules/system/controllers/LogController.php
          - common/models/KeyStorageItem.php
          - common/components/keyStorage/KeyStorage.php
