$schema: https://raw.githubusercontent.com/context-hub/generator/refs/heads/main/json-schema.json

documents:
  - 
    title: Chip Tuning Services
    description: Core chip tuning services and business logic
    outputPath: features/chip-tuning.md
    overwrite: true
    sources:
      - 
        type: file
        sourcePaths:
          - common/chip/alientech/services/AlientechService.php
          - common/chip/alientech/services/FileService.php
          - common/chip/alientech/services/AlientechProjectService.php
          - common/chip/project/services/ProjectService.php
          - common/chip/project/services/CreateProjectService.php
          - common/chip/project/services/ProjectFileService.php

  - 
    title: Chip Tuning DTOs
    description: Data Transfer Objects used for chip tuning operations
    outputPath: features/chip-tuning-dtos.md
    overwrite: true
    sources:
      - 
        type: file
        sourcePaths:
          - common/chip/alientech/entities/dto/AlientechDto.php
          - common/chip/alientech/entities/dto/AlientechLinkDto.php
          - common/chip/alientech/entities/dto/AsyncOperationDto.php
          - common/chip/project/entities/dto/ProjectDto.php
          - common/chip/project/entities/dto/ProjectFileDto.php
          - common/chip/project/entities/dto/ProjectMessageDto.php

  - 
    title: Autopack Features
    description: Autopack-related services and components
    outputPath: features/autopack.md
    overwrite: true
    sources:
      - 
        type: file
        sourcePaths:
          - common/chip/autopack/services/AutopackService.php
          - common/chip/autopack/services/AutopackLinkService.php
          - common/chip/autopack/entities/dto/AutopackDto.php
          - common/chip/autopack/entities/dto/AutopackConfigDto.php
          - common/chip/autopack/entities/dto/AutopackProcDto.php
          - common/chip/autopack/repositories/AutopackProcRepository.php
          - common/chip/autopack/repositories/AutopackConfigRepository.php
