<?php
$config = [
    'homeUrl' => Yii::getAlias('@apiUrl'),
    'controllerNamespace' => 'api\controllers',
    'defaultRoute' => 'site/index',
    'bootstrap' => ['maintenance'],
    'modules' => [
        'v1' => \api\modules\v1\Module::class
    ],
    'components' => [
        'errorHandler' => [
            'errorAction' => 'site/error'
        ],
        'maintenance' => [
            'class' => common\components\maintenance\Maintenance::class,
            'enabled' => function ($app) {
                if (env('APP_MAINTENANCE') === '1') {
                    return true;
                }
                return $app->keyStorage->get('frontend.maintenance') === 'enabled';
            }
        ],
        'request' => [
            'enableCookieValidation' => false,
//            'baseUrl' => '',
            // !!! insert a secret key in the following (if it is empty) - this is required by cookie validation
            'cookieValidationKey' => 'xf_bopzPdE5X3_za4mmcVreGxGRaAqEJ',
            'parsers' => [
                'application/json' => 'yii\web\JsonParser'
            ]
        ],
        'response' => [
            // ...
            'formatters' => [
                \yii\web\Response::FORMAT_JSON => [
                    'class' => 'yii\web\JsonResponseFormatter',
                    'prettyPrint' => YII_DEBUG, // use "pretty" output in debug mode
                    'encodeOptions' => JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE,
                    // ...
                ],
            ],
        ],
        'user' => [
            'class' => yii\web\User::class,
            'identityClass' => common\models\User::class,
            'loginUrl' => ['/user/sign-in/login'],
            'enableAutoLogin' => true,
            'as afterLogin' => common\behaviors\LoginTimestampBehavior::class
        ],
        'urlManager' => [
            'enablePrettyUrl' => true,
            'showScriptName' => false,
            'class' => 'yii\web\UrlManager',
            'rules' => [
                ['class' => 'yii\rest\UrlRule', 'controller' => 'v1/vehicle', 'pluralize' => false,],
                [
                    'class' => 'yii\rest\UrlRule',
                    'controller' => 'v1/product',
                    'pluralize' => true,
                    //                    'except' => ['update'],
                    'extraPatterns' => [
                        //                        'OPTIONS' => 'options',
                        'GET types' => 'types',
                        'GET search/<text:\w+>' => 'search',
                        'POST update/<id:\d+>' => 'update',
                        'GET delete/<id:\d+>' => 'delete',
                        'POST add' => 'create',
                        'GET <id:\d+>/expenses' => 'expenses',
                        //                        'POST file-ukr-post' => 'file-ukr-post',
                    ],
                ],
        [
            'class' => 'yii\rest\UrlRule',
            'controller' => 'v1/expenses',
            'pluralize' => true,
            //                    'except' => ['update'],
            'extraPatterns' => [
                //                        'OPTIONS' => 'options',
                'GET search/<text:\w+>' => 'search',
                'POST update/<id:\d+>' => 'update',
                'GET delete/<id:\d+>' => 'delete',
                'POST add' => 'create',
                //                        'POST file-ukr-post' => 'file-ukr-post',
            ],
        ],
    ],
],
    ]
];

return $config;
