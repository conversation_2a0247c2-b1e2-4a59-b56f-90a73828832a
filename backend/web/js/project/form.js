$('#projectForm').on('beforeSubmit', function (event) {
    console.log(event);
    // if (errorAttributes.length == 0) {
    if(document.getElementById("file_added")) {
        console.log('file_added');
        return true;
    } else {
        console.log('file_not_added');
        if (confirm("Are you sure create project without file?")) {
            return true;
        }
        // swal({
        //      title: "Are you sure create project without file?",
        //      // text: "Your will be able to recover this option later!",
        //      type: "warning",
        //      showCancelButton: true,
        //      confirmButtonClass: "btn btn-danger",
        //      confirmButtonText: "Yes, create project!",
        //      closeOnConfirm: true,
        //  },
        //  function(isConfirm) {
        //          console.log('isConfirm');
        //          console.log(isConfirm);
        //      if (isConfirm) {
        //          console.log('return true');
        //          return true;
        //      }
        //  });
        $('#create_project').removeAttr('disabled');
        return false;
    }
    // return true;
    // }
    return false;
});
$('#projectForm').on('beforeValidate', function (e) {
    $('#create_project').attr('disabled', 'disabled');
    if(document.getElementById("ecu_id")) {
        if ($('#ecu_id').val() > 0) {
            return true;
        } else {
            if(document.getElementById("projects-custom_ecu")) {
                if ($('#projects-custom_ecu').val().length > 0) {
                    return true;
                } else {
                    swal("Error", "Select or type Ecu!", "error");
                    $('#create_project').removeAttr('disabled');
                    return false;
                }
            }
        }
    }
    return true;
});
$('#projectForm').on('afterValidate', function (event, messages, errorAttributes) {
    let data = $('#projectForm').data('yiiActiveForm');
    //check if we are in submission process and if there are any errors
    if (data.submitting && errorAttributes.length > 0) {
        $('#create_project').removeAttr('disabled');
    }
});
$('#engine_hp').on('keyup', function (e) {
    if (parseInt(this.value) > 0) {
        $('#engine_kwt').val(parseInt(this.value/1.341));
    }
});

$('#engine_kwt').on('keyup', function (e) {
    if (parseInt(this.value) > 0) {
        $('#engine_hp').val(parseInt(this.value*1.341));
    }
});
$(window).on('load', function () {


function getProjectAllData(params) {
    $('#stages_div').html('');
    $('#ecu_div').html('');
    console.log(params);
    // var json = JSON.parse(params);
    // console.log(json);
    $.ajax({
        method: "POST",
        url: "get-all-data/",
        data: params,
        // processData : false,
        // dataType: 'json',
    })
        .done(function( msg ) {
            $('#stages_div').html(msg.priceHtml);
            $('#ecu_div').html(msg.ecuHtml);
            // initSwitchers();
            // console.log();
            // alert( "Data Saved: " + msg );
            initSwitchers();
        });
}
function clearYear() {
    // console.log('yesr select');
    $("#year").val("").trigger('change');
}
function getStages() {
    // console.log('getStages');
    var params = {};
    params.vehicle_id = $("#vehicle_id").val();
    params.brand_id = $("#brand_id").val();

    params.model_id = $("#model_id").val();
    // console.log(model_id);

    params.generation_id = $("#generation_id").val();
    // console.log(generation_id);

    params.engine_id = $("#engine_id").val();
    // console.log(engine_id);

    params.ecu_id = $("#ecu_id").val();
    // console.log(ecu_id);
    console.log(params);

    // getStagesList(params);
    getProjectAllData(params);
}

}(jQuery));
