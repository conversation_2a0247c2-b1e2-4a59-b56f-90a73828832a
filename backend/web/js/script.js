"use strict";
$(document).ready(function() {

    // card js start
    $(".card-header-right .close-card").on('click', function() {
        var $this = $(this);
        $this.parents('.card').animate({
            'opacity': '0',
            '-webkit-transform': 'scale3d(.3, .3, .3)',
            'transform': 'scale3d(.3, .3, .3)'
        });

        setTimeout(function() {
            $this.parents('.card').remove();
        }, 800);
    });
    $(".card-header-right .reload-card").on('click', function() {
        var $this = $(this);
        $this.parents('.card').addClass("card-load");
        $this.parents('.card').append('<div class="card-loader"><i class="fa fa-spinner rotate-refresh"></div>');
        setTimeout(function() {
            $this.parents('.card').children(".card-loader").remove();
            $this.parents('.card').removeClass("card-load");
        }, 3000);
    });
    $(".card-header-right .card-option .fa-chevron-left").on('click', function() {
        var $this = $(this);
        if ($this.hasClass('fa-chevron-right')) {
            $this.parents('.card-option').animate({
                'width': '35px',
            });
        } else {
            $this.parents('.card-option').animate({
                'width': '190px',
            });
        }
        $(this).toggleClass("fa-chevron-right").fadeIn('slow');
    });
    $(".card-header-right .minimize-card").on('click', function() {
        var $this = $(this);
        var port = $($this.parents('.card'));
        var card = $(port).children('.card-block').slideToggle();
        $(this).toggleClass("fa-minus").fadeIn('slow');
        $(this).toggleClass("fa-plus").fadeIn('slow');
    });
    $(".card-header-right .full-card").on('click', function() {
        var $this = $(this);
        var port = $($this.parents('.card'));
        port.toggleClass("full-card");
        $(this).toggleClass("fa-window-restore");
    });

    $(".card-header-right .icofont-spinner-alt-5").on('mouseenter mouseleave', function() {
        $(this).toggleClass("rotate-refresh").fadeIn('slow');
    });
    $("#more-details").on('click', function() {
        $(".more-details").slideToggle(500);
    });
    $(".mobile-options").on('click', function() {
        $(".navbar-container .nav-right").slideToggle('slow');
    });
    $(".search-btn").on('click', function() {
        $(".main-search").addClass('open');
        $('.main-search .form-control').animate({
            'width': '200px',
        });
    });
    $(".search-close").on('click', function() {
        $('.main-search .form-control').animate({
            'width': '0',
        });
        setTimeout(function() {
            $(".main-search").removeClass('open');
        }, 300);
    });
    // $(".header-notification").on('click', function() {
    //     $(this).children('.show-notification').slideToggle(500);
    //     $(this).toggleClass('active');
    //
    // });

    function clearYear() {
        // console.log('yesr select');
        $("#year").val("").trigger('change');
    }

    $(document).ready(function(){
        clearYear();
        $(document).on('click', '.dino_file', function (e) {
            console.log(e);
            var image = $(this).clone();
            var src = $(image).attr('src');
            var img = '<img src="'+src+'"/>';
            $('.modal-body').html(img);
            $('#ajaxCrudModal').modal('show');
        });


        // $(document).on('click', '#create_project', function(e){
        //     // console.log($(e.target).hasAttr('disabled'));
        //     $(e.target).attr('disabled', 'disabled');
        //     return true;
        //     $('#projectForm').submit();
        //     setTimeout(function() {
        //         $(e.target).removeAttr('disabled');
        //     }, 10000);
        // });

        $(".header-notification").click(function(){
            $(this).find(".show-notification").slideToggle(500);
            $(this).toggleClass('active');
        });
    });
    $(document).on("click", function(event){
        var $trigger = $(".header-notification");
        if($trigger !== event.target && !$trigger.has(event.target).length){
            $(".show-notification").slideUp(300);
            $(".header-notification").removeClass('active');
        }
    });

    // card js end
    // $.mCustomScrollbar.defaults.axis = "yx";
    // $("#styleSelector .style-cont").slimScroll({
    //     setTop: "1px",
    //     height:"calc(100vh - 495px)",
    // });
    // $(".main-menu").mCustomScrollbar({
    //     setTop: "1px",
    //     setHeight: "calc(100% - 56px)",
    // });
    // /*chatbar js start*/
    // /*chat box scroll*/
    // var a = $(window).height() - 80;
    // $(".main-friend-list").slimScroll({
    //     height: a,
    //     allowPageScroll: false,
    //     wheelStep: 5,
    //     color: '#1b8bf9'
    // });

    // search
    // $("#search-friends").on("keyup", function() {
    //     var g = $(this).val().toLowerCase();
    //     $(".userlist-box .media-body .chat-header").each(function() {
    //         var s = $(this).text().toLowerCase();
    //         $(this).closest('.userlist-box')[s.indexOf(g) !== -1 ? 'show' : 'hide']();
    //     });
    // });

    // open chat box
    // $('.displayChatbox').on('click', function() {
    //     var my_val = $('.pcoded').attr('vertical-placement');
    //     if (my_val == 'right') {
    //         var options = {
    //             direction: 'left'
    //         };
    //     } else {
    //         var options = {
    //             direction: 'right'
    //         };
    //     }
    //     $('.showChat').toggle('slide', options, 500);
    // });

    //open friend chat
    // $('.userlist-box').on('click', function() {
    //     var my_val = $('.pcoded').attr('vertical-placement');
    //     if (my_val == 'right') {
    //         var options = {
    //             direction: 'left'
    //         };
    //     } else {
    //         var options = {
    //             direction: 'right'
    //         };
    //     }
    //     $('.showChat_inner').toggle('slide', options, 500);
    // });
    //back to main chatbar
    // $('.back_chatBox').on('click', function() {
    //     var my_val = $('.pcoded').attr('vertical-placement');
    //     if (my_val == 'right') {
    //         var options = {
    //             direction: 'left'
    //         };
    //     } else {
    //         var options = {
    //             direction: 'right'
    //         };
    //     }
    //     $('.showChat_inner').toggle('slide', options, 500);
    //     $('.showChat').css('display', 'block');
    // });
    // $('.back_friendlist').on('click', function() {
    //     var my_val = $('.pcoded').attr('vertical-placement');
    //     if (my_val == 'right') {
    //         var options = {
    //             direction: 'left'
    //         };
    //     } else {
    //         var options = {
    //             direction: 'right'
    //         };
    //     }
    //     $('.p-chat-user').toggle('slide', options, 500);
    //     $('.showChat').css('display', 'block');
    // });
    // /*chatbar js end*/
    $(function() {
        $('[data-toggle="tooltip"]').tooltip()
    })
});
function togglePass(){
    var attr = $('#userform-password').attr('type');
    if (attr == 'password') {
        $('#userform-password').attr('type', 'text');
    } else {
        $('#userform-password').attr('type', 'password');
    }
}

$(document).ready(function() {
    // $('tbody tr[myurl]').click(function (e) {
    //     if ($(e.target).is('td')) {
    //         window.location.href = $(this).attr('myurl');
    //     }
    // });

    $('[data-toggle="tooltip"]').tooltip();

    $('[data-toggle="popover"]').popover({
        html: true,
        content: function() {
            return $('#primary-popover-content').html();
        }
    });
    preloaderCycle();
});
function preloaderCycle() {
    $(".theme-loader").animate({
        'z-index': "9999",
        'opacity': "1"
    },100);
    // setTimeout(function() {
        $(".theme-loader").animate({
            'opacity': "0",
            'z-index': "-1"
        },1000);
    // }, 800);
}
// toggle full screen
function toggleFullScreen() {
    var a = $(window).height() - 10;

    if (!document.fullscreenElement && // alternative standard method
        !document.mozFullScreenElement && !document.webkitFullscreenElement) { // current working methods
        if (document.documentElement.requestFullscreen) {
            document.documentElement.requestFullscreen();
        } else if (document.documentElement.mozRequestFullScreen) {
            document.documentElement.mozRequestFullScreen();
        } else if (document.documentElement.webkitRequestFullscreen) {
            document.documentElement.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT);
        }
    } else {
        if (document.cancelFullScreen) {
            document.cancelFullScreen();
        } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen();
        } else if (document.webkitCancelFullScreen) {
            document.webkitCancelFullScreen();
        }
    }
}



function setFile(initialPreviewConfig) {
    $("#projectForm").append('<input type="hidden" id="file_added" name="Projects[file][]" value="'+initialPreviewConfig.key+'">');
}
let messages = [];

function checkMessages() {
    let needReloadAllBlocks = false;
    let needReloadProjBlocks = false;
    jQuery.ajax({
        type: "GET",
        url: "/ctadmin/site/get-messages",
        dataType: "json",
        success: function(response) {
            // console.log(response);
            if (parseInt(response.tickets) > 0){
                reloadProjectMessagesListBlock();
                // console.log(typeof response.reload);
                // needReloadAllBlocks = true;
            }
            if (parseInt(response.reload) !== 0){
                // console.log(typeof response.reload);
                needReloadAllBlocks = true;
            }
            // console.log(needReloadAllBlocks);
            if (parseInt(response.count) > 0) {
                PNotify.desktop.permission();
                // needReloadAllBlocks = true;
                // console.log(messages.indexOf(message));
                response.data.forEach(function (message) {
                    // console.log(needReloadAllBlocks);
                    // console.log(messages.indexOf(message.id));
                    if (messages.indexOf(message.id) > (-1)) {
                        return;
                    }
                    $('.messages-badge').removeClass('invisible');
                    messages.push(message.id);
                    // $('.show-notification').append(message.notif);
                    // if (!message.is_notified) {
                    //     needReloadAllBlocks = true;
                        // (new PNotify({
                        //         title: message.title,
                        //         // type: message.type,
                        //         text: message.notif,
                        //         // hide: true,
                        //         desktop: {
                        //             desktop: true,
                        //             icon: 'assets/images/pnotify/success.png'
                        //         },
                        //         after_open: function(PNotify) {
                        //             // alert('I\'m called after the notice opens. I\'m passed the PNotify object for the current notice: ' + PNotify);
                        //         },
                        //     })
                        // ).get().click(function(e) {
                        // });

                    // }
                    // console.log(needReloadAllBlocks);
                });
                // new PNotify({
                //     title: response.data.title,
                //     addclass: 'bg-primary',
                //     text: 'Look at my beautiful <strong>strong</strong>, <a href="#" class="alert-link">linked</a>, <em>emphasized</em>, and <u>underlined</u> text with <i class="icon-make-group"></i> icon.',
                //     icon: 'icon-comment-discussion'
                // });
                if(needReloadProjBlocks && !needReloadAllBlocks) {
                    reloadProjectMessagesBlock();
                    reloadProjectOptionsBlock();
                    reloadProjectFilesBlock();
                }
             }
            // console.log(needReloadAllBlocks);
            if(needReloadAllBlocks) {
                reloadAllBlocks();
            }

        }
    });
}
function reloadAllBlocks() {
    reloadProjectMessagesBlock();
    reloadProjectOptionsBlock();
    reloadProjectFilesBlock();
    reloadProjectMessagesTopBlock();
    reloadProjectListBlock();
    initSwitchers();
}

function reloadProjectMessagesBlock() {
    if(document.getElementById("view_notes_pjax")) {
        $.pjax.reload({container: "#view_notes_pjax", async: false});
    }
}
function reloadNoteFormBlock() {
    if(document.getElementById("note_modal_pjax")) {
        $.pjax.reload({container: "#note_modal_pjax", async: false});
    }
}
function reloadProjectOptionsBlock() {
    if(document.getElementById("tuning_info_pjax")) {
        $.pjax.reload({container: "#tuning_info_pjax", async: false});
    }
}
function reloadProjectFilesBlock() {
    if(document.getElementById("view_files_pjax")) {
        $.pjax.reload({container: "#view_files_pjax", async: false});
    }
}
function reloadProjectMessagesTopBlock() {
    if(document.getElementById("message_main_top")) {
        $.pjax.reload({container: "#message_main_top", async: false});
    }
}
function reloadProjectMessagesListBlock() {
    if(document.getElementById("message_list_top")) {
        $.pjax.reload({container: "#message_list_top", async: false});
    }
}
function reloadProjectListBlock() {
    if(document.getElementById("crud-datatable-pjax")) {
        $.pjax.reload({container: "#crud-datatable-pjax", async: true});
    }
}
// let reloadedAlreadyHour = 0;
// let reloadedAlreadyMinute = 0;
function reloadMainBlock(h, m) {
    // if (reloadedAlreadyHour !== h && reloadedAlreadyMinute !== m) {
    //     reloadedAlreadyHour = h;
    //     reloadedAlreadyMinute = m;

    // }
}
function startTime()
{
    var tm=new Date();
    var t;
    var h=tm.getHours();
    var m=tm.getMinutes();
    var s=tm.getSeconds();
    m=checkTime(m);
    s=checkTime(s);
    if(document.getElementById("txt")) {
        document.getElementById('txt').innerHTML=h+":"+m+":"+s;
    }
    // let flagLoaded = m-1;
    // console.log(s);
    if (m === '00' && s === '00') {
        // console.log(m);
        // if (flagLoaded !== m){
            // flagLoaded = m;
        if(document.getElementById("main_pjax")) {
            $.pjax.reload({container: "#main_pjax", async: true});
        }
        // $('#myclock').empty();
        //
        // $('#myclock').thooClock({
        //     size: 200,
        // });

        // reloadMainBlock(h, m);

        // }
    }
    t = setTimeout('startTime()',500);
}
function checkTime(i)
{
    if (i<10)
    {
        i="0" + i;
    }
    return i;
}
function setLayoutLight()
{
    $('body').addClass('dark');
}

$(document).ready(function() {
    // проверяем свежие сообщения по всем проектам
    startTime();
    setInterval(checkMessages, 20000);
    $.mCustomScrollbar.defaults.axis = "yx";
    (function(){
        "use strict";

        var myPlayer = jQuery( "#bgndVideo" ).YTPlayer();

        var onMobile = false;

        if( /Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent) ) { onMobile = true; }

        if( ( onMobile === false ) ) {

            $(".player").mb_YTPlayer();

        } else {

            $("body").vegas({
                slides: [
                    { src: "/storage/web/assets/extra-pages/404/1/img/home-slide-1.jpg" },
                    { src: "/storage/web/assets/extra-pages/404/1/img/home-slide-2.jpg" },
                    { src: "/storage/web/assets/extra-pages/404/1/img/home-slide-3.jpg" }
                ],

                // Delay beetween slides in milliseconds.
                delay: 5000,
                transition: 'fade'

            });
        }
    })()

    $("#styleSelector .style-cont").slimScroll({
        setTop: "1px",
        height:"calc(100vh - 495px)",
    });

    $(".main-menu").mCustomScrollbar({
        setTop: "1px",
        setHeight: "calc(100% - 56px)",
    });

    // метод выбора стейджа при создании проекта
    $(document).on('click', '.stage_id', function (e) {
        var id = $(this).data("id");
        $("#projectForm").append('<input type="hidden" name="stage_id" value="'+id+'">');
        console.log(id);
        return true;
    });

    $(document).on('click', '.comment_yes', function (e) {
        var id = $(this).data("id");
        $(".text_"+id).removeClass('hidden').show(500);
        console.log(id);
        return true;
    });

    $(document).on('click', '.comment_no', function (e) {
        var id = $(this).data("id");
        $(".text_"+id).hide(500);
        console.log(id);
        return true;
    });

    // метод отметки всем сообщениям статуса прочитано
    $(document).on('click', '.read-all', function (e) {
        jQuery.ajax({
            type: "GET",
            url: "/ctadmin/site/read-all",
            dataType: "json",
            success: function(response) {
                if (response.success) {
                    reloadAllBlocks();
                }

            }
        });
        return true;
    });
    // $(document).on('click', '.project-options-change', function (e) {
    //     var id = $(this).data("id");
    //     var action = $(this).data("action");
    //     var val = $(this).val();
    //     var that = this;
    //     // if($(this).is(":checked")) {
    //     //     var returnVal = confirm("Are you sure?");
    //     //     $(this).attr("checked", returnVal);
    //     // }
    //     // if(!$(this).is(":checked")) {
    //     swal({
    //             title: "Are you sure?",
    //             text: "Your will not be able to recover this imaginary file!",
    //             type: "warning",
    //             showCancelButton: true,
    //             confirmButtonClass: "btn btn-danger",
    //             confirmButtonText: "Yes, delete it!",
    //             closeOnConfirm: false,
    //         },
    //         function(isConfirm) {
    //             if (isConfirm) {
    //                 // var data = {val:val, id:id};
    //                 // console.log(data);
    //                 $.ajax({
    //                     method: "POST",
    //                     url: "upd-addit/",
    //                     dataType: "json",
    //                     data: {addition_id:val, prOptionId:id, action:action},
    //                 })
    //                     .done(function( msg ) {
    //                         if (msg.status == 'success') {
    //                             console.log(that);
    //                            if (action == 'off') {
    //                                $('#span_'+id).addClass("text-danger").removeClass("text-primary");
    //                                $(that).data("action", "on");
    //                            } else {
    //                                $('#span_'+id).addClass("text-primary").removeClass("text-danger");
    //                                $(that).data("action", "off");
    //                            }
    //                             swal("Deleted!", "Your imaginary file has been deleted.", "success");
    //                             $(that).attr("checked", false);
    //                         }
    //                         // $('#additions_div').html(msg);
    //                         // initSwitchers();
    //                         // console.log();
    //                         // alert( "Data Saved: " + msg );
    //                     });
    //
    //             } else {
    //                 console.log(e.target);
    //                 $(e.target).prop("checked", true);
    //                 // $(this).attr("checked", true);
    //                 swal("Cancelled", "Your imaginary file is safe :)", "error");
    //                 // console.log($(this).attr("checked"));
    //                 // $(this).trigger("change");
    //                 return false;
    //             }
    //         });
    //     // }
    // });
    // метод закрытия проекта
    $(document).on('click', '#close_project', function (e) {
        var id = $(this).data("id");
        // var val = $(this).val();
        // var that = this;
        // var isChecked = $(this).is(":checked");
        // var action = isChecked ? 'on' : 'off';

        swal({
                title: "Are you sure?",
                icon: "warning",
                buttons: {
                    cancel: {
                        text: "No",
                        value: 0,
                        visible: true,
                        className: "btn btn-danger",
                        closeModal: true,
                    },
                    confirm: {
                        text: "Yes, close project!",
                        value: 1,
                        visible: true,
                        className: "btn btn-success",
                        closeModal: true
                    }
                }
            }).then(function(isConfirm) {
                if (isConfirm) {
                    preloaderCycle();
                    $.ajax({
                        method: "POST",
                        url: "/ctadmin/projects/close-project/",
                        dataType: "json",
                        data: {id:id},
                    })
                        .done(function( msg ) {
                            if (msg.status == 'success') {
                                    swal({
                                        title: "Closed!",
                                        icon: "success",
                                        timer: 1000
                                    });
                            } else {
                                swal({
                                    title: "Error!",
                                    content: "Try later.",
                                    icon: "danger",
                                    timer: 1000
                                });
                            }
                        });
                }
            });
    });
    $(document).on('click', '#del_project', function (e) {
        var id = $(this).data("id");
        // var val = $(this).val();
        // var that = this;
        // var isChecked = $(this).is(":checked");
        // var action = isChecked ? 'on' : 'off';

        swal({
            title: "Are you sure?",
            icon: "warning",
            buttons: {
                cancel: {
                    text: "No",
                    value: 0,
                    visible: true,
                    className: "btn btn-danger",
                    closeModal: true,
                },
                confirm: {
                    text: "Yes, delete project!",
                    value: 1,
                    visible: true,
                    className: "btn btn-success",
                    closeModal: true
                }
            }
        }).then(function(isConfirm) {
            if (isConfirm) {
                preloaderCycle();
                $.ajax({
                    method: "POST",
                    url: "/ctadmin/projects/delete-project/",
                    dataType: "json",
                    data: {id:id},
                })
                    .done(function( msg ) {
                        if (msg.status == 'success') {
                            swal({
                                title: "Deleted!",
                                icon: "success",
                                timer: 1000
                            });
                            window.location.href = '/ctadmin/projects';
                        } else {
                            swal({
                                title: "Error!",
                                content: "Try later.",
                                icon: "danger",
                                timer: 1000
                            });
                        }
                    });
            }
        });
    });

    // метод изменения опций тюнинга в проекте
    $(document).on('change', '.project-options-change', function (e) {
        var id = $(this).data("id");
        var val = $(this).val();
        var that = this;
        var isChecked = $(this).is(":checked");
        var action = isChecked ? 'on' : 'off';

        swal({
                title: "Are you sure?",
                text: "Your will be able to recover this option later!",
                type: "warning",
                showCancelButton: true,
                confirmButtonClass: "btn btn-danger",
                confirmButtonText: isChecked ? "Yes, enable it!" : "Yes, disable it!",
                closeOnConfirm: false,
            }).then(function(isConfirm) {
                if (isConfirm) {
                    $.ajax({
                        method: "POST",
                        url: "/ctadmin/projects/upd-addit/",
                        dataType: "json",
                        data: {addition_id:val, prOptionId:id, action:action},
                    })
                        .done(function( msg ) {
                            if (msg.status == 'success') {
                                if(isChecked) {
                                    // $('#span_' + id).addClass("text-primary").removeClass("text-danger");
                                    swal({
                                        title: "Enabled!",
                                        text: "Tuning option has been enabled.",
                                        type: "success",
                                        timer: 1000
                                    });
                                } else {
                                    // $('#span_'+id).addClass("text-danger").removeClass("text-primary");
                                    swal({
                                        title: "Disabled!",
                                        text: "Tuning option has been disabled.",
                                        type: "success",
                                        timer: 1000
                                    });
                                }
                                reloadProjectOptionsBlock();
                                reloadProjectMessagesBlock();
                            }
                        });

                } else {
                    // console.log(isChecked);
                    if(isChecked) {
                        $(e.target).prop("checked", false);
                    } else {
                        $(e.target).prop("checked", true);
                    }
                    // swal("Cancelled", "Your imaginary file is safe :)", "error");
                    return false;
                }
            });
    });

    // метод смены стейджа проекта
    $(document).on('change', '.project-stage-onoff', function (e) {
        var id = $(this).data("id");
        var val = $(this).val();
        var that = this;
        var isChecked = $(this).is(":checked");
        var action = isChecked ? 'on' : 'off';
        swal({
                title: "Are you sure?",
                text: "Your will be able to recover this option later!",
                type: "warning",
                showCancelButton: true,
                confirmButtonClass: "btn btn-danger",
                confirmButtonText: isChecked ? "Yes, enable it!" : "Yes, disable it!",
                closeOnConfirm: false,
            },
            function(isConfirm) {
                if (isConfirm) {
                    $.ajax({
                        method: "POST",
                        url: "/ctadmin/projects/upd-stage-project/",
                        dataType: "json",
                        data: {stage_id:val, prId:id, action:action},
                    })
                        .done(function( msg ) {
                            if (msg.status == 'success') {
                                if(isChecked) {
                                    swal({
                                        title: "Enabled!",
                                        text: "Tuning stage has been enabled.",
                                        type: "success",
                                        timer: 500
                                    });
                                } else {
                                    swal({
                                        title: "Disabled!",
                                        text: "Tuning stage has been disabled.",
                                        type: "success",
                                        timer: 500
                                    });
                                }
                                reloadProjectMessagesBlock();
                                reloadProjectOptionsBlock();
                            }
                        });
                } else {
                    // console.log(isChecked);
                    if(isChecked) {
                        $(e.target).prop("checked", false);
                    } else {
                        $(e.target).prop("checked", true);
                    }
                    // swal("Cancelled", "You cancel yours trying", "error");
                    return false;
                }
            });
    });

    // метод выбора чекбокса на странице настроек quick со сменой состояния
    $(document).on('change', '.kv-row-checkbox', function (e) {
        // var id = $(this).data("id");
        var val = $(this).val();
        var that = this;
        var isChecked = $(this).is(":checked");
        var action = isChecked ? 'on' : 'off';
        if (isChecked) {
            $('.ecus_div').append('<input type="hidden" value="'+val+'" class="ecu_id_'+val+'" name="ChipEcuAdditions[ecus][]">')
        } else {
            $('.ecu_id_'+val).remove();
        }
        console.log(val);
        console.log(action);
        return false;
    });

    // метод выбора всех чекбоксов со сменой состояния
    $(document).on('change', '.select-on-check-all', function (e) {
        $('.kv-row-checkbox').trigger('change');
        return false;
    });

    //--------
    // метод выбора активного стейджа на странице создания проекта
    $(document).on('change', '#stage_enabled', function (e) {
        var isChecked = $(this).is(":checked");
        if(isChecked) {
            var selected_stage =  $('.active.select_stage');
            console.log(selected_stage.data("id"));
            $('#stage_id').val(selected_stage.data("id"));
        } else {
            $('#stage_id').val(0);
        }
        // return false;
    });
    //--------

    // метод изменения возможности скачивания файла
    $(document).on('change', '.download-file-change', function (e) {
        var id = $(this).data("id");
        var that = this;
        var isChecked = $(this).is(":checked");
        var val = isChecked ? '1' : '0';

        swal({
                title: "Are you sure?",
                icon: "warning",
                content: "Your will be able to recover this option later!",
                buttons: {
                    cancel: {
                        text: "No",
                        value: 0,
                        visible: true,
                        className: "btn btn-danger",
                        closeModal: true,
                    },
                    confirm: {
                        text:  isChecked ? "Yes, enable it!" : "Yes, disable it!",
                        value: 1,
                        visible: true,
                        className: "btn btn-success",
                        closeModal: true
                    }
                }
            }).then(function(isConfirm) {
                if (isConfirm) {
                    $.ajax({
                        method: "POST",
                        url: "/ctadmin/projects/file-options/",
                        dataType: "json",
                        data: {option:'can_download', value:val, id:id},
                    })
                        .done(function( msg ) {
                            if (msg.status == 'success') {
                                if(isChecked) {
                                    swal({
                                        title: "Enabled!",
                                        content: "File can be downloaded.",
                                        icon: "success",
                                        timer: 1000
                                    });
                                } else {
                                    swal({
                                        title: "Disabled!",
                                        content: "File can not be downloaded.",
                                        icon: "success",
                                        timer: 1000
                                    });
                                }
                                reloadProjectMessagesBlock();
                            }
                        });

                } else {
                    if(isChecked) {
                        $(e.target).prop("checked", false);
                    } else {
                        $(e.target).prop("checked", true);
                    }
                    swal({
                        title: "Cancelled",
                        content: "Your imaginary file is safe.",
                        icon: "error",
                        timer: 500
                    });

                    // swal("Cancelled", "Your imaginary file is safe :)", "error");
                    // return false;
                }
            });
    });

    // метод изменения опций у проекта
    $(document).on('click', '.change_additions_btn', function (e) {
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
        var form = $('#change_additions')[0];
        var data = new FormData(form);
        $.ajax({
            method: "POST",
            url: "/ctadmin/projects/upd-project-params",
            processData: false,
            contentType: false,
            data: data,
        })
            .done(function( msg ) {
                if (msg.status == 'success') {
                    swal({
                        title: "Changed!",
                        text: "Tuning options has been changed.",
                        type: "success",
                        timer: 1500
                    });
                    reloadAllBlocks();
                    return true;
                } else {
                    swal({
                        title: "Error!",
                        text: "Tuning options has been not changed, try later or ask administrator about this problem.",
                        type: "error",
                        timer: 4500
                    });
                    return true;
                }
            });
        return true;
    });

    // метод изменения стейджей у проекта
    $(document).on('click', '.change_stage_btn', function (e) {
        var form = $('#change_stage')[0];

        var data = new FormData(form);
        $.ajax({
            method: "POST",
            url: "/ctadmin/projects/upd-stage/",
            processData: false,
            contentType: false,
            data: data,
        })
            .done(function( msg ) {
                if (msg.status == 'success') {
                    // reloadProjectMessagesBlock();
                    // reloadProjectOptionsBlock();
                    // swal("Changed!", "Stage has been changed.", "success");
                    swal({
                        title: "Changed!",
                        text: "Stage has been changed.",
                        type: "success",
                        timer: 500
                    });
                    reloadAllBlocks();
                    // location.reload();
                } else {
                    swal("Error", "Stage has been not changed, try later or ask administrator about this" +
                        " problem", "error");
                }
            });
        return false;
    });

    // метод удаления файла
    $(document).on('click', '.del-file', function (e) {
        var url = $(this).attr('href');
        console.log(url);
        swal({
                title: "Are you sure?",
                icon: "warning",
                content:{
                    element: 'div',
                    attributes: {
                        className: 'text-dark',
                        innerHTML: "Your will not be able to recover this option later!",
                    },
                },
                buttons: {
                    cancel: {
                        text: "No",
                        value: 0,
                        visible: true,
                        className: "btn btn-danger",
                        closeModal: true,
                    },
                    confirm: {
                        text: "Yes, delete it!",
                        value: 1,
                        visible: true,
                        className: "btn btn-success",
                        closeModal: true
                    }
                }
            }).then(function(isConfirm) {
            // function(isConfirm) {
                if (isConfirm) {
                    $.ajax({
                        method: "POST",
                        url: url,
                        dataType: "json",
                        // data: {stage_id:val, prId:id, action:action},
                    })
                        .done(function( msg ) {
                            if (msg.status == 'success') {
                                reloadProjectMessagesBlock();
                                reloadProjectFilesBlock();
                                swal({
                                    title: "Deleted!",
                                    content: "File has been deleted.",
                                    icon: "success",
                                    timer: 500
                                });
                            }
                        });
                } else {
                    return false;
                }
            });
        return false;
    });

    // метод удаления сообщения
    $(document).on('click', '.del-message', function (e) {
        var url = $(this).attr('href');
        console.log(url);
        swal({
                title: "Are you sure?",
                icon: "warning",
                content:{
                    element: 'div',
                    attributes: {
                        className: 'text-dark',
                        innerHTML: "Your will not be able to recover this option later!",
                    },
                },
                buttons: {
                    cancel: {
                        text: "No",
                        value: 0,
                        visible: true,
                        className: "btn btn-danger",
                        closeModal: true,
                    },
                    confirm: {
                        text: "Yes, delete it!",
                        value: 1,
                        visible: true,
                        className: "btn btn-success",
                        closeModal: true
                    }
                }
            }).then (function(isConfirm) {
                if (isConfirm) {
                    $.ajax({
                        method: "POST",
                        url: url,
                        dataType: "json",
                        // data: {stage_id:val, prId:id, action:action},
                    })
                        .done(function( msg ) {
                            if (msg.status == 'success') {
                                reloadProjectMessagesBlock();
                                swal({
                                    title: "Deleted!",
                                    content: "Message has been deleted.",
                                    type: "success",
                                    timer: 500
                                });
                            }
                        });
                } else {
                    return false;
                }
            });
        return false;
    });

    // let file_ver = 0;

    // метод вывода формы добавления файла к проекту
    // $(document).on('click', '#file_orig', function (e) {
    //     file_ver = 1;
    // });
    // $(document).on('click', '.files-head', function (e) {
    //     swal({
    //         title: "Uploaded!",
    //         icon: "success",
    //         content:{
    //             element: 'div',
    //             attributes: {
    //                 className: 'text-dark',
    //                 innerHTML: 'File has been uploaded. Do you want enable downloading this file?<br/><label><input type="radio" name="file_ver" value="2" class="file_ver" id="file_orig"/>Original file</label><br/><label class="text-dark"><input type="radio" name="file_ver" value="1" class="file_ver" id="file_mod" checked="checked"/>Modified file</label>',
    //             },
    //         },
    //         buttons: {
    //             cancel: {
    //                 text: "No",
    //                 value: 0,
    //                 visible: true,
    //                 className: "btn btn-danger",
    //                 closeModal: true,
    //             },
    //             confirm: {
    //                 text: "Yes",
    //                 value: 1,
    //                 visible: true,
    //                 className: "btn btn-success",
    //                 closeModal: true
    //             }
    //         },
    //     }).then(function(result) {
    //         let canDownload = result;
    //         let fileVer =  jQuery(".file_ver:checked").val();
    //         $.ajax({
    //             method: "POST",
    //             url: "/ctadmin/projects/file-options/",
    //             dataType: "json",
    //             data: {option:'can_download', value:canDownload, id:msg.id, fileVer:fileVer},
    //         })
    //             .done(function( msg_options ) {
    //                 reloadProjectFilesBlock();
    //                 reloadProjectMessagesBlock();
    //                 if (msg_options.status === 'success') {
    //                     swal({
    //                         title: "Enabled!",
    //                         content: "File can be downloaded.",
    //                         icon: "success",
    //                         timer: 1000
    //                     });
    //                 }
    //             });
    //         // }
    //     });
    // });

    // метод добавления МОД файла к проекту
    $(document).on('change', '.add_modified_file', function (e) {

        $('.encode_files').removeAttr('disabled');
        var data = new FormData();
        var $input = $(this);
        var id = $(this).data("id");
        var type = $(this).data("type");
        var original = $(this).data("original");



        data.append('id', id);
        data.append('type', type);
        data.append('orig_id', original);
        data.append('add_file', $input.prop('files')[0]);
        preloaderCycle();
        $.ajax({
            method: "POST",
            url: "/ctadmin/projects/add-slave-file/",
            processData: false,
            contentType: false,
            data: data,
        })
            .done(function( msg ) {
                if (msg.status === 'success') {
                    if (msg.showAccess === 1) {
                        swal({
                            title: "Uploaded!",
                            icon: "success",
                            content:{
                                element: 'div',
                                attributes: {
                                    className: 'text-dark',
                                    innerHTML:
                                        'Select version your file. <br/>' +
                                        '<label class="m-r-15"><input type="radio" name="file_ver" value="2" class="file_ver m-r-10" id="file_orig"/> Original file  </label>' +
                                        '<label class="text-dark"><input type="radio" name="file_ver" value="1" class="file_ver m-r-10" id="file_mod" checked="checked"/> Modified file</label>' +
                                        ' <br/>Do you want enable downloading this file? <br/>' +
                                        '<label class="m-r-15"><input type="radio" name="can_download" value="1" class="can_download m-r-10" id="can_download" checked="checked"/> Yes   </label>' +
                                        '<label class="text-dark"><input type="radio" name="can_download" value="0" class="can_download m-r-10" id="cant_download"/> No</label>' +
                                        '</br><label>Comment</label><textarea id="file_comment_text"></textarea>',
                                },
                            },
                            buttons: {
                                // cancel: {
                                //     text: "No",
                                //     value: 0,
                                //     visible: true,
                                //     className: "btn btn-danger",
                                //     closeModal: true,
                                // },
                                confirm: {
                                    text: "Ok",
                                    value: 1,
                                    visible: true,
                                    className: "btn btn-success",
                                    closeModal: true
                                }
                            },
                        }).then(function(result) {
                            // let canDownload = result;
                            let fileVer =  jQuery(".file_ver:checked").val();
                            let canDownload =  jQuery(".can_download:checked").val();
                            let comment =  jQuery("#file_comment_text").val();
                            $.ajax({
                                method: "POST",
                                url: "/ctadmin/projects/file-options/",
                                dataType: "json",
                                data: {option:'can_download', value:canDownload, id:msg.id, fileVer:fileVer, comment:comment},
                            })
                                .done(function( msg_options ) {
                                    reloadProjectFilesBlock();
                                    reloadProjectMessagesBlock();
                                    if (msg_options.status === 'success') {
                                        swal({
                                            title: "Enabled!",
                                            text: "File can be downloaded.",
                                            type: "success",
                                            timer: 1000
                                        });
                                    }
                                });
                            // }
                        });
                    } else {
                        reloadProjectFilesBlock();
                        reloadProjectMessagesBlock();
                        swal({
                            title: "Uploaded!",
                            content: "File has been uploaded.",
                            icon: "success",
                            timer: 500
                        });
                    }
                    // // && msg.showAccess == 1
                    //     swal({
                    //             title: "Uploaded!",
                    //             // text: "File has been uploaded. Do you want enable downloading this file?",
                    //             type: "success",
                    //             html: 'File has been uploaded. Do you want enable downloading this file?<br/><label><input type="radio" name="file_ver" value="orig" class="file_ver" id="file_orig"/>Original file</label><br/><label><input type="radio" name="file_ver" value="mod" class="file_ver" id="file_mod" checked="checked"/>Modified file</label>',
                    //             // input: 'radio',
                    //             // inputOptions: {
                    //             //     'orig' : 'Original file',
                    //             //     'mod' : 'Modified file',
                    //             // },
                    //             showCancelButton: true,
                    //             confirmButtonColor: '#3085d6',
                    //             cancelButtonColor: '#d33',
                    //             confirmButtonText: 'Yes',
                    //             cancelButtonText: 'No',
                    //             preConfirm: function () {
                    //                 return new Promise(function (resolve) {
                    //                     resolve([
                    //                         $( ".file_ver:checked" ).val()
                    //                     ])
                    //                 }).then(function (result) {
                    //                     file_ver = result;
                    //                 });
                    //             },
                    //             // timer: 500
                    //         },
                    //         function(isConfirm) {
                    //             let canDownload = 0;
                    //             // let file_orig = $('#file_orig').prop("checked");
                    //             // let file_mod = $('#file_mod').prop("checked");
                    //              console.log(file_ver);
                    //             // console.log(file_mod);
                    //              if (isConfirm) {
                    //                  canDownload = 1;
                    //              }
                    //                 $.ajax({
                    //                     method: "POST",
                    //                     url: "/ctadmin/projects/file-options/",
                    //                     dataType: "json",
                    //                     data: {option:'can_download', value:canDownload, id:msg.id, file_ver:file_ver},
                    //                 })
                    //                     .done(function( msg_options ) {
                    //                         reloadProjectFilesBlock();
                    //                         reloadProjectMessagesBlock();
                    //                         if (msg_options.status === 'success') {
                    //                             swal({
                    //                                 title: "Enabled!",
                    //                                 text: "File can be downloaded.",
                    //                                 type: "success",
                    //                                 timer: 1000
                    //                             });
                    //                         }
                    //                     });
                    //             // }
                    //         });
                    //     // });
                    // } else {
                    //     reloadProjectFilesBlock();
                    //     reloadProjectMessagesBlock();
                    //     swal({
                    //         title: "Uploaded!",
                    //         text: "File has been uploaded.",
                    //         type: "success",
                    //         timer: 500
                    //     });
                    // }
                } else {
                    swal({
                        title: "Error",
                        content: "File has not been uploaded, try later or ask administrator about this problem",
                        icon: "error"
                    });
                }
            });
        return false;
    });

    // метод кодирования мод файлов
    $(document).on('click', '.encode_files', function (e) {
        var id = $(this).data("id");
        swal({
            title: "Please select encode options",
            icon: "success",
            content:{
                element: 'div',
                attributes: {
                    className: 'text-dark',
                    innerHTML:
                        'Select version your file. <br/>' +
                        '<label class="m-r-15"><input type="radio" name="file_ver" value="2" class="file_ver m-r-10" id="file_orig"/> Original file  </label>' +
                        '<label class="text-dark"><input type="radio" name="file_ver" value="1" class="file_ver m-r-10" id="file_mod" checked="checked"/> Modified file</label>' +
                        ' <br/>Do you want enable downloading this file? <br/>' +
                        '<label class="m-r-15"><input type="radio" name="can_download" value="1" class="can_download m-r-10" id="can_download" checked="checked"/> Yes   </label>' +
                        '<label class="text-dark"><input type="radio" name="can_download" value="0" class="can_download m-r-10" id="cant_download"/> No</label>' +
                        '</br><label>Comment</label><textarea id="file_comment_text"></textarea>',
                },
            },
            buttons: {
                confirm: {
                    text: "Ok",
                    value: 1,
                    visible: true,
                    className: "btn btn-success",
                    closeModal: true
                }
            },
        }).then(function(result) {
            let fileVer =  jQuery(".file_ver:checked").val();
            let canDownload =  jQuery(".can_download:checked").val();
            let comment =  jQuery("#file_comment_text").val();
            $.ajax({
                method: "POST",
                url: "/ctadmin/projects/file-options-encode/",
                dataType: "json",
                data: {option:'can_download', value:canDownload, id:id, fileVer:fileVer, comment:comment},
            })
                .done(function( msg_options ) {
                    reloadProjectFilesBlock();
                    reloadProjectMessagesBlock();
                    if (msg_options.status === 'success') {
                        swal({
                            title: "Enabled!",
                            text: "File can be downloaded.",
                            type: "success",
                            timer: 500
                        });

                        var data = new FormData();
                        $(this).attr('disabled', 'disabled');

                        data.append('id', id);
                        preloaderCycle();
                        $.ajax({
                            method: "POST",
                            url: "/ctadmin/projects/encode-files/",
                            processData: false,
                            contentType: false,
                            data: data,
                        })
                            .done(function( msg ) {
                                if (msg.status === 'success') {
                                    swal({
                                        title: "Encoding... Please wait",
                                        content: "Encoding... Please wait",
                                        icon: "success",
                                        timer: 500
                                    });
                                    reloadProjectFilesBlock();
                                    reloadProjectMessagesBlock();
                                } else {
                                    var error = msg.message;
                                    swal({
                                        title: JSON.stringify(error),
                                        content: JSON.stringify(error),
                                        icon: "error"
                                    });
                                }
                            });
                    }
                });
            // }
        });






        return false;
    });

    $(document).on('click', '.upload_file', function (e) {
        $('#add_file').click();
    });
    // метод добавления файла к проекту
    $(document).on('change', '#add_file', function (e) {
        var data = new FormData();
        var $input = $("#add_file");
        var id = $(this).data("id");
        var type = $(this).data("type");

        data.append('id', id);
        data.append('type', type);
        data.append('add_file', $input.prop('files')[0]);
        preloaderCycle();
        $.ajax({
            method: "POST",
            url: "/ctadmin/projects/add-file/",
            processData: false,
            contentType: false,
            data: data,
        })
            .done(function( msg ) {
                if (msg.status === 'success') {
                    if (msg.showAccess === 1) {
                        swal({
                            title: "Uploaded!",
                            icon: "success",
                            content:{
                                element: 'div',
                                attributes: {
                                    className: 'text-dark',
                                    innerHTML:
                                        'Select version your file. <br/>' +
                                        '<label class="m-r-15"><input type="radio" name="file_ver" value="2" class="file_ver m-r-10" id="file_orig"/> Original file  </label>' +
                                        '<label class="text-dark"><input type="radio" name="file_ver" value="1" class="file_ver m-r-10" id="file_mod" checked="checked"/> Modified file</label>' +
                                        ' <br/>Do you want enable downloading this file? <br/>' +
                                        '<label class="m-r-15"><input type="radio" name="can_download" value="1" class="can_download m-r-10" id="can_download" checked="checked"/> Yes   </label>' +
                                        '<label class="text-dark"><input type="radio" name="can_download" value="0" class="can_download m-r-10" id="cant_download"/> No</label>' +
                                        '</br><label>Comment</label><textarea id="file_comment_text"></textarea>',
                                },
                            },
                            buttons: {
                                // cancel: {
                                //     text: "No",
                                //     value: 0,
                                //     visible: true,
                                //     className: "btn btn-danger",
                                //     closeModal: true,
                                // },
                                confirm: {
                                    text: "Ok",
                                    value: 1,
                                    visible: true,
                                    className: "btn btn-success",
                                    closeModal: true
                                }
                            },
                        }).then(function(result) {
                            // let canDownload = result;
                            let fileVer =  jQuery(".file_ver:checked").val();
                            let canDownload =  jQuery(".can_download:checked").val();
                            let comment =  jQuery("#file_comment_text").val();
                            $.ajax({
                                method: "POST",
                                url: "/ctadmin/projects/file-options/",
                                dataType: "json",
                                data: {option:'can_download', value:canDownload, id:msg.id, fileVer:fileVer, comment:comment},
                            })
                                .done(function( msg_options ) {
                                    reloadProjectFilesBlock();
                                    reloadProjectMessagesBlock();
                                    if (msg_options.status === 'success') {
                                        swal({
                                            title: "Enabled!",
                                            text: "File can be downloaded.",
                                            type: "success",
                                            timer: 1000
                                        });
                                    }
                                });
                            // }
                        });
                        } else {
                            reloadProjectFilesBlock();
                            reloadProjectMessagesBlock();
                            swal({
                                title: "Uploaded!",
                                content: "File has been uploaded.",
                                icon: "success",
                                timer: 500
                            });
                        }
                    // // && msg.showAccess == 1
                    //     swal({
                    //             title: "Uploaded!",
                    //             // text: "File has been uploaded. Do you want enable downloading this file?",
                    //             type: "success",
                    //             html: 'File has been uploaded. Do you want enable downloading this file?<br/><label><input type="radio" name="file_ver" value="orig" class="file_ver" id="file_orig"/>Original file</label><br/><label><input type="radio" name="file_ver" value="mod" class="file_ver" id="file_mod" checked="checked"/>Modified file</label>',
                    //             // input: 'radio',
                    //             // inputOptions: {
                    //             //     'orig' : 'Original file',
                    //             //     'mod' : 'Modified file',
                    //             // },
                    //             showCancelButton: true,
                    //             confirmButtonColor: '#3085d6',
                    //             cancelButtonColor: '#d33',
                    //             confirmButtonText: 'Yes',
                    //             cancelButtonText: 'No',
                    //             preConfirm: function () {
                    //                 return new Promise(function (resolve) {
                    //                     resolve([
                    //                         $( ".file_ver:checked" ).val()
                    //                     ])
                    //                 }).then(function (result) {
                    //                     file_ver = result;
                    //                 });
                    //             },
                    //             // timer: 500
                    //         },
                    //         function(isConfirm) {
                    //             let canDownload = 0;
                    //             // let file_orig = $('#file_orig').prop("checked");
                    //             // let file_mod = $('#file_mod').prop("checked");
                    //              console.log(file_ver);
                    //             // console.log(file_mod);
                    //              if (isConfirm) {
                    //                  canDownload = 1;
                    //              }
                    //                 $.ajax({
                    //                     method: "POST",
                    //                     url: "/ctadmin/projects/file-options/",
                    //                     dataType: "json",
                    //                     data: {option:'can_download', value:canDownload, id:msg.id, file_ver:file_ver},
                    //                 })
                    //                     .done(function( msg_options ) {
                    //                         reloadProjectFilesBlock();
                    //                         reloadProjectMessagesBlock();
                    //                         if (msg_options.status === 'success') {
                    //                             swal({
                    //                                 title: "Enabled!",
                    //                                 text: "File can be downloaded.",
                    //                                 type: "success",
                    //                                 timer: 1000
                    //                             });
                    //                         }
                    //                     });
                    //             // }
                    //         });
                    //     // });
                    // } else {
                    //     reloadProjectFilesBlock();
                    //     reloadProjectMessagesBlock();
                    //     swal({
                    //         title: "Uploaded!",
                    //         text: "File has been uploaded.",
                    //         type: "success",
                    //         timer: 500
                    //     });
                    // }
                } else {
                    swal({
                        title: "Error",
                        content: "File has not been uploaded, try later or ask administrator about this problem",
                        icon: "error"
                    });
                }
            });
        return false;
    });
    $(document).on('click', '.add_note_button', function (e) {
        $("#file_message_comment_id").val('');
        return true;
    });
    // метод добавления комментария к проекту
    $(document).on('click', '.add_note_submit', function (e) {
        var form = $('#add_note_form')[0];
        var data = new FormData(form);

        data.append('add_file', $('#note_attach').prop('files')[0]);
        $.ajax({
            method: "POST",
            url: "/ctadmin/projects/add-note/",
            processData: false,  // Important!
            contentType: false,
            data: data,
        })
            .done(function( msg ) {
                if (msg.status == 'success') {
                    reloadNoteFormBlock();
                    reloadProjectMessagesBlock();
                   // swal("Changed!", "Stage has been changed.", "success");
                   //  location.reload();
                } else {
                    swal("Error", "Stage has been not changed, try later or ask administrator about this" +
                        " problem", "error");
                }
            });
        return false;
    });

    // метод добавления запроса на сброс к заводским настройкам проекта
    $(document).on('click', '.reset_vehicle_submit', function (e) {
        var form = $('#reset_vehicle_form')[0];
        var data = new FormData(form);
        let file = $('#reset_attach').prop('files')[0];
        if (typeof file === "undefined"){
            alert('File is required!');
            return false;
        }
        // console.log(file);
        data.append('add_file', file);
        $.ajax({
            method: "POST",
            url: "/ctadmin/projects/reset-vehicle/",
            processData: false,  // Important!
            contentType: false,
            data: data,
        })
            .done(function( msg ) {
                if (msg.status == 'success') {
                    swal({
                        title: "Success!",
                        text: "Call to reset vehicle to standard settinsg sended.",
                        type: "success",
                        timer: 500
                    });
                    $('#reset-vehicle-Modal').modal('hide');
                    reloadAllBlocks();
                    // location.reload();
                } else {
                    swal("Error", "Something went wrong, reset vehicle call not sended", "error");
                }
            });
        return false;
    });

    // при получении аякс данных нужно отрисовать красивые чекбоксы
    initSwitchers();

    // $(document).on('change', '.js-switch', function (e) {
    //     if(this.checked) {
    //         $(this).popover('show');
    //     } else {
    //         $(this).popover('hide');
    //     }
    //     return true;
    // });
    $(document).on('change', '.stage_switch', function (e) {

        if(this.checked) {
            $('.stage_switch').each(function (i, el) {
                // console.log(el);
                if(el.checked){
                    el.click();
                }
            });
            this.click();
            //     $(this).attr('checked', true);
        }
        // return true;
    });

    // метод обновления списка сообщений при скачивании файла
    $(document).on('click', '.download_file', function (e) {
        setTimeout('reloadProjectMessagesBlock()', 4000);
    });
    // метод вывода формы добавления комментария к файлу
    $(document).on('click', '.add_file_comment', function (e) {
        var id = $(this).data("id");
        $("#file_comment_file_id").val(id);
        $.ajax({
            method: "GET",
            url: "/ctadmin/projects/get-file-comment/",
            dataType: "json",
            data: {id:id},
        })
            .done(function( msg ) {
                if (msg.status == 'success') {
                    $("#file_comment").val(msg.result);
                }
            });

    });

    // метод добавления комментария к файлу
    $(document).on('click', '.add_file_comment_submit', function (e) {
        var val = $("#file_comment").val();
        var id = $("#file_comment_file_id").val();

        if(val.length > 0) {
            $.ajax({
                method: "POST",
                url: "/ctadmin/projects/file-options/",
                dataType: "json",
                data: {option:'comment', value:val, id:id},
            })
            .done(function( msg ) {
                if (msg.status == 'success') {
                    swal({
                        title: "Enabled!",
                        text: "File comment added success.",
                        type: "success",
                        timer: 500,
                    });
                    reloadProjectMessagesBlock();
                    reloadProjectFilesBlock();
                    // location.reload(true);
                }
            });
        } else {
            alert ('Comment can not be empty!');
            return;
        }
    });

    $(document).on('click', '.edit-message', function (e) {
        var id = $(this).data("id");
        $("#file_message_comment_id").val(id);
        $.ajax({
            method: "GET",
            url: "/ctadmin/projects/get-message-comment/",
            dataType: "json",
            data: {id:id},
        })
            .done(function( msg ) {
                if (msg.status == 'success') {
                    $("#note_comment").val(msg.result);
                }
            });
    });

    $(document).on('change', '.additions_request', function (e) {
        let allText = '';
        let issetCheckedAdditions = false;
        // if(this.checked) {
        $('.additions_request').each(function (i, el) {
            // console.log(el);
            if(el.checked){
                allText += ', '+el.dataset.title;
                issetCheckedAdditions = true;
                // el.click();
            }
        });
        if (issetCheckedAdditions) {
            // $('#addit_request').prop('checked', true);
            changeSwitchery($('#addit_request'), true);
        } else {
            // $('#addit_request').prop('checked', false);
            changeSwitchery($('#addit_request'), false);
        }
        // this.click();
        //     $(this).attr('checked', true);
        // }
        // return true;
        $('#additions_request_text').val(allText);
    });

    // метод обновления списка сообщений при скачивании файла
    $(document).on('click', '#addit_request_label', function (e) {
        let issetCheckedAdditions = false;
        $('.additions_request').each(function (i, el) {
            if(el.checked){
                issetCheckedAdditions = true;
            }
        });
        // console.log(issetCheckedAdditions);
        if (issetCheckedAdditions) {
            // $('#addit_request').prop('checked', true);
            changeSwitchery($('#addit_request'), true);
        } else {
            // $('#addit_request').prop('checked', false);
            changeSwitchery($('#addit_request'), false);
        }

        // console.log($(this).prop('checked'));
        // return false;
    });

    $(document).on('change', '.additions_param_change_box', function (e) {
        let allText = '';
        // if(this.checked) {
        $('.additions_param_change_box').each(function (i, el) {
            // console.log(el);
            if(el.checked){
                allText += el.dataset.title+', ';
                // el.click();
            }
        });
        // this.click();
        //     $(this).attr('checked', true);
        // }
        // return true;
        $('#additions_request_text').val(allText);
    });

    $(document).on('change', '#custom_ecu', function (e) {
        // let allText = '';
         if(this.value.length > 0) {
            $('#opt_request_btn').removeClass('hidden');
         } else {
             $('#opt_request_btn').addClass('hidden');
         }
    });
    $(document).on('click', '.client_id', function (e) {
        // let allText = '';
        // if(this.value.length > 0) {
            $('.swal-button').removeClass('hidden');
        // } else {
        //     $('#opt_request_btn').addClass('hidden');
        // }
    });
    $(document).on('change', '#order_1c', function (e) {
        $("#client_name").val("");
        // let allText = '';
        if(this.value.length > 10) {
            // var id = $(this).data("id");
            // $("#file_message_comment_id").val(id);
                $.ajax({
                    method: "GET",
                    url: "/ctadmin/projects/get-client1c/?orderNum=" + this.value,
                    dataType: "json",
                    // data: {id:id},
                })
                .done(function( msg ) {
                    if (msg.elementsCount > 1) {
                        var list = '';
                        for (var i=0; i<msg.data.length; i++) {
                            list += '<label><input type="radio" class="client_id m-l-10" name="client_id" value="' + msg.data[i].id + '" data-name="' + msg.data[i].text + '">' + msg.data[i].text + '</label>';
                        }
                        swal({
                            title: "Select your client name!",
                            icon: "success",
                            closeOnClickOutside: false,
                            closeOnConfirm: false,
                            content:{
                                element: 'div',
                                attributes: {
                                    className: 'text-dark text-left',
                                    innerHTML: list,
                                },
                            },
                            buttons: {
                                confirm: {
                                    text: "OK",
                                    value: true,
                                    visible: true,
                                    className: "hidden",
                                    closeModal: true,
                                }
                            },

                        }).then(function(result) {
                            // console.log(result);
                            let client_id =  jQuery(".client_id:checked").val();
                            // console.log(client_id);
                            // // this.close();
                            // if( typeof client_id === 'undefined'){
                            //     alert('1');
                            //     return false;
                            // }
                            // let client_id =  jQuery(".client_id:checked").val();
                            let name =  jQuery(".client_id:checked").data("name");

                            $("#client_1c").val(client_id);
                            $("#client_name").val(name);
                        });
                    } else {
                        $("#client_1c").val(msg.data[0].id);
                        $("#client_name").val(msg.data[0].text);
                    }




                    // var sel = $("#client_name");
                    // sel.empty();
                    // if (!msg.error) {
                    //     for (var i=0; i<msg.length; i++) {
                    //         sel.append('<option value="' + msg[i].id + '">' + msg[i].text + '</option>');
                    //     }
                    //     // $("#client_1c").val(msg.id);
                    //     // $("#client_name").val(msg.text);
                    // } else {
                    //     sel.append('<option value="0">' + msg.text + '</option>');
                    // }
                });
        }
    });

});

function initSwitchers() {

    var elem = Array.prototype.slice.call(document.querySelectorAll('.js-switch'));
    // console.log(elem.length);
    elem.forEach(function(html) {
        // if ($(html).data('switchery')) {
        //     console.log(html.dataset);
        let data = JSON.parse(JSON.stringify(html.dataset));
        // console.log(data);
        // }
        if (!data.switchery) {
            var switchery = new Switchery(html, { color: '#2ECC71', jackColor: '#fff', secondaryColor:'#E74C3C' });
        }
    });

    var elem_success = Array.prototype.slice.call(document.querySelectorAll('.js-success'));
    // console.log(elem_success.length);
    elem_success.forEach(function(html) {
        var switchery = new Switchery(html, { color: '#2ECC71', jackColor: '#fff', secondaryColor:'#E74C3C' });
    });
    $('[data-toggle="tooltip"]').tooltip();

    $('[data-toggle="popover"]').popover({
        html: true,
        content: function() {
            return $('#primary-popover-content').html();
        }
    });

}
function addBrandToEcu(brandId) {
    $('#ecu_id').attr('data-brand_id', brandId);
}

function tryDonload(fileElem) {
    console.log();
    return false;
}

function getAdditions(elem) {
    // console.log($(elem).data("brand_id"));
    // return false;
    let ecuId = $(elem).val();
    let brandId = $(elem).data("brand_id");
    $.ajax({
        method: "GET",
        url: "/ctadmin/projects/get-addit/?id="+ecuId+"&brand_id="+brandId,
        //data: { name: "John", location: "Boston" }
    })
        .done(function( msg ) {
            $('#additions_div').html(msg);
            // initSwitchers();
            // console.log();
            // alert( "Data Saved: " + msg );
        });
}

function getStagesList(params) {
    console.log(params);
    // var json = JSON.parse(params);
    // console.log(json);
    $.ajax({
        method: "POST",
        url: "/ctadmin/chip-ecu-stages/get-stages/",
        data: params,
        // processData : false,
        // dataType: 'json',
    })
        .done(function( msg ) {
            $('#stages_div').html(msg);
            // initSwitchers();
            // console.log();
            // alert( "Data Saved: " + msg );
        });
}
// $(document).on('click', '#change_options', function (e) {
//     console.log('#change_options');
//     return false;
// });

function changeSwitchery(element, checked) {
    if ( ( element.is(':checked') && checked == false ) || ( !element.is(':checked') && checked == true ) ) {
        element.parent().find('.switchery').trigger('click');
    }
}
function startClock(hours) {
    $('#myclock').empty();
    return $('#myclock').thooClock({
        size: 200,
        timeCorrection: {                        // time correction object correction can be positive or negative
            operator: '+',                          // + or -
            hours: hours,                               // number of hours
            minutes: 0                            // number of minutes
        },
    });
};
$(document).on('pjax:success', function() {
    initSwitchers();
});

$(document).on('focus', '.select2.select2-container', function (e) {

    var isOriginalEvent = e.originalEvent // don't re-open on closing focus event
    var isSingleSelect = $(this).find(".select2-selection--single").length > 0 // multi-select will pass focus to input

    if (isOriginalEvent && isSingleSelect) {
        $(this).siblings('select:enabled').select2('open');
    }

});
$(document).on('select2:open', () => {
    document.querySelector('.select2-search__field').focus();
});
function getStages() {
    // console.log('getStages');
    // var params = {};
    // params.vehicle_id = $("#vehicle_id").val();
    // params.brand_id = $("#brand_id").val();
    //
    // params.model_id = $("#model_id").val();
    // // console.log(model_id);
    //
    // params.generation_id = $("#generation_id").val();
    // // console.log(generation_id);
    //
    // params.engine_id = $("#engine_id").val();
    // // console.log(engine_id);
    //
    // params.ecu_id = $("#ecu_id").val();
    // // console.log(ecu_id);
    // console.log(params);

    // getStagesList(params);
}