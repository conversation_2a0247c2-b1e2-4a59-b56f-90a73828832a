<?php
$config = [
    'homeUrl' => Yii::getA<PERSON><PERSON>('@backendUrl'),
    'controllerNamespace' => 'backend\controllers',
    'defaultRoute' => 'timeline-event/index',
    'components' => [
        'mailer' => [
            'class' => 'yii\swiftmailer\Mailer',
            'transport' => [
                'class' => 'Swift_SmtpTransport',
                'host' => 'smtp.msgroup.ua',
                'username' => '',
                'password' => '',
                'port' => '25',
            ],
        ],
        'errorHandler' => [
            'errorAction' => 'site/error',
        ],
        'request' => [
            'cookieValidationKey' => env('BACKEND_COOKIE_VALIDATION_KEY'),
            'baseUrl' => env('BACKEND_BASE_URL'),
        ],
        'urlManagerFrontend' => [
            'class' => 'yii\web\UrlManager',
            'baseUrl' => env('FRONTEND_BASE_URL'),
            'enablePrettyUrl' => true,
            'enableStrictParsing' => true,
            'showScriptName' => false,
            'rules' => [
                '' => 'site/index',
            ],
        ],
        'user' => [
            'class' => yii\web\User::class,
            'identityClass' => common\models\User::class,
            'loginUrl' => ['/sign-in/login'],
            'enableAutoLogin' => true,
            'as afterLogin' => common\behaviors\LoginTimestampBehavior::class,
        ],
        'balanceManager' => [
            'class' => 'yii2tech\balance\ManagerDb',
            'accountTable' => '{{%balance}}',
            'transactionTable' => '{{%balance_transactions}}',
            'accountLinkAttribute' => 'accountId',
            'amountAttribute' => 'value',
            'dataAttribute' => 'data',
        ],
        'telegram' => [
            'class' => 'aki\telegram\Telegram',
            'botToken' => '*********:AAEOsKMuWDOy9n-UdWMjmc2FsG16NDCfCyY',
        ],
    ],
    'modules' => [
        'ticket' => [
            'class' => backend\modules\ticket\Module::className(),
            'uploadFilesDirectory' => '@backend/web/fileTicket',
        ],
        'message' => [
            'class' => 'thyseus\message\Module',
            'userModelClass' => '\common\models\User', // your User model. Needs to be ActiveRecord.
        ],
        'content' => [
            'class' => backend\modules\content\Module::class,
            'layout' => '@backend/views/layouts/admin',
        ],
        'widget' => [
            'class' => backend\modules\widget\Module::class,
            'layout' => '@backend/views/layouts/admin',
        ],
        'file' => [
            'class' => backend\modules\file\Module::class,
            'layout' => '@backend/views/layouts/admin',
        ],
        'system' => [
            'class' => backend\modules\system\Module::class,
            'layout' => '@backend/views/layouts/admin',
        ],
        'translation' => [
            'class' => backend\modules\translation\Module::class,
            'layout' => '@backend/views/layouts/admin',
        ],
        'rbac' => [
            'class' => backend\modules\rbac\Module::class,
            'defaultRoute' => 'rbac-auth-item/index',
            'layout' => '@backend/views/layouts/admin',
        ],
        'gridview' =>  [
            'class' => '\kartik\grid\Module',
            'layout' => '@backend/views/layouts/admin',
        ],
    ],
    'as globalAccess' => [
        'class' => common\behaviors\GlobalAccessBehavior::class,
        'rules' => [
            [
                'controllers' => ['sign-in'],
                'allow' => true,
                'roles' => ['?'],
                'actions' => ['login'],
            ],
            [
                'controllers' => ['sign-in'],
                'allow' => true,
                'roles' => ['@'],
                'actions' => ['logout'],
            ],
            [
                'controllers' => ['site'],
                'allow' => true,
                'roles' => ['?', '@'],
                'actions' => ['error'],
            ],
            [
                'controllers' => ['debug/default'],
                'allow' => true,
                'roles' => ['?'],
            ],
            [
                'controllers' => ['user'],
                'allow' => true,
                'roles' => ['administrator', 'dealer'],
            ],
            [
                'controllers' => ['user'],
                'allow' => false,
            ],
            [
                'allow' => true,
                'roles' => ['manager', 'user', 'administrator'],
            ],
        ],
    ],
];

if (YII_ENV_DEV) {
$config['bootstrap'][] = 'gii';
$config['modules']['gii'] = [
    'class' => 'yii\gii\Module',
    // uncomment the following to add your IP if you are not connecting from localhost.
    'allowedIPs' => ['*', '::1'],
];
}

return $config;
