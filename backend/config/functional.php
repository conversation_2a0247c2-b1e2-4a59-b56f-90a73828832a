<?php
$config =  yii\helpers\ArrayHelper::merge(
    require(__DIR__ . '/main.php'),
    require(__DIR__ . '/main-local.php'),
    [
        'id' => 'app-tests',
        'components' => [
            'db' => [
                'dsn' => env('TEST_DB_DSN'),
				'username' => env('TEST_DB_USERNAME'),
				'password' => env('TEST_DB_PASSWORD'),
				'charset' => env('DB_CHARSET', 'utf8'),
				'enableSchemaCache' => YII_ENV_PROD,
            ]
        ]        
    ]
);
return $config;

?>