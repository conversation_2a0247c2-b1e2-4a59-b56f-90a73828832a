<?php

namespace backend\models;

use common\models\User;
use frontend\modules\user\Module;
use Yii;
use yii\base\Exception;
use yii\base\Model;
use yii\helpers\ArrayHelper;

/**
 * Create user form
 */
class UserForm extends Model
{
    public $username;
    public $email;
    public $password;
    public $status;
    public $tarif_id;
    public $roles;
    public $type;
    public $blocked;
    public $phone;
    public $guid_1c;
    public $data_1c;
    public $tools;

    public $company;
    public $address;
    public $zip;
    public $city;
    public $country;
    public $organisation;
    public $firstname;
    public $skype;

    private $model;

    const SCENARIO_LOGIN = 'login';
    const SCENARIO_CREATE = 'create';
    const SCENARIO_CREATE_PERSONAL = 'create_personal';
    const SCENARIO_CREATE_BUSINESS = 'create_business';
//    const SCENARIO_CREATE_DEALER = 'create_dealer';
    const SCENARIO_UPDATE_DEALER = 'update_dealer';
    const SCENARIO_UPDATE_ADMIN = 'ROLE_DEALER';

    /**
     * @return array
     */
    public function scenarios()
    {
        return ArrayHelper::merge(
            parent::scenarios(),
            [
                self::SCENARIO_LOGIN => ['username', 'password'],
                self::SCENARIO_CREATE => ['username', 'email', 'password'],
                self::SCENARIO_CREATE_PERSONAL => ['username', 'email', 'password', 'phone', 'type'],
                self::SCENARIO_UPDATE_DEALER => ['email', 'phone'],
                self::SCENARIO_UPDATE_ADMIN => ['email', 'phone', 'type', 'status', 'blocked', 'tools'],
                self::SCENARIO_CREATE_BUSINESS => ['username', 'email', 'phone','password','company', 'address', 'zip',
                    'city', 'country', 'organisation', 'firstname', 'skype', 'type'],
            ]
        );
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['username', 'email', 'password', 'phone'], 'required'],
            ['username', 'filter', 'filter' => 'trim'],
            ['username', 'required'],
            ['username', 'unique', 'targetClass' => User::class, 'filter' => function ($query) {
                if (!$this->getModel()->isNewRecord) {
                    $query->andWhere(['not', ['id' => $this->getModel()->id]]);
                }
            }],
            ['username', 'string', 'min' => 2, 'max' => 255],
            [['type'], 'default', 'value' => 1],
            [['type'], 'default', 'value' => 2, 'on' => self::SCENARIO_CREATE_BUSINESS],
            [['username', 'email', 'phone', 'password', 'company', 'address', 'zip',
                'city', 'country', 'organisation', 'firstname', 'skype'], 'required', 'on' => self::SCENARIO_CREATE_BUSINESS],

            ['email', 'filter', 'filter' => 'trim'],
            ['email', 'required'],
            ['email', 'email'],
            ['email', 'unique', 'targetClass' => User::class, 'filter' => function ($query) {
                if (!$this->getModel()->isNewRecord) {
                    $query->andWhere(['not', ['id' => $this->getModel()->id]]);
                }
            }],

            ['password', 'required', 'on' => 'create'],
            ['password', 'string', 'min' => 6],

            [['status', 'tarif_id'], 'integer'],
            [['roles'], 'each',
                'rule' => ['in', 'range' => ArrayHelper::getColumn(
                    Yii::$app->authManager->getRoles(),
                    'name'
                )]
            ],
            ['phone', 'unique', 'targetClass' => User::class, 'filter' => function ($query) {
                if (!$this->getModel()->isNewRecord) {
                    $query->andWhere(['not', ['id' => $this->getModel()->id]]);
                }
            }],
//            ['phone', 'unique'],
            [['type', 'blocked', 'guid_1c', 'data_1c', 'tools', 'company', 'address', 'zip', 'city', 'country', 'organisation', 'firstname', 'skype'], 'safe']
        ];
    }

    /**
     * @return User
     */
    public function getModel()
    {
        if (!$this->model) {
            $this->model = new User();
        }
        return $this->model;
    }

    /**
     * @param User $model
     * @return mixed
     */
    public function setModel($model)
    {

        $this->username = $model->username;
        $this->email = $model->email;
        $this->status = $model->status;
        $this->tarif_id = $model->tarif_id;
        $this->type = $model->type;
        $this->blocked = $model->blocked;
        $this->phone = $model->phone;
        $this->tools = !empty($model->tools) ? json_decode($model->tools) : '';

        $this->company = $model->company;
        $this->address = $model->address;
        $this->zip = $model->zip;
        $this->city = $model->city;
        $this->country = $model->country;
        $this->organisation = $model->organisation;
        $this->firstname = $model->firstname;
        $this->skype = $model->skype;
//        var_dump($this->attributes);
//        var_dump($model->errors);
//        die;

        $this->model = $model;
        $this->roles = ArrayHelper::getColumn(
            Yii::$app->authManager->getRolesByUser($model->getId()),
            'name'
        );
        return $this->model;
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'username' => Yii::t('common', 'Username'),
            'email' => Yii::t('common', 'Email'),
            'status' => Yii::t('common', 'Status'),
            'tarif_id' => Yii::t('common', 'Tarif'),
            'password' => Yii::t('common', 'Password'),
            'roles' => Yii::t('common', 'Roles')
        ];
    }

//    public function beforeValidate()
//    {
//        $data = Yii::$app->request->post('UserProfile');
//        var_dump($data);
//        die;
//        $this->phone = $data['phone'];
//        $this->type = $data['type'];
//        $this->load(Yii::$app->request->post('UserProfile'));
//        return parent::beforeValidate(); // TODO: Change the autogenerated stub
//    }

    /**
     * Signs user up.
     * @return User|null the saved model or null if saving fails
     * @throws Exception
     */
    public function signUp()
    {
        if ($this->validate()) {
            $model = self::getModel();
            $isNewRecord = $model->getIsNewRecord();
            $model->username = $this->username;
            $model->email = $this->email;
            $model->tarif_id = $this->tarif_id;
            $model->status = $this->status;
            $model->blocked = $this->blocked;
            $model->type = $this->type;
            $model->guid_1c = $this->guid_1c;
            $model->data_1c = $this->data_1c;
            $model->tools = !empty($this->tools) ? json_encode($this->tools) : '';

            $model->company = $this->company;
            $model->address = $this->address;
            $model->zip = $this->zip;
            $model->city = $this->city;
            $model->country = $this->country;
            $model->organisation = $this->organisation;
            $model->firstname = $this->firstname;
            $model->skype = $this->skype;
            $model->phone = $this->phone;
            if ($this->password) {
                $model->setPassword($this->password);
            }
//            var_dump($this->scenario);
//            var_dump($this->attributes);
//            die;
//            var_dump($model->validate());
//            var_dump($model->errors);
//            die;
            if ($model->validate()) {
                $model->save();
            } else {
                return $model;
            }
//            if (!$model->save()) {
//                throw new Exception('Model not saved');
//            }
            if ($isNewRecord) {
                $model->afterSignup();
            }
            $auth = Yii::$app->authManager;
//            $auth->revokeAll($model->getId());
//            $this->roles = [''];
//            $auth->assign($auth->getRole('user'), $model->getId());
//            $auth->assign($auth->getRole('dealer'), $model->getId());
            try {
            if ($this->scenario == self::SCENARIO_CREATE_PERSONAL) {
                $auth->assign($auth->getRole(User::ROLE_DEALER_PERSONAL), $model->getId());
            } elseif ($this->scenario == self::SCENARIO_CREATE_BUSINESS) {
                $auth->assign($auth->getRole(User::ROLE_DEALER_BUSINESS), $model->getId());
            } else {
                $auth->assign($auth->getRole(User::ROLE_USER), $model->getId());
            }
            } catch (\Exception $e) {
                var_dump($e);
            }
//            print_r($model->attributes);
//            die;
//                                    var_dump($this->validate());
//                                    var_dump($this->attributes);
//                                    var_dump($this->errors);
//                                    die;

//            if ($this->roles && is_array($this->roles)) {
//                foreach ($this->roles as $role) {
//                    $auth->assign($auth->getRole($role), $model->getId());
//                }
//            }

            return $model;
        }
        return null;
    }
    /**
     * Signs user up.
     * @return User|null the saved model or null if saving fails
     * @throws Exception
     */
    public function save()
    {
        if ($this->validate()) {
            $model = self::getModel();
            $isNewRecord = $model->getIsNewRecord();
            $model->username = $this->username;
            $model->email = $this->email;
            $model->tarif_id = $this->tarif_id;
            $model->status = $this->status;
            $model->blocked = $this->blocked;
            $model->type = $this->type;
            $model->guid_1c = $this->guid_1c;
            $model->data_1c = $this->data_1c;
            $model->tools = !empty($this->tools) ? json_encode($this->tools) : '';

            $model->company = $this->company;
            $model->address = $this->address;
            $model->zip = $this->zip;
            $model->city = $this->city;
            $model->country = $this->country;
            $model->organisation = $this->organisation;
            $model->firstname = $this->firstname;
            $model->skype = $this->skype;
            $model->phone = $this->phone;
            if ($this->password) {
                $model->setPassword($this->password);
            }
//            var_dump($this->tools);
//            var_dump($this->roles);
//            var_dump($model->attributes);
//            var_dump($model->errors);
//            die;
            if (!$model->save()) {
//                print_r($model->errors);
//                die;
                throw new Exception('Model not saved');
            }
            if ($isNewRecord) {
                $model->afterSignup();
            }
            $auth = Yii::$app->authManager;
            $auth->revokeAll($model->getId());
            if ($this->roles && is_array($this->roles)) {
                foreach ($this->roles as $role) {
                    $auth->assign($auth->getRole($role), $model->getId());
                }
            }
//            var_dump($this->roles);
//            var_dump($auth->getRolesByUser($model->getId()));
//            die;

            return $model;
        }
        return null;
    }

    public function shouldBeActivated()
    {
        /** @var Module $userModule */
        $userModule = Yii::$app->getModule('user');
        if (!$userModule) {
            return false;
        } elseif ($userModule->shouldBeActivated) {
            return true;
        } else {
            return false;
        }
    }

}
