<?php

namespace app\backend\models;
use Yii;

/**
 * This is the model class for table "balance_history".
 *
 * @property int $id
 * @property int $account_id
 * @property string $value
 * @property int $order_id
 * @property int $date
 * @property int $accountId
 * @property int $extraAccountId
 * @property int $amount
 * @property string $comment
 * @property string $data
 * @property string $created_at
 * @property string $updated_at
 * @property int $created_by
 * @property string $deleted_at
 * @property int $deleted_by
 */
class BalanceHistory extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'balance_history';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['account_id', 'value', 'order_id', 'date', 'accountId', 'extraAccountId', 'comment', 'data', 'created_by'], 'required'],
            [['account_id', 'order_id', 'date', 'accountId', 'extraAccountId', 'amount', 'created_by', 'deleted_by'], 'integer'],
            [['value'], 'number'],
            [['comment', 'data'], 'string'],
            [['created_at', 'updated_at', 'deleted_at'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'account_id' => Yii::t('app', 'Account ID'),
            'value' => Yii::t('app', 'Value'),
            'order_id' => Yii::t('app', 'Order ID'),
            'date' => Yii::t('app', 'Date'),
            'accountId' => Yii::t('app', 'Account ID'),
            'extraAccountId' => Yii::t('app', 'Extra Account ID'),
            'amount' => Yii::t('app', 'Amount'),
            'comment' => Yii::t('app', 'Comment'),
            'data' => Yii::t('app', 'Data'),
            'created_at' => Yii::t('app', 'Created At'),
            'updated_at' => Yii::t('app', 'Updated At'),
            'created_by' => Yii::t('app', 'Created By'),
            'deleted_at' => Yii::t('app', 'Deleted At'),
            'deleted_by' => Yii::t('app', 'Deleted By'),
        ];
    }

    /**
     * {@inheritdoc}
     * @return BalanceHistoryQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new BalanceHistoryQuery(get_called_class());
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getBalance()
    {
        return $this->hasOne(Balance::class, ['id' => 'balance_id']);
    }

}
