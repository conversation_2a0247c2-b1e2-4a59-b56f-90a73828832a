<style>
input[type="file"] {
    position: absolute;
    top: -500px;
    display: none;
}

/*div.file-listing{*/
/*  width: 200px;*/
/*}*/

span.remove-file {
    color: red;
    cursor: pointer;
    float: right;
    margin-left: 7px;
}
span.uploaded {
    margin: 0 auto;
    max-height: 45px;
    overflow: hidden;
}
</style>

<template>
    <div class="container">

        <div class="large-12 medium-12 small-12 cell">
            <label>
                <input type="file" id="file" ref="file" v-on:change="handleFileUpload()"/>
            </label>
        </div>

        <div class="large-12 medium-12 small-12 cell">
            <div class="file-listing">
                <span v-if="uploaded" class="uploaded">{{fileName}} Uploaded!</span>
                {{ file.name }}
                <span v-if="file" class="remove-file" v-on:click="removeFile()">
          <svg aria-hidden="true"
               style="display:inline-block;font-size:inherit;height:1em;overflow:visible;vertical-align:-.125em;width:.875em"
               xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
              <path fill="currentColor"
                    d="M32 464a48 48 0 0048 48h288a48 48 0 0048-48V128H32zm272-256a16 16 0 0132 0v224a16 16 0 01-32 0zm-96 0a16 16 0 0132 0v224a16 16 0 01-32 0zm-96 0a16 16 0 0132 0v224a16 16 0 01-32 0zM432 32H312l-9-19a24 24 0 00-22-13H167a24 24 0 00-22 13l-9 19H16A16 16 0 000 48v32a16 16 0 0016 16h416a16 16 0 0016-16V48a16 16 0 00-16-16z"></path>
          </svg>
      </span>
            </div>
        </div>

        <div class="large-12 medium-12 small-12 cell">
            <button class="btn btn-default" v-on:click="addFiles()">Add {{ type }} File <i class="fa fa-upload"></i>
            </button>
        </div>

        <div class="large-12 medium-12 small-12 cell" v-if="showSaveBtn">
            <button class="btn btn-success" v-on:click="submitFiles()">Upload {{ type }} <i class="fa fa-save"></i>
            </button>
        </div>
    </div>
</template>

<script>
export default {
    props: ["name", "type", "params", "additions", "ecu_id", "group_id", "stage_id"],
    data() {
        return {
            file: '',
            fileName: '',
            uploaded: false,
            // showSaveBtn: false
        }
    },
    computed: {
        showSaveBtn: function () {
            return this.file !== ''
        }
    },
    methods: {
        addFiles() {
            this.$refs.file.click();
        },
        submitFiles() {
            let formData = new FormData();
            formData.append('file', this.file);
            formData.append('type', this.type);
            axios.post('upload-file',
                formData,
                {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    }
                }
            ).then((resp) => {
                formData = null;
                this.fileName = this.file.name
                this.file = ''
                console.log('SUCCESS!!');
                if (resp.data.status === 'success') {
                    this.uploaded = true
                    this.$emit('uploaded', resp.data.file_id)
                }
            })
                .catch(function () {
                    console.log('FAILURE!!');
                });
        },

        handleFileUpload() {
            this.file = this.$refs.file.files[0];
            this.$emit('change', this.file)
        },

        removeFile() {
            this.uploaded = false
            this.fileName =''
            this.file = '';
        }
    }
}
</script>