<template>
  <div class="card">

    <div class="row card-block b-t-default p-t-10">
        <div class="edit-top-panel__select col-md-6">
            <div class="form-group">
                <label class="control-label">ECUs</label>
                <v-select-own :options="ecuList" label="title" :reduce="title => title.id" v-model="selectedEcu"></v-select-own>
            </div>
            <div class="form-group">
                <label class="control-label">Vehicle brands</label>
                <v-select-own :options="brandList" label="title" :reduce="title => title.id" multiple v-model="selectedBrands"></v-select-own>
            </div>
            <div class="form-group">
                <label class="control-label">Software number</label>
                <input type="text" v-model="softwareNumber"/>
            </div>
            <div class="form-group search_form">
                <div class="edit-top-panel__select col-md-6">
                    <FileInput name="search_file" type="search" ref="searchFileInput" @uploaded="uploadedSearchFile"></FileInput>
                </div>
                <div class="edit-top-panel__select col-md-6">
                    <input class="form-control btn-danger" type="button" value="Search mods by original script" @click="search"/>
                </div>
            </div>
        </div>
        <div class="edit-top-panel__select col-md-6">
            <div class="col-md-12 p-l-0 p-r-0">
                <div class="edit-top-panel__select col-md-6">
                    <FileInput name="orig_file" type="original" ref="origFileInput" @uploaded="uploadedOrigFile"></FileInput>
                </div>
                <div class="edit-top-panel__select col-md-6">
                    <FileInput name="mod_file" type="modified" ref="modFileInput" @uploaded="uploadedModFile"></FileInput>
                </div>
            </div>
            <div class="col-md-12 p-l-0 p-r-0">
                <div class="col-md-12 p-l-0 p-r-0">
                    <div class="col-md-4 p-l-0 p-r-0" v-for="(stage) in stages" :key="`stage-${stage.id}`">
                        <app-switch classes="is-warning" :type="'checkbox'" :name="'stages[]'" :checked="selectedStages.indexOf(stage.id) != -1" :value="stage.id" @click="selectStage(stage)">{{stage.title}}</app-switch>
                    </div>
                </div>
                <div class="col-md-12 p-l-0 p-r-0">
                    <div class="col-md-4 p-l-0 p-r-0" v-for="(addition) in additionList" :key="`addition-${addition.id}`">
                        <app-switch classes="is-warning" :type="'checkbox'" :name="'additions[]'" :checked="selectedAdditions.indexOf(addition.id) != -1" :value="addition.id" @click="selectAddition(addition)">{{addition.title}}</app-switch>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-group col-md-12">
          <div class="form-group col-md-6 p-l-0">
            <div class="form-group col-md-6 p-l-0">
              <input class="form-control btn-danger" type="button" value="View all scripts" @click="loadData"/>
            </div>
            <div class="form-group col-md-6 p-r-0">
              <input class="form-control btn-danger" type="button" value="Reset" @click="resetAll"/>
            </div>
          </div>
          <div class="form-group col-md-6 p-r-0">
            <div class="form-group col-md-6 col-md-offset-3">
              <input class="form-control btn-success" type="button" value="Save" @click="saveSettings"/>
            </div>
          </div>
        </div>
        <div class="form-group col-md-12" v-if="configList.length > 1">
          <div class="col-md-12 p-l-0 p-r-0">
            <div class="file-listing-header">
              <input type="checkbox" v-model="addAllToDelList"/>
              <span class="remove-config" v-on:click="removeAllCheckedConfigs()">
                            <svg aria-hidden="true" style="display:inline-block;font-size:inherit;height:1em;overflow:visible;vertical-align:-.125em;width:.875em" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M32 464a48 48 0 0048 48h288a48 48 0 0048-48V128H32zm272-256a16 16 0 0132 0v224a16 16 0 01-32 0zm-96 0a16 16 0 0132 0v224a16 16 0 01-32 0zm-96 0a16 16 0 0132 0v224a16 16 0 01-32 0zM432 32H312l-9-19a24 24 0 00-22-13H167a24 24 0 00-22 13l-9 19H16A16 16 0 000 48v32a16 16 0 0016 16h416a16 16 0 0016-16V48a16 16 0 00-16-16z"></path></svg>
                        </span>
            </div>
          </div>
        </div>
      <div class="form-group col-md-12">
            <div class="col-md-12 p-l-0 p-r-0" v-for="(configItem) in configList" :key="`config-${configItem.id}`">
                    <div class="file-listing">
                        <span class="listing-title">{{ configItem.script_path }}</span>
                        <input class="form-control btn-success test-button" type="button" v-if="showTestBtn" value="Test mod" @click="createTestFile(configItem.id)"/>
                        <input class="" type="checkbox" :id="configItem.id" :value="configItem.id" v-model="delList"/>
                        <span class="created_date">{{ configItem.created_at }}</span>
                        <span class="remove-config" v-on:click="removeConfig(configItem.id)">
                            <svg aria-hidden="true" style="display:inline-block;font-size:inherit;height:1em;overflow:visible;vertical-align:-.125em;width:.875em" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M32 464a48 48 0 0048 48h288a48 48 0 0048-48V128H32zm272-256a16 16 0 0132 0v224a16 16 0 01-32 0zm-96 0a16 16 0 0132 0v224a16 16 0 01-32 0zm-96 0a16 16 0 0132 0v224a16 16 0 01-32 0zM432 32H312l-9-19a24 24 0 00-22-13H167a24 24 0 00-22 13l-9 19H16A16 16 0 000 48v32a16 16 0 0016 16h416a16 16 0 0016-16V48a16 16 0 00-16-16z"></path></svg>
                        </span>
                    </div>
            </div>
        </div>

    </div>
  </div>
</template>

<script>
  import VJstree from 'vue-jstree'
  import Switch from './components/Switch'
  import FileInput from "../autopack-admin/components/FileInput.vue";
  // Bosch EDC16U34
  export default {
      components: {
          'app-switch': Switch,
          FileInput
      },
  props: [],
  name: "Main",
  data: function () {
      return {
          brandList: [],
          selectedBrands: [],
          selectedAdditions: [],
          ecuList: [],
          selectedEcu: 0,
          selectedStages: [],
          softwareNumber: "",
          configList: [],
          originalFile: null,
          modifiedFile: null,
          searchFile: null,
          componentKey: 0,
          additionList: [],
          stages: [],
          searchConfigsPresent: false,
          addAllToDelList: false,
          delList: [],
      }
  },
  computed: {
      // showLoadBtn: function () {
      //     return this.selectedEcu !== 0
      // },
      // showSaveBtn: function () {
      //     return this.selectedEcu !== 0 && this.selectedBrands.length > 0 && this.softwareNumber.length > 0 && (this.selectedAdditions.length > 0 || this.selectedStages.length > 0)
      // },
      showTestBtn: function () {
          return this.configList.length !== 0 && this.searchFile !== null && this.searchConfigsPresent === true
      }
  },
    watch: {
        selectedEcu: {
            handler: function (val) {
                this.selectedBrands=[]
                this.getBrands()
                // console.log(val);
            }
        },
      addAllToDelList: {
            handler: function (val) {
              if (val) {
                this.delList = this.configList.map(el => el.id);
              } else {
                this.delList = [];
              }
            }
        },
    },
    methods: {
        reset()
        {
            // this.brandList= [];
            // this.selectedBrands= [];
            this.selectedAdditions= [];
            // this.configList= [];
            // this.selectedEcu= null;
            this.selectedStages= [];
            this.originalFile= null;
            this.modifiedFile= null;
            // this.softwareNumber= "";
            var origFileInputRef = this.$refs.origFileInput;
            origFileInputRef.removeFile();
            var modFileInputRef = this.$refs.modFileInput;
            modFileInputRef.removeFile();
        },
      async removeAllCheckedConfigs()
        {
          if (this.delList < 1){
            return false;
          }
          if (!confirm('Are you sure?')){
            return false;
          }
          await axios({
            method:'post',
            url:'delete-configs',
            data: JSON.stringify({
              "configs": this.delList,
            })
          }).then((response) => {
            if (response.data !== 0) {
              this.search()
            } else {
              alert('error delete config items');
            }
          }).catch(function (e) {
            console.log(e.message)
          })
        },
        resetAll()
        {
            this.brandList= [];
            this.selectedBrands= [];
            this.selectedAdditions= [];
            this.configList= [];
            this.selectedEcu= null;
            this.selectedStages= [];
            this.originalFile= null;
            this.modifiedFile= null;
            this.softwareNumber= "";
            var origFileInputRef = this.$refs.origFileInput;
            origFileInputRef.removeFile();
            var modFileInputRef = this.$refs.modFileInput;
            modFileInputRef.removeFile();
        },
        async search()
        {
            if (this.searchFile == null) {
                alert('searchFile');
                return false;
            }

            await axios({
                method:'post',
                url:'search-config-list',
                data: JSON.stringify({
                    "ecu_id": this.selectedEcu,
                    "stages": this.selectedStages,
                    "brands": this.selectedBrands,
                    "additions": this.selectedAdditions,
                    "software_number": this.softwareNumber,
                    "search_file": this.searchFile,
                })
            }).then((response) => {
                this.loadConfigData(response.data)
                this.searchConfigsPresent = true
            }).catch(function (e) {
                console.log(e.message)
            })
        },
        loadConfigData(configData)
        {
            this.configList = configData
        },
        selectAddition(item)
        {
            this.selectedAdditions.indexOf(item.id) != -1 ? this.selectedAdditions.splice(this.selectedAdditions.indexOf(item.id), 1) : this.selectedAdditions.push(item.id)
        },
        selectStage(item)
        {
            this.selectedStages.indexOf(item.id) != -1 ? this.selectedStages.splice(this.selectedStages.indexOf(item.id), 1) : this.selectedStages.push(item.id)
        },
        uploadedOrigFile(fileData)
        {
            this.originalFile = fileData
        },

        uploadedModFile(fileData)
        {
            this.modifiedFile = fileData
        },
        uploadedSearchFile(fileData)
        {
            this.searchFile = fileData
        },
        validate()
        {

            if (typeof this.selectedEcu == null) {
                alert('selectedEcu');
                return false;
            }
            // if (!this.selectedStages.length) {
            //     alert('selectedStages');
            //     return false;
            // }

            if (!this.selectedBrands.length) {
                alert('selectedBrands');
                return false;
            }

            // if (!this.selectedAdditions.length) {
            //     alert('selectedAdditions');
            //     return false;
            // }

            if (this.originalFile == null) {
                alert('originalFile');
                return false;
            }

            if (this.modifiedFile == null) {
                alert('modifiedFile');
                return false;
            }

            if (!this.softwareNumber.length) {
                alert('softwareNumber');
                return false;
            }
            return true;
        },
        async saveSettings () {
            if (!this.validate()){
                return false;
            }
            await axios({
                method:'post',
                url:'save-config',
                data: JSON.stringify({
                    "ecu_id": this.selectedEcu,
                    "stages": this.selectedStages,
                    "brands": this.selectedBrands,
                    "additions": this.selectedAdditions,
                    "orig_file": this.originalFile,
                    "mod_file": this.modifiedFile,
                    "software_number": this.softwareNumber
                })
            }).then((response) => {
                this.reset()
                this.configList = response.data
            }).catch(function (e) {
                console.log(e.message)
            })
        },

        async removeConfig (configId) {
            if (!confirm('Are you sure?')){
                return false;
            }
            await axios({
                method:'post',
                url:'delete-config',
                data: JSON.stringify({
                    "config_id": configId,
                })
            }).then((response) => {
                if (response.data !== 0) {
                    this.search()
                } else {
                  alert('error delete config item');
                }
            }).catch(function (e) {
                console.log(e.message)
            })
        },
        async createTestFile (configId) {
            if (!confirm('Are you sure?')){
                return false;
            }
            await axios({
                method:'post',
                url:'test-mod',
                data: JSON.stringify({
                    "config_id": configId,
                    "search_file": this.searchFile,
                })
            }).then((response) => {
                if (response.data !== null) {
                    window.open(response.data, '_blank');
                } else {
                    console.log('error delete config item');
                }
            }).catch(function (e) {
                console.log(e.message)
            })
        },
    selectEcu(item)
    {
      let data = {
        id: item.value,
        title: item.text,
        values: {}
      }
      // this.dates.forEach(item => {
      //   data.values[item.date] = {}
      //   data.values[item.date].date = item.date
      //   data.values[item.date].plan = 0
      //   data.values[item.date].fact = 0
      //   data.values[item.date].deviation = 0
      // })
      console.log(data)
      // this.$emit('selected', data)
      // this.hide()
    },
        async getAdditions(){
            await axios.get('addition-list')
                .then((response) => {
                    this.additionList = response.data.content
                }).catch(function (e) {
                    console.log(e.message)
                })
        },

     async getEcus(){
         await axios.get('ecu-list')
                  .then((response) => {
                    this.ecuList = response.data.content
                  })
                  .catch(function (e) {
                   console.log(e.message)
                  })
      },
     async getBrands(){
         await axios.get('brand-list?ecu_id=' + this.selectedEcu)
                  .then((response) => {
                    this.brandList = response.data.content
                  })
                  .catch(function (e) {
                   console.log(e.message)
                  })
      },

      async getStages(){
        await axios.get('stage-list')
                  .then((response) => {
                    this.stages = response.data.content
                  })
                .catch(function (e) {
                  console.log(e.message)
                })
      },

      async loadData(){
        await axios({
            method:'post',
            url:'config-list',
            data: JSON.stringify({
                "ecu_id": this.selectedEcu,
                "stages": this.selectedStages,
                "brands": this.selectedBrands,
                "additions": this.selectedAdditions,
                "software_number": this.softwareNumber
            })
        }).then((response) => {

            this.loadConfigData(response.data)
        }).catch(function (e) {
            console.log(e.message)
        })
      },

  },
  created() {
    this.getEcus()
      this.getBrands()
    this.getStages()
      this.getAdditions();

      // this.getState({'id' : this.id, 'pm' : this.pm}).then(() => {
    //   //this.$refs.Table.loadedState()
    // })
  }
}
</script>

<style>
    .search_form .cell {
        margin:10px;
    }
    .search_form {
        border: 1px solid;
        display: flex;
        width: 100%;
        align-items: center;
    }
    .listing-title {
        width:70%;
    }
    .test-button {
        width:110px;
    }
    .created_date {
        color:#94e889;
    }
  .pcoded .pcoded-navbar {
    z-index: 600 !important;
  }
  .tree-selected{
    color:#8bc4ea !important;
  }
  .standardTreeClass{
    background-color:transparent !important;
  }
  .changedTreeClass{
    background-color:transparent !important;
  }
  .tree-hovered{
    background-color:#2e8b57 !important;
  }
  .tree-selected{
    background-color:#2e8b57 !important;
    color:#0a0a0a!important;
  }
  .left_column div {
    min-height: 45px;
  }
  .vs__selected{
    color:#89898c!important;
  }
  .vs__actions svg {
    fill:#ffffee!important;
  }
  .remove-config {
      float: right;
      color: red;
      cursor: pointer;
  }
  .file-listing {
      padding: 5px;
      font-size: 115%;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
  }
  .file-listing-header {
    width: 248px;
    float: right;
  }
</style>