<template>
  <div class="card">

    <div class="row card-block b-t-default p-t-10">
      <div class="edit-top-panel__select col-md-6">
        <v-jstree :key="componentKey" :data="groupsTree" allow-transition allow-batch @item-click="itemClick"></v-jstree>
      </div>
      <div class="edit-top-panel__select col-md-6">
        <span style="font-size: 16px;font-weight:600;">Create {{(editGroup.parent_id > 0 && selectedGroup !== null) ? ' subgroup for '+ selectedGroup.text : 'group' }} :</span>
        <div class="form-group">
          <label class="control-label">Group title</label>
          <input class="form-control" type="text" v-model="editGroup.title"/>
        </div>
        <div class="form-group">
          <label class="control-label">Vehicle brands</label>
          <v-select-own :options="brandList" label="title" :reduce="title => title.id" multiple v-model="selectedBrands"></v-select-own>
        </div>
        <div class="form-group">
          <input class="form-control" type="button" value="Save" @click="saveGroup"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import VJstree from 'vue-jstree'

  export default {
  components: {
    VJstree
  },
  props: [],
  name: "Main",
  data: function () {
    return {
      brandList: [],
      selectedBrands: [],
      groupsTree: [],
      editGroup:{
        id: 0,
        title: '',
        parent_id: 0,
        chip_brands: [],
      },
      selectedGroup: null,
      componentKey: 0,
    }
  },
  computed: {
  },
    watch: {
      selectedBrands: {
        handler: function (val) {
          this.editGroup.chip_brands = val
        }
      }
    },
    methods: {
      itemClick (node) {
        this.componentKey += 1;
        if (node.model.selected) {
          if (this.selectedGroup != null &&  node.model.id == this.selectedGroup.id && node.model.type == this.selectedGroup.type) {
            node.model.selected = false
            this.selectedGroup = null
            this.editGroup.parent_id = 0
          } else {
            this.selectedGroup = node.model
            this.editGroup.parent_id = node.model.id
          }
        } else {
          this.selectedGroup = null
        }
        // console.log(node.model)
      },
      async saveGroup () {
        await axios({
          method:'post',
          url:'save-group',
          data: JSON.stringify(this.editGroup)
        }).then((response) => {
          this.loadTree()
        }).catch(function (e) {
                  console.log(e.message)
                })
      },
    selectEcu(item)
    {
      let data = {
        id: item.value,
        title: item.text,
        values: {}
      }
      // this.dates.forEach(item => {
      //   data.values[item.date] = {}
      //   data.values[item.date].date = item.date
      //   data.values[item.date].plan = 0
      //   data.values[item.date].fact = 0
      //   data.values[item.date].deviation = 0
      // })
      console.log(data)
      // this.$emit('selected', data)
      // this.hide()
    },

     async getItems(){
         await axios.get('brand-list')
                  .then((response) => {
                    this.brandList = response.data.content
                  })
                  .catch(function (e) {
                   console.log(e.message)
                  })
      },

      async getGroups(){
        await axios.get('group-tree?only_groups=1')
                  .then((response) => {
                    this.groupsTree = response.data.content
                  })
                .catch(function (e) {
                  console.log(e.message)
                })
      },

      async loadTree(){
        await axios.get('group-tree?only_groups=1')
                  .then((response) => {
                    this.groupsTree = response.data.content
                  })
                .catch(function (e) {
                  console.log(e.message)
                })
      },

  },
  created() {
    this.getItems()
    this.getGroups()
    // this.getState({'id' : this.id, 'pm' : this.pm}).then(() => {
    //   //this.$refs.Table.loadedState()
    // })
  }
}
</script>

<style>
  .pcoded .pcoded-navbar {
    z-index: 600 !important;
  }
  .tree-selected{
    color:#8bc4ea !important;
  }
  .standardTreeClass{
    background-color:transparent !important;
  }
  .changedTreeClass{
    background-color:transparent !important;
  }
  .tree-hovered{
    background-color:#2e8b57 !important;
  }
  .tree-selected{
    background-color:#2e8b57 !important;
    color:#0a0a0a!important;
  }
  .left_column div {
    min-height: 45px;
  }
  .vs__selected{
    color:#89898c!important;
  }
  .vs__actions svg{
    fill:#ffffee!important;
  }
</style>
