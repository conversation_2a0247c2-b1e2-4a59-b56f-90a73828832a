<?php

namespace backend\widgets;


use thrieu\grid\FilterStateInterface;
use thrieu\grid\FilterStateTrait;
use Yii;
use yii\bootstrap4\Button;
use yii\helpers\Html;

class GridView extends \kartik\grid\GridView implements FilterStateInterface {
    use FilterStateTrait;

    public $clearFilter = true;
    public $form;
    public $formAction = 'index';


    private function getForm($action)
    {
        $form = Html::beginForm($action);
        $form .= Html::hiddenInput('clear-state', '1');
        $form .= Html::hiddenInput('redirect-to', '');
        $form .= Button::widget([
            'label' => Yii::t('app', 'Очистить фильтр'),
            'options' => [
                'class' => 'btn btn-warning'
            ]
        ]);
        $form .= Html::endForm();

        return $form;
    }

    protected function renderToolbar()
    {
        $this->form = $this->getForm($this->formAction);
        $toolbar = '';
        if ($this->clearFilter) {
            $toolbar = $this->form;
        }
        $toolbar .= parent::renderToolbar();
        return $toolbar;
    }

}
