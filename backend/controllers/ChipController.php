<?php

namespace backend\controllers;

use common\models\ChipBrand;
use common\models\ChipEcu;
use common\models\ChipEngine;
use common\models\ChipGeneration;
use common\models\ChipModel;
use common\models\ChipStages;
use common\models\ChipStagesEcu;
use common\models\ProjectMessages;
use Yii;
use zxbodya\yii2\elfinder\ConnectorAction;
use zxbodya\yii2\tinymce\TinyMceCompressorAction;

/**
 * Site controller
 */
class ChipController extends \yii\web\Controller
{

    /**
     * @inheritdoc
     */
    public $layout = '/admin';
    public $contentClass = '';
    public $enableCsrfValidation = false;

    public function behaviors()
    {
        return [
            'clearFilterState' => \thrieu\grid\ClearFilterStateBehavior::className(),
        ];
    }

    public function actions()
    {
        return [
            'tinyMceCompressor' => [
                'class' => TinyMceCompressorAction::className(),
            ],
            'connector' => array(
                'class' => ConnectorAction::className(),
                'settings' => array(
                    'root' => Yii::getAlias('@webroot') . '/uploads/',
                    'URL' => Yii::getAlias('@web') . '/uploads/',
                    'rootAlias' => 'Home',
                    'mimeDetect' => 'none'
                )
            ),
        ];
    }

    /**
     * @param $action
     * @return bool
     * @throws \yii\web\BadRequestHttpException
     */
    public function beforeAction($action)
    {
        if (!Yii::$app->user->isGuest) {
            $this->view->params['contentClass'] = $this->contentClass;
        }
        return parent::beforeAction($action);
    }
}