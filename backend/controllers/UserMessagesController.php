<?php

namespace backend\controllers;

use backend\controllers\ChipController;
use common\helpers\MessageHelper;
use common\helpers\UserHelper;
use common\models\UserMessagesFiles;
use common\models\UserMessagesUsers;
use Yii;
use common\models\UserMessages;
use common\models\search\UserMessagesSearch;
use yii\data\ActiveDataProvider;
use yii\helpers\ArrayHelper;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use \yii\web\Response;
use yii\helpers\Html;

/**
 * UserMessagesController implements the CRUD actions for UserMessages model.
 */
class UserMessagesController extends ChipController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['post'],
                    'bulk-delete' => ['post'],
                ],
            ],
        ];
    }

    /**
     * Lists all UserMessages models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new UserMessagesSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $dataProvider->setSort(['defaultOrder' => ['id'=>SORT_DESC]]);
        return $this->render('list', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Lists all UserMessages models.
     * @return mixed
     */
    public function actionList()
    {
        $searchModel = UserMessagesUsers::find()->where(['user_id' => Yii::$app->user->identity->id]);
        $dataProvider = new ActiveDataProvider([
            'query' => $searchModel,
        ]);
//        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $dataProvider->setSort(['defaultOrder' => ['id'=>SORT_DESC]]);
        return $this->render('list-client', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single UserMessages model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        $request = Yii::$app->request;
            $model = $this->findModel($id);

            $userMessageUser = UserMessagesUsers::find()->where(['user_id' => Yii::$app->user->identity->id, 'message_id' => $id])->one();
            $userMessageUser->setAttribute('readed', 1);
            $userMessageUser->save(false);

            return $this->render('read-client', [
                'model' => $model,
            ]);
    }

    /**
     * Displays a single UserMessages model.
     * @param integer $id
     * @return mixed
     */
    public function actionRead($id)
    {
        $request = Yii::$app->request;
            return $this->render('read', [
                'model' => $this->findModel($id),
            ]);
    }

    /**
     * Creates a new UserMessages model.
     * For ajax request will return json object
     * and for non-ajax request if creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $request = Yii::$app->request;
        $model = new UserMessages();

        if($request->isAjax){
            /*
            *   Process for ajax request
            */
            Yii::$app->response->format = Response::FORMAT_JSON;
            if($request->isGet){
                return [
                    'title'=> "Create new UserMessages",
                    'content'=>$this->renderAjax('create', [
                        'model' => $model,
                    ]),
                    'footer'=> Html::button('Close',['class'=>'btn btn-default pull-left','data-dismiss'=>"modal"]).
                        Html::button('Save',['class'=>'btn btn-primary','type'=>"submit"])

                ];
            }else if($model->load($request->post()) && $model->save()){
                return [
                    'forceReload'=>'#crud-datatable-pjax',
                    'title'=> "Create new UserMessages",
                    'content'=>'<span class="text-success">Create UserMessages success</span>',
                    'footer'=> Html::button('Close',['class'=>'btn btn-default pull-left','data-dismiss'=>"modal"]).
                        Html::a('Create More',['create'],['class'=>'btn btn-primary','role'=>'modal-remote'])

                ];
            }else{
                return [
                    'title'=> "Create new UserMessages",
                    'content'=>$this->renderAjax('create', [
                        'model' => $model,
                    ]),
                    'footer'=> Html::button('Close',['class'=>'btn btn-default pull-left','data-dismiss'=>"modal"]).
                        Html::button('Save',['class'=>'btn btn-primary','type'=>"submit"])

                ];
            }
        }else{
            /*
            *   Process for non-ajax request
            */
//            var_dump($request->post());
//            var_dump($model->load($request->post()));
//            var_dump($model->attributes);
//            var_dump($files);
//            var_dump($receivers);
//            var_dump($users);
//            $users = [];
//            $data = $request->post('UserMessages');
//            $receivers = $data['receivers'];
//            $users = ArrayHelper::merge($users, $receivers);
//            $roles = $data['receivers-roles'];
//            if (is_array($roles) && count($roles) > 0) {
//                foreach ($roles as $role){
//                    $roleUsers = UserHelper::getRoleUsers($role);
//                    if (count($roleUsers) > 0) {
//                        foreach ($roleUsers as $roleUser) {
////                            var_dump($roleUser);
//                           $users[] =  $roleUser['id'];
//                        }
//                    }
//                }
//            }
//            var_dump('users');
//            var_dump(array_unique($users));
//            die;
            if ($model->load($request->post())) {
                if ($model->save()) {
//                    var_dump($model->attributes);
//                    die;
                $data = $request->post('UserMessages');
                $files = isset($data['files']) ? $data['files'] : [];
                if (is_array($files) && count($files) > 0) {
                    foreach ($files as $file) {
                        $userMessageFile = new UserMessagesFiles();
                        $userMessageFile->setAttributes([
                            'message_id' => $model->id,
                            'name' => $file['name'],
                            'path' => $file['path'],
                            'size' => $file['size'],
                            'type' => $file['type'],
                            'base_url' => $file['base_url'],
                        ]);
                        $userMessageFile->save();
                    }
                }
                $users = [];
                $receivers = isset($data['receivers']) ? $data['receivers'] : [];
                $users = ArrayHelper::merge($users, $receivers);
                $roles = isset($data['receivers-roles']) ? $data['receivers-roles'] : [];
                if (is_array($roles) && count($roles) > 0) {
                    foreach ($roles as $role){
                        $roleUsers = UserHelper::getRoleUsers($role);
                        if (count($roleUsers) > 0) {
                            foreach ($roleUsers as $roleUser) {
//                            var_dump($roleUser);
                                $users[] =  $roleUser['id'];
                            }
                        }
                    }
                }
                $users = array_unique($users);
                foreach ($users as $user){
                    MessageHelper::sendMessageToUser($user, $model->id);
                }
                return $this->redirect('index');
                }
            } else {
//                            var_dump($model->errors);
//die;
                return $this->render('create', [
                    'model' => $model,
                ]);
            }
        }

    }

    /**
     * Updates an existing UserMessages model.
     * For ajax request will return json object
     * and for non-ajax request if update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $request = Yii::$app->request;
        $model = $this->findModel($id);

        if($request->isAjax){
            /*
            *   Process for ajax request
            */
            Yii::$app->response->format = Response::FORMAT_JSON;
            if($request->isGet){
                return [
                    'title'=> "Update UserMessages #".$id,
                    'content'=>$this->renderAjax('update', [
                        'model' => $model,
                    ]),
                    'footer'=> Html::button('Close',['class'=>'btn btn-default pull-left','data-dismiss'=>"modal"]).
                        Html::button('Save',['class'=>'btn btn-primary','type'=>"submit"])
                ];
            }else if($model->load($request->post()) && $model->save()){
                return [
                    'forceReload'=>'#crud-datatable-pjax',
                    'title'=> "UserMessages #".$id,
                    'content'=>$this->renderAjax('view', [
                        'model' => $model,
                    ]),
                    'footer'=> Html::button('Close',['class'=>'btn btn-default pull-left','data-dismiss'=>"modal"]).
                        Html::a('Edit',['update','id'=>$id],['class'=>'btn btn-primary','role'=>'modal-remote'])
                ];
            }else{
                return [
                    'title'=> "Update UserMessages #".$id,
                    'content'=>$this->renderAjax('update', [
                        'model' => $model,
                    ]),
                    'footer'=> Html::button('Close',['class'=>'btn btn-default pull-left','data-dismiss'=>"modal"]).
                        Html::button('Save',['class'=>'btn btn-primary','type'=>"submit"])
                ];
            }
        }else{
            /*
            *   Process for non-ajax request
            */
            if ($model->load($request->post()) && $model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            } else {
                return $this->render('update', [
                    'model' => $model,
                ]);
            }
        }
    }

    /**
     * Delete an existing UserMessages model.
     * For ajax request will return json object
     * and for non-ajax request if deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $request = Yii::$app->request;
        $this->findModel($id)->delete();

        if($request->isAjax){
            /*
            *   Process for ajax request
            */
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ['forceClose'=>true,'forceReload'=>'#crud-datatable-pjax'];
        }else{
            /*
            *   Process for non-ajax request
            */
            return $this->redirect(['index']);
        }


    }

    /**
     * Delete multiple existing UserMessages model.
     * For ajax request will return json object
     * and for non-ajax request if deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionBulkDelete()
    {
        $request = Yii::$app->request;
        $pks = explode(',', $request->post( 'pks' )); // Array or selected records primary keys
        foreach ( $pks as $pk ) {
            $model = $this->findModel($pk);
            $model->delete();
        }

        if($request->isAjax){
            /*
            *   Process for ajax request
            */
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ['forceClose'=>true,'forceReload'=>'#crud-datatable-pjax'];
        }else{
            /*
            *   Process for non-ajax request
            */
            return $this->redirect(['index']);
        }

    }

    /**
     * Finds the UserMessages model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return UserMessages the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = UserMessages::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
