<?php

namespace backend\controllers;

use common\models\ChipEcu;
use common\models\ChipEcuDict;
use common\models\ChipEngine;
use Yii;
use common\models\ChipEcuAdditions;
use common\models\search\ChipEcuAdditionsSearch;
use backend\controllers\ChipController;
use yii\helpers\ArrayHelper;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\Response;

/**
 * ChipEcuAdditionsController implements the CRUD actions for ChipEcuAdditions model.
 */
class ChipEcuAdditionsController extends ChipController
{

    /** @inheritdoc */
    public function behaviors()
    {
        return ArrayHelper::merge(
            parent::behaviors(),
            [
                'verbs' => [
                    'class' => VerbFilter::class,
                    'actions' => [
                        'delete' => ['post'],
                    ],
                ],
            ]);
    }

    /**
     * Lists all ChipEcuAdditions models.
     * @return mixed
     */
    public function actionIndex()
    {
        $request = Yii::$app->request;
        $model = new ChipEcuAdditions();
        if ($request->isPost) {
            $post = $request->post();
            if (is_array($post['ChipEcuAdditions'])) {
                $data = $post['ChipEcuAdditions'];
                if (!empty($data['ecu_id'])) {
//                    foreach ($data['ecu_id'] as $ecuId){
                        if (!empty($data['additions']) && is_array($data['additions']) ) {
                            foreach ($data['additions'] as $additionId => $additionData) {
                                if (!empty($additionData['addition_id'])) {
                                    if ($add = ChipEcuAdditions::find()->where([
                                        'ecu_id' => $data['ecu_id'],
                                        'brand_id' => $data['brand_id'],
                                        'model_id' => 0,
                                        'generation_id' => 0,
                                        'engine_id' => 0,
                                        'addition_id' => $additionData['addition_id'],
                                    ])->one()) {
                                    } else {
                                        $add = new ChipEcuAdditions();
                                    }
//                                    ChipEcuAdditions::deleteAll(['ecu_id' => $this->ecu_id, 'brand_id' => $this->brand_id, 'model_id' => 0, 'generation_id' => 0, 'engine_id' => 0]);

                                    $add->setAttribute('brand_id', $data['brand_id']);
                                    $add->setAttribute('model_id', 0);
                                    $add->setAttribute('generation_id', 0);
                                    $add->setAttribute('engine_id', 0);
                                    $add->setAttribute('ecu_id', $data['ecu_id']);
                                    $add->setAttribute('addition_id', $additionData['addition_id']);
                                    $add->setAttribute('comment', $additionData['comment']);
                                    $add->setAttribute('price', $additionData['price']);
                                    $add->save(false);
//                        print_r($add);
                                }
                            }
                        }
//                    }
                }
//                echo 'afterSave<pre>';
//                print_r($post);
//                die;
//                ChipEcuAdditions::deleteAll(['ecu_id' => $this->ecu_id, 'brand_id' => $this->brand_id, 'model_id' => 0, 'generation_id' => 0, 'engine_id' => 0]);
//                if (isset($post['ChipEcuAdditions']['additions']) && !empty($post['ChipEcuAdditions']['additions']) ) {
//                    foreach ($post['ChipEcuAdditions']['additions'] as $additionId => $additionData) {
//                        if (!empty($additionData['addition_id'])) {
//                            $add = new ChipEcuAdditions();
//                            $add->setAttribute('brand_id', $this->brand_id);
//                            $add->setAttribute('model_id', $this->model_id);
//                            $add->setAttribute('generation_id', $this->generation_id);
//                            $add->setAttribute('engine_id', $this->engine_id);
//                            $add->setAttribute('ecu_id', $this->ecu_id);
//                            $add->setAttribute('addition_id', $additionData['addition_id']);
//                            $add->setAttribute('comment', $additionData['comment']);
//                            $add->setAttribute('price', $additionData['price']);
//                            $add->save();
////                        print_r($add);
//                        }
//                    }
//
            }
            return $this->redirect('index');
        }
//            die;

//            if ($model->load(Yii::$app->request->post()) && !$model->validate()){
//                return $this->render('create', [
//                    'model' => $model,
////                    'adds' => $adds,
//                ]);
//            }
//            $post = $request->post();
//            $data = $post['ChipEcuAdditions'];
//            if (!$data['ecu_id']) {
//                $model->addError();
//            }
//            if (is_array($data['additions'])) {
//                ChipEcuAdditions::deleteAll(['ecu_id' => $data['ecu_id']]);
//                foreach ($data['additions'] as $additionId) {
//                    $add = new ChipEcuAdditions();
//                    $add->setAttribute('brand_id', $data['brand_id']);
//                    $add->setAttribute('ecu_id', $data['ecu_id']);
//                    $add->setAttribute('addition_id', $additionId);
//                    $add->save();
//                }
//            }
//            print_r($data);
//            die;
//            return $this->redirect('index');
//
//        }
//        if ($model->load(Yii::$app->request->post()) && $model->save()) {
//            return $this->redirect(['view', 'id' => $model->id]);
//        }


//        if ($model->load(Yii::$app->request->post()) && $model->save()) {
//            return $this->redirect(['update', 'id' => $model->id]);
//        }
        return $this->render('create', [
            'model' => $model,
//            'adds' => $adds,
        ]);

//        $searchModel = new ChipEcuAdditionsSearch();
//        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
//
//        return $this->render('index', [
//            'searchModel' => $searchModel,
//            'dataProvider' => $dataProvider,
//        ]);
    }

    /**
     * Displays a single ChipEcuAdditions model.
     * @param integer $id
     * @return mixed
     */
    public function actionGetAddit()
    {
        $request = Yii::$app->request;
        $id = $request->get('id');
        $ecu = ChipEcu::findOne(['ecu_id' => $id]);
        return $this->renderPartial('/partial/quick_ecu_additions', ['model' => $ecu]);
//        return $this->render('view', [
//            'model' => $ecu,
//        ]);
    }

    /**
     * Displays a single ChipEcuAdditions model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        $ecu = ChipEcuDict::findOne($id);

        return $this->render('view', [
            'model' => $ecu,
        ]);
    }

    /**
     * Creates a new ChipEcuAdditions model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $request = Yii::$app->request;
        $model = new ChipEcuAdditions();
        if ($request->isPost) {
            $post = $request->post();

            if (is_array($post['ChipEcuAdditions'])) {
                $data = $post['ChipEcuAdditions'];
                if (!empty($data['ecu_id']) && is_array($data['ecu_id'])) {
                    foreach ($data['ecu_id'] as $ecuId){
                        if (!empty($data['additions']) && is_array($data['additions']) ) {
                            foreach ($data['additions'] as $additionId => $additionData) {
                                if (!empty($additionData['addition_id'])) {
                                    $add = new ChipEcuAdditions();
                                    $add->setAttribute('brand_id', $data['brand_id']);
                                    $add->setAttribute('model_id', 0);
                                    $add->setAttribute('generation_id', 0);
                                    $add->setAttribute('engine_id', 0);
                                    $add->setAttribute('ecu_id', $ecuId);
                                    $add->setAttribute('addition_id', $additionData['addition_id']);
                                    $add->setAttribute('comment', $additionData['comment']);
                                    $add->setAttribute('price', $additionData['price']);
                                    $add->save();
//                        print_r($add);
                                }
                            }
                        }
                    }
                }
//                echo 'afterSave<pre>';
//                print_r($post);
//                die;
//                ChipEcuAdditions::deleteAll(['ecu_id' => $this->ecu_id, 'brand_id' => $this->brand_id, 'model_id' => 0, 'generation_id' => 0, 'engine_id' => 0]);
//                if (isset($post['ChipEcuAdditions']['additions']) && !empty($post['ChipEcuAdditions']['additions']) ) {
//                    foreach ($post['ChipEcuAdditions']['additions'] as $additionId => $additionData) {
//                        if (!empty($additionData['addition_id'])) {
//                            $add = new ChipEcuAdditions();
//                            $add->setAttribute('brand_id', $this->brand_id);
//                            $add->setAttribute('model_id', $this->model_id);
//                            $add->setAttribute('generation_id', $this->generation_id);
//                            $add->setAttribute('engine_id', $this->engine_id);
//                            $add->setAttribute('ecu_id', $this->ecu_id);
//                            $add->setAttribute('addition_id', $additionData['addition_id']);
//                            $add->setAttribute('comment', $additionData['comment']);
//                            $add->setAttribute('price', $additionData['price']);
//                            $add->save();
////                        print_r($add);
//                        }
//                    }
//
                }
            return $this->redirect('index');
            }
//            die;

//            if ($model->load(Yii::$app->request->post()) && !$model->validate()){
//                return $this->render('create', [
//                    'model' => $model,
////                    'adds' => $adds,
//                ]);
//            }
//            $post = $request->post();
//            $data = $post['ChipEcuAdditions'];
//            if (!$data['ecu_id']) {
//                $model->addError();
//            }
//            if (is_array($data['additions'])) {
//                ChipEcuAdditions::deleteAll(['ecu_id' => $data['ecu_id']]);
//                foreach ($data['additions'] as $additionId) {
//                    $add = new ChipEcuAdditions();
//                    $add->setAttribute('brand_id', $data['brand_id']);
//                    $add->setAttribute('ecu_id', $data['ecu_id']);
//                    $add->setAttribute('addition_id', $additionId);
//                    $add->save();
//                }
//            }
//            print_r($data);
//            die;
//            return $this->redirect('index');
//
//        }
//        if ($model->load(Yii::$app->request->post()) && $model->save()) {
//            return $this->redirect(['view', 'id' => $model->id]);
//        }


//        if ($model->load(Yii::$app->request->post()) && $model->save()) {
//            return $this->redirect(['update', 'id' => $model->id]);
//        }
        return $this->render('create', [
            'model' => $model,
//            'adds' => $adds,
        ]);
    }

    /**
     * Updates an existing ChipEcuAdditions model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = ChipEcuAdditions::find()->where(['ecu_id' => $id])->one();
        $request = Yii::$app->request;
        $adds = ChipEcuAdditions::find()->where(['ecu_id' => $id])->select('addition_id')->column();

        if ($request->isPost) {
            if ($model->load(Yii::$app->request->post()) && !$model->validate()) {
                return $this->render('update', [
                    'model' => $model,
                    'adds' => $adds,
                ]);
            }

//            print_r($data);
//            die;
            return $this->redirect('index');

        } else {

        }


//        if ($model->load(Yii::$app->request->post()) && $model->save()) {
//            return $this->redirect(['update', 'id' => $model->id]);
//        }
//        if ($model->load(Yii::$app->request->post()) && $model->save()) {
//            return $this->redirect(['view', 'id' => $model->id]);
//        }
        return $this->render('update', [
            'model' => $model,
            'adds' => $adds,
        ]);
    }

    /**
     * Deletes an existing ChipEcuAdditions model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the ChipEcuAdditions model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return ChipEcuAdditions the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = ChipEcuAdditions::findOne($id)) !== null) {
            return $model;
        }
        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
