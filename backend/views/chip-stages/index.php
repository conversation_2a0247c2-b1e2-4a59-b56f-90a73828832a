<?php

use yii\helpers\Html;
use backend\widgets\GridView;

/* @var $this yii\web\View */
/* @var $searchModel common\models\ChipStagesSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = Yii::t('backend', 'Chip Stages');
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="chip-stages-index">
    <p>
        <?php echo Html::a(Yii::t('backend', 'Create Chip Stage'), ['create'], ['class' => 'btn btn-success']) ?>
    </p>

    <?php echo GridView::widget([
        'bsVersion' => '4.x',
        'id'=>'crud-datatable',
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'as filterBehavior' => \thrieu\grid\FilterStateBehavior::className(),
        'exportConfig' => [\kartik\grid\GridView::EXCEL => true, \kartik\grid\GridView::PDF => true],
        'pjax' => true,
        'striped' => true,
        'condensed' => true,
        'clearFilter' => true,
        'responsive' => true,
        'panel' => [
            'heading' => '<i class="glyphicon glyphicon-list"></i> ЭБУ',
        ],
        'columns' => [
            'title',
            ['class' => 'yii\grid\ActionColumn'],
        ],
    ]); ?>
</div>
