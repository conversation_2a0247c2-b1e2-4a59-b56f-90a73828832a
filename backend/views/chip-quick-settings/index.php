<?php

use common\models\ChipBrand;
use common\models\ChipEcu;
use common\models\ChipGeneration;
use common\models\ChipModel;
use yii\bootstrap\ActiveForm;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use backend\widgets\GridView;
use yii\helpers\Url;
use yii\web\View;

/* @var $this yii\web\View */
/* @var $searchModel common\models\ChipEcuSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = Yii::t('backend', 'Quick Ecu Settings');
$this->params['breadcrumbs'][] = $this->title;
$script = <<< JS
    
    $('.dinostend_file').mouseover(function() {
        console.log(this.dataset);
        $('.dino_img_'+this.dataset.id).show();
    });
    $('.dinostend_file').mouseout(function() {
        console.log(this.dataset);
        $('.dino_img_'+this.dataset.id).hide();
    });

    // $('#table').DataTable();

      // $('#table').excelTableFilter();
JS;
//маркер конца строки, обязательно сразу, без пробелов и табуляции
$this->registerJs($script, yii\web\View::POS_READY);
//var_dump(common\models\ChipEcu::find()->where(!empty($searchModel->brand_id) ? 'chip_ecu.brand_id = ' . $searchModel->brand_id : 'chip_ecu.brand_id>0')
//    ->leftJoin('chip_ecu_dict', 'chip_ecu_dict.id = chip_ecu.ecu_id')
//    ->select('chip_ecu.id as id, chip_ecu_dict.title as title')->groupBy('chip_ecu.ecu_id')->createCommand()->getRawSql());
//die;
$this->registerJs(<<<JS
    $(document).on('pjax:beforeSend', function() {
       $('#pjax1').css('background','#fff');
    });
    $(document).on('pjax:complete', function() {
      $('#pjax1').css('background', 'transparent');
    });
JS
);
?>
<style>
    .dinostend_img{display:none;max-width: 400px;position: absolute;}
</style>
<div class="chip-ecu-index card chip-ecu-admin">
    <ul class="nav nav-tabs md-tabs" role="tablist">
        <li class="nav-item">
            <a class="nav-link active" data-toggle="tab" href="#addForm" role="tab"><i class="icofont icofont-ui-add f-20"></i> <?=Yii::t('backend', 'Add ECU settings')?></a>
            <div class="slide slide-quick"></div>
        </li>
        <li class="nav-item">
            <a class="nav-link" data-toggle="tab" href="#delForm" role="tab"><i class="icofont icofont-danger-zone f-20"></i> <?=Yii::t('backend', 'Delete ECU settings')?></a>
            <div class="slide slide-quick"></div>
        </li>
    </ul>
    <div class="tab-content">
        <div class="col-md-12 col-lg-12 col-sm-12 tab-pane active" id="addForm" role="tabpanel">
            <?php $form = ActiveForm::begin(['method' => 'post', 'options' => ['enctype' => "multipart/form-data",] ]); ?>
            <input type="hidden" value="add" name="form_type">
            <div class="col-sm-12 col-xl-12 col-md-12 m-t-20 m-b-20" >
                <?php echo Html::a(Yii::t('backend', 'Save'), ['save-additions'], [
                    'class' => 'btn btn-warning',
                    'data' => [
                        'confirm' => Yii::t('backend', 'Are you sure you want to save this items?'),
        //                'method' => 'post',
                        'pjax' => 1,
        //                'callback' => 'function(res){consolr.log(res);}'
                    ],
                ]) ?>
            </div>
            <div class="col-sm-12 col-xl-12 col-md-12 ecus_div"></div>

            <div class="col-sm-12 col-xl-12 col-md-12" id="stages_div">
                <?=$this->render('/partial/quick_ecu_stages', ['model' => null, 'viewType' => 'full'])?>
            </div>
            <div class="col-sm-12 col-xl-12 col-md-12" id="additions_div">
                <?=$this->render('/partial/quick_ecu_additions', ['model' => null, 'viewType' => 'full'])?>
            </div>
            <div class="col-sm-12 col-xl-12 col-md-12 m-t-20 m-b-20" >
                <?php echo Html::a(Yii::t('backend', 'Save'), ['save-additions'], [
                    'class' => 'btn btn-warning',
                    'data' => [
                        'confirm' => Yii::t('backend', 'Are you sure you want to save this items?'),
                        //                'method' => 'post',
                        'pjax' => 1,
                        //                'callback' => 'function(res){consolr.log(res);}'
                    ],
                ]) ?>
            </div>
            <?php ActiveForm::end(); ?>
        </div>
        <div class="col-md-12 col-lg-12 col-sm-12 tab-pane" id="delForm" role="tabpanel">
            <?php $form = ActiveForm::begin(); ?>
            <input type="hidden" value="del" name="form_type">
            <div class="col-sm-8 col-xl-8 col-md-8 m-t-20 m-b-20" >
                <?php echo Html::a(Yii::t('backend', 'Save'), ['save-additions'], [
                    'class' => 'btn btn-warning',
                    'data' => [
                        'confirm' => Yii::t('backend', 'Are you sure you want to save this items?'),
                        //                'method' => 'post',
                        'pjax' => 1,
                        //                'callback' => 'function(res){consolr.log(res);}'
                    ],
                ]) ?>
            </div>
            <div class="row col-sm-4 col-xl-4 col-md-4 m-t-20 m-b-20">
                <div class="col-sm-5 col-xl-5 col-md-5 m-b-10">
                    <?= Yii::t('backend', 'On')?>/<?= Yii::t('backend', 'Off')?>
                    <input type="checkbox" name="dinostend_file" value="1" class="js-switch"/>
                </div>
                <div class="col-sm-7 col-xl-7 col-md-7 m-b-10">
                    <label for="dinostend_file" class="text-dark"><h3><?= Yii::t('backend', 'Dinostend file')?></h3></label>
                </div>
            </div>

            <div class="col-sm-12 col-xl-12 col-md-12 ecus_div"></div>

            <div class="col-sm-12 col-xl-12 col-md-12" id="stages_div">
                <?=$this->render('/partial/quick_ecu_del_stages', ['model' => null, 'viewType' => 'full'])?>
            </div>
            <div class="col-sm-12 col-xl-12 col-md-12" id="additions_div">
                <?=$this->render('/partial/quick_ecu_del_additions', ['model' => null, 'viewType' => 'full'])?>
            </div>
            <?php ActiveForm::end(); ?>

        </div>
    </div>

    <?php \yii\widgets\Pjax::begin(['id' => 'pjax1',

        'timeout' => false,

        'enablePushState' => true,

        'clientOptions' => ['method' => 'GET']]); ?>

    <?php /** @var TYPE_NAME $ecuFilterData */
    /** @var TYPE_NAME $brandFilterData */
    echo GridView::widget([
        'bsVersion' => '4.x',
        'id'=>'crud-datatable',
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'as filterBehavior' => \thrieu\grid\FilterStateBehavior::className(),
        'exportConfig' => [\kartik\grid\GridView::EXCEL => true, \kartik\grid\GridView::PDF => true],
        'striped' => true,
        'formAction' => 'quick',
        'condensed' => true,
        'clearFilter' => true,
        'responsive' => true,
        'panel' => [
            'heading' => '<i class="glyphicon glyphicon-list"></i> ',
        ],
        'columns' => [
            [
                'class' => 'kartik\grid\ExpandRowColumn',
                'width' => '50px',
                'value' => function ($model, $key, $index, $column) {
                    return GridView::ROW_COLLAPSED;
                },
                'detail' => function ($model, $key, $index, $column) {
                    $html = '';
                    if (!empty($model->additions)) {
                        foreach ($model->additions as $addition) {
                            $html .= '<p class="text-dark">';
                            $html .= $addition->addition->title;
                            $html .= '</p>';
                        }
                    }
                    return $html;
                },
                'headerOptions' => ['class' => 'kartik-sheet-style'] ,
                'expandOneOnly' => false,
            ],
            [
                'attribute' => 'ecu_id',
                'format' => 'raw',
                'width' => '550px',
                'class'=>'\kartik\grid\DataColumn',
                'value' => function($model){
                    $dinostend_files = [];
                    $html = '';
                    $html .= !$model->ecu ? '' : $model->ecu->title;
                    foreach($model->stages as $stage) {
                        $href = Url::to('/uploads/dinostend/'.$stage->dinostend_file, true);
                        $html .= $stage->dinostend_file ? '<a class="dinostend_file dino_file_'.$model->id.'_'.$stage->id.'" data-id="'.$model->id.'_'.$stage->id.'" data-src="'.$href.'" data-pjax="0" target="_blank" href="'.$href.'"><span class="ti-image"></span></a>&nbsp<img class="dinostend_img dino_img_'.$model->id.'_'.$stage->id.'" src="'.$href.'"/>' : '';
                    }
                    return $html;
                },
                'filterType' => \kartik\grid\GridView::FILTER_SELECT2,
                'filter' => $ecuFilterData,
                'filterWidgetOptions'=>[
                    'pluginOptions'=>['allowClear'=>true, 'multiple' => true],
                ],
                'filterInputOptions'=>['placeholder'=>'Выберите'],
            ],
            [
                'attribute' => 'brand_id',
                'format' => 'html',
                'width' => '550px',
                'class'=>'\kartik\grid\DataColumn',
                'value' => function($model){
                    return $model->brand->title;
                },
                'filterType'=> \kartik\grid\GridView::FILTER_SELECT2,
                'filter'=>$brandFilterData,

                'filterWidgetOptions'=>[
                    'pluginOptions'=>['allowClear'=>true],
                ],
                'filterInputOptions'=>['placeholder'=>'Выберите'],
            ],
            [
                'attribute' => 'model_id',
                'format' => 'html',
                'width' => '550px',
                'class'=>'\kartik\grid\DataColumn',
                'value' => function($model){
                    return $model->model->title;
                },
            ],
            [
                'attribute' => 'generation_id',
                'format' => 'html',
                'width' => '550px',
                'class'=>'\kartik\grid\DataColumn',
                'value' => function($model){
//                    return $model->generation->title;
                    return !$model->generation ? '' : $model->generation->title;
                },
            ],
            [
                'attribute' => 'engine_id',
                'format' => 'html',
                'width' => '550px',
                'class'=>'\kartik\grid\DataColumn',
                'value' => function($model){
                    return $model->engine->title;
                },
            ],
            [
                'attribute' => 'standard',
                'headerOptions' => ['class' => 'kartik-sheet-style'] ,
                'header' => '<a href="#"> Standard</a>',
                'format' => 'html',
                'width' => '100px',
                'class'=>'\kartik\grid\DataColumn',
                'value' => function($model){
//                    return var_dump($model->stages);
                    return isset($model->stages[0]) ? (int)$model->stages[0]->inc_hp.' Hp/'.(int)$model->stages[0]->inc_tork.' Nm' : Yii::t('backend', 'No data');
                },
            ],
            [
                'headerOptions' => ['class' => 'kartik-sheet-style'] ,
                'header' => '<a href="#"> Stage 1</a>',
                'format' => 'html',
                'width' => '100px',
                'class'=>'\kartik\grid\DataColumn',
                'value' => function($model){
                    return isset($model->stages[1]) ? (int)$model->stages[1]->inc_hp.' Hp/'.(int)$model->stages[1]->inc_tork.' Nm' : Yii::t('backend', 'No data');
                },
            ],
//            [
//                'header' => 'Stage 2',
//                'format' => 'html',
//                'width' => '100px',
//                'class'=>'\kartik\grid\DataColumn',
//                'value' => function($model){
//                    return isset($model->stages[2]) ? (int)$model->stages[2]->inc_hp.' Hp/'.(int)$model->stages[2]->inc_tork.' Nm' : Yii::t('backend', 'No data');
//                },
//            ],
//            [
//                'header' => 'Stage 3',
//                'format' => 'html',
//                'width' => '100px',
//                'class'=>'\kartik\grid\DataColumn',
//                'value' => function($model){
//                    return isset($model->stages[3]) ? (int)$model->stages[3]->inc_hp.' Hp/'.(int)$model->stages[3]->inc_tork.' Nm' : Yii::t('backend', 'No data');
//                },
//            ],
            [
                'class' => 'kartik\grid\CheckboxColumn',
            ],
        ],
    ]); ?>
    <?php \yii\widgets\Pjax::end(); ?>
</div>
