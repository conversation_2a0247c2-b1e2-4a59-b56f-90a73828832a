<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\ChipEcu */

$this->title = $model->title;
$this->params['breadcrumbs'][] = ['label' => Yii::t('backend', 'Chip Ecus'), 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="chip-ecu-view">

    <p>
        <?php echo Html::a(Yii::t('backend', 'Update'), ['update', 'id' => $model->id], ['class' => 'btn btn-primary']) ?>
        <?php echo Html::a(Yii::t('backend', 'Delete'), ['delete', 'id' => $model->id], [
            'class' => 'btn btn-danger',
            'data' => [
                'confirm' => Yii::t('backend', 'Are you sure you want to delete this item?'),
                'method' => 'post',
            ],
        ]) ?>
    </p>

    <?php echo DetailView::widget([
        'model' => $model,
        'attributes' => [
            [
                'attribute' => 'brand_id',
                'value' => function($model) {
                    return $model->brand->title;
                }
            ],
            [
                'attribute' => 'model_id',
                'value' => function($model) {
                    return $model->model->title;
                }
            ],
            [
                'attribute' => 'generation_id',
                'value' => function($model) {
                    return $model->generation->title;
                }
            ],
            [
                'attribute' => 'engine_id',
                'value' => function($model) {
                    return $model->engine->title;
                }
            ],
//            'engine_id',
            'title',
//            'slug',
//            'created_at',
//            'updated_at',
//            'deleted_at',
        ],
    ]) ?>

</div>
