<?php

use common\models\ChipAddition;
use common\models\ChipBrand;
use common\models\ChipEngine;
use common\models\ChipGeneration;
use common\models\ChipModel;
use kartik\widgets\DepDrop;
use kartik\widgets\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\bootstrap\ActiveForm;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $model common\models\ChipEcu */
/* @var $form yii\bootstrap\ActiveForm */
/* @var $viewType string */

//echo '<pre>';
//print_r($model->additionsList);
//die;
?>
<div class="row">
    <?php $form = ActiveForm::begin(); ?>
    <div class="col-lg-12 col-xl-12">
    <div class="form-group">
        <?php echo Html::submitButton($model->isNewRecord ? Yii::t('backend', 'Create') : Yii::t('backend', 'Save'), ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
    </div>
    </div>
    <div class="col-lg-12 col-xl-12">
        <!-- Nav tabs -->
        <ul class="nav nav-tabs  tabs" role="tablist">
            <li class="nav-item active">
                <a class="nav-link" data-toggle="tab" href="#edit" role="tab"><?= Yii::t('backend', 'Edit information')?></a>
            </li>
            <?php if (!$model->isNewRecord) { ?>
            <?php } ?>
                <li class="nav-item">
                    <a class="nav-link" data-toggle="tab" href="#additions" role="tab"><?= Yii::t('backend', 'Edit additions')?></a>
                </li>
        </ul>
        <!-- Tab panes -->
        <div class="tab-content tabs card-block">
            <div class="tab-pane active" id="edit" role="tabpanel">

                <div class="card">
                    <div class="card-block">


                    <?php echo $form->errorSummary($model); ?>

                    <div class="row">
                        <div class="col-sm-6">
                            <?= $form->field($model, 'brand_id')->widget(Select2::classname(), [
                                'theme' => Select2::THEME_KRAJEE,
                                'language' => 'ru',
                                'data' => ArrayHelper::map(common\models\ChipBrand::find()->orderBy('title')->all(),'id', 'title'),
                                'options' => [
                                    'placeholder' => 'Выберите',
                                    'id'=>'brand_id'
                                ],
                                'pluginOptions' => [
                                    'allowClear' => true,
                                    'initialize' => true,
                                ],
                            ]); ?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <?= $form->field($model, 'model_id')->widget(DepDrop::classname(), [
                                'type' => DepDrop::TYPE_SELECT2,
                                'language' => 'ru',
                                'data' => ArrayHelper::map(common\models\ChipModel::find()->orderBy('title')->all(),'id', 'title'),
                                'options' => [
                                    'placeholder' => 'Выберите',
                                    'id'=>'model_id'
                                ],
                                'select2Options' => ['pluginOptions' => ['allowClear' => true]],
                                'pluginOptions' => [
                                    'depends'=>['brand_id'],
                                    'placeholder'=>'Выберите марку',
                                    'url'=>Url::to(['/chip-model/json-list']),
                                ],
                            ]); ?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <?= $form->field($model, 'generation_id')->widget(DepDrop::classname(), [
                                'type' => DepDrop::TYPE_SELECT2,
                                'language' => 'ru',
                                'data' => ArrayHelper::map(common\models\ChipGeneration::find()->orderBy('title')->all(),'id', 'title'),
                                'options' => [
                                    'placeholder' => 'Выберите',
                                    'id'=>'generation_id'
                                ],
                                'select2Options' => ['pluginOptions' => ['allowClear' => true]],
                                'pluginOptions' => [
                                    'depends'=>['model_id'],
                                    'placeholder'=>'Выберите модель',
                                    'url'=>Url::to(['/chip-generation/json-list']),
                                ],
                            ]); ?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <?= $form->field($model, 'engine_id')->widget(DepDrop::classname(), [
                                'type' => DepDrop::TYPE_SELECT2,
                                'language' => 'ru',
                                'data' => ArrayHelper::map(common\models\ChipEngine::find()->orderBy('title')->all(),'id', 'title'),
                                'options' => [
                                    'placeholder' => 'Выберите',
                                    'id'=>'engine_id'
                                ],
                                'select2Options' => ['pluginOptions' => ['allowClear' => true]],
                                'pluginOptions' => [
                                    'depends'=>['generation_id'],
                                    'placeholder'=>'Выберите поколение',
                                    'url'=>Url::to(['/chip-engine/json-list']),
                                ],
                            ]); ?>
                        </div>
                    </div>
                    <div class="row">
                            <div class="col-sm-6">
                                <?= $form->field($model, 'ecu_id')->widget(DepDrop::classname(), [
                                    'type' => DepDrop::TYPE_SELECT2,
                                    'language' => 'ru',
                                    'data' => ArrayHelper::map(common\models\ChipEcuDict::find()->orderBy('title')->all(),'id', 'title'),
                                    'options' => [
                                        'placeholder' => 'Выберите',
                                    ],
                                    'select2Options' => ['pluginOptions' => ['allowClear' => true]],
                                    'pluginOptions' => [
                                        'depends'=>['engine_id'],
                                        'placeholder'=>'Выберите Двигатель',
                                        'url'=>Url::to(['/chip-ecu/json-list']),
                                    ],
                                ]); ?>
                            </div>
                        </div>



                    </div>
                </div>
            </div>
            <div class="tab-pane" id="additions" role="tabpanel">
                <div class="card">
                    <div class="row">
                        <div class="col-sm-12 col-xl-6 col-md-6 m-b-30">
                            <div class="card-header">
                                <h4><?= Yii::t('backend', 'Edit tuning additions')?></h4>
                            </div>
                            <div class="card-block">
                                <?=$this->render('ecu_additions', ['model' => $model, 'viewType' => 'full'])?>
                            </div>
                        </div>
                        <div class="col-sm-12 col-xl-6 col-md-6 m-b-30">
                            <div class="card-header">
                                <h4><?= Yii::t('backend', 'Edit tuning stages')?></h4>
                            </div>
                            <div class="card-block">
                                <?php foreach (\common\models\ChipStages::find()->all() as $stage) { ?>
                                        <div class="row z-depth-top-0 m-b-10  p-b-10">
                                            <div class="col-sm-12 col-xl-12 col-md-12 text-center text-danger m-b-10 m-t-10">
                                                <label><h3><?=$stage->title?></h3></label>
                                            </div>
                                            <div class="col-sm-3 col-xl-3 col-md-3 m-b-10">
                                                <label><?= Yii::t('backend', 'Horse power')?></label>
                                            </div>
                                            <div class="col-sm-9 col-xl-9 col-md-9 m-b-10">
                                                <input type="text" name="ChipEcuAdditions[stages][<?=$stage->id?>][inc_hp]" value="<?= !empty($model->stagesList[$stage->id]) ? $model->stagesList[$stage->id]->inc_hp : ''?>"/>
                                            </div>
                                            <div class="col-sm-3 col-xl-3 col-md-3 m-b-10">
                                                <label><?= Yii::t('backend', 'Torque')?></label>
                                            </div>
                                            <div class="col-sm-9 col-xl-9 col-md-9 m-b-10">
                                                <input type="text" name="ChipEcuAdditions[stages][<?=$stage->id?>][inc_tork]" value="<?= !empty($model->stagesList[$stage->id]) ? $model->stagesList[$stage->id]->inc_tork : ''?>"/>
                                            </div>
                                            <div class="col-sm-12 col-xl-3 col-md-3 m-b-10">
                                                <br><label>Описание</label>
                                            </div>
                                            <div class="col-sm-12 col-xl-9 col-md-9 m-b-10">
                                                <textarea rows="3" cols="45" name="ChipEcuAdditions[stages][<?=$stage->id?>][comment]"><?=!empty($model->stagesList[$stage->id]) ? $model->stagesList[$stage->id]->comment : ''?></textarea>
                                            </div>
                                            <div class="col-sm-3 col-xl-3 col-md-3">
                                                <label>Цена</label>
                                            </div>
                                            <div class="col-sm-9 col-xl-9 col-md-9">
                                                <input type="text" name="ChipEcuAdditions[stages][<?=$stage->id?>][price]" value="<?=!empty($model->stagesList[$stage->id]) ? $model->stagesList[$stage->id]->price : ''?>"/>
                                            </div>
                                        </div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php ActiveForm::end(); ?>
</div>
