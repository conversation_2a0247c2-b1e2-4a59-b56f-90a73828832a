<?php

use common\models\ChipAddition;
use common\models\ChipBrand;
use common\models\ChipEngine;
use common\models\ChipGeneration;
use common\models\ChipModel;
use common\models\ChipStages;
use kartik\widgets\DepDrop;
use kartik\widgets\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\bootstrap\ActiveForm;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $models common\models\ChipEcuStages[] */
/* @var $form yii\bootstrap\ActiveForm */
/* @var $stagesDict common\models\ChipStages[] */
/* @var $tarifsDict common\models\ChipTarifs[] */
/* @var $viewType string */
//echo '<pre>';
//print_r($priceData);
//die;
?>
        <?php foreach (ChipStages::find()->where('title != "Standard"')->all() as $keyStage => $stage) { ?>

            <label for="stage_del_<?=$stage->id?>" class="col-sm-2 col-xl-2 col-md-2"><input type="checkbox"  name="ChipPrice[stages][]" id="stage_del_<?=$stage->id?>" value="<?=$stage->id?>" class="js-switch"/> <?=$stage->title?></label>
        <?php } ?>



