<?php

use common\models\ChipAddition;
use common\models\ChipBrand;
use common\models\ChipEngine;
use common\models\ChipGeneration;
use common\models\ChipModel;
use common\models\ChipStages;
use kartik\widgets\DepDrop;
use kartik\widgets\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\bootstrap\ActiveForm;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $additionsDataArray array */
/* @var $stagesDict array */
/* @var $stagesEcuDataArray array */
//echo '<pre>';
//print_r($model->projectOptions);
//die;
?>
<div class="row">
    <div class="col-md-12 col-lg-12 col-sm-12">
        <h6 class="sub-title text-center p-t-5"><?=Yii::t('backend', 'Tuning Stage')?>
            <span class="btn btn-danger pcoded-badge label label-danger f-right project-stage-change"
                  data-id="<?=$model->id?>" data-toggle="modal" data-target="#change_stage_modal"><?=Yii::t('backend',
                    'Change')?></span>
        </h6>
    </div>
    <div class="col-md-12 col-lg-12 col-sm-12 stage-empty hidden">
        <div class="col-md-3 col-lg-3 col-sm-3 text-dark text-left">
            <span class="label label-danger">No tuning required</span>
        </div>
        <div class="col-md-4 col-lg-4 col-sm-4 text-dark text-center lead">
        </div>
        <div class="col-md-1 col-lg-1 col-sm-1">
        </div>
        <div class="col-md-2 col-lg-2 col-sm-2">
        </div>
        <div class="col-md-2 col-lg-2 col-sm-2">
        </div>
    </div>
    <div class="col-md-12 col-lg-12 col-sm-12 stage stage-<?=$model->id?>">
        <div class="col-md-3 col-lg-3 col-sm-3 text-dark text-left">
            <?= !empty($model->stage) ?
                '<h5 class="text-light">'.$model->stage->title.'</h5>' :
                '<span class="label label-danger">No tuning required</span>' ;
            ?>
        </div>
        <div class="col-md-4 col-lg-4 col-sm-4 text-light text-center lead">
            <?= !empty($model->stage) ?
                (int)$model->projectStage->inc_hp
                . ' '
                . Yii::t('backend', 'BHP')
                . ' / '
                . (int)$model->projectStage->inc_tork
                . ' '
                . Yii::t('backend', 'NM'). '' : '' ; ?>
        </div>
        <div class="col-md-1 col-lg-1 col-sm-1">
        </div>
        <div class="col-md-2 col-lg-2 col-sm-2">
            <?= !empty($model->projectStage) ? (!$model->projectStage->comment ? '' : ('<i class="fa fa-exclamation-triangle mytooltip text-warning fa-lg"><span class="tooltip-content5"><span class="tooltip-text3 card card-border-warning"><span class="tooltip-inner2">'.$model->projectStage->comment.'</span></span></span></i>')) : '' ?>
        </div>
        <div class="col-md-2 col-lg-2 col-sm-2">
            <?php if (!empty($model->projectStage)) { ?>
                <div class="f-right">
                    <i class="fa fa-check text-success fa-2x"></i>
                </div>
            <?php } ?>
        </div>
    </div>
    <div class="col-md-12 col-lg-12 col-sm-12">
        <h6 class="sub-title text-center p-t-5">
            <?=Yii::t('backend', 'Tuning Options')?>
            <span class="btn btn-danger pcoded-badge label label-danger f-right project-options-change" data-id="<?=$model->id?>" data-toggle="modal" data-target="#change_additions_modal"><?=Yii::t('backend', 'Change')?></span>
        </h6>
    </div>
    <div class="col-md-12 col-lg-12 col-sm-12">
        <?php if (!empty($model->projectOptions)) { ?>
            <?php foreach ($model->projectOptions as $option) {?>
                <div class="col-md-7 col-lg-7 col-sm-7 text-dark">
                    <span id="span_<?=$option->id?>" class="text-light"><?=$option->addition->title?></span>
                </div>
                <div class="col-md-1 col-lg-1 col-sm-1">
                </div>
                <div class="col-md-2 col-lg-2 col-sm-2">
                    <?=(isset($option->ecuAddition) && !empty($option->ecuAddition->comment)) ? ('<i class="fa fa-exclamation-triangle mytooltip text-warning fa-2x"><span class="tooltip-content5"><span class="tooltip-text3 card card-border-warning"><span class="tooltip-inner2">'.$option->ecuAddition->comment.'</span></span></span></i>') : ''?>
                </div>
                <div class="col-md-2 col-lg-2 col-sm-2">
                    <div class="f-right">
                        <span class="cr cursor-default"><i class="fa fa-check text-success fa-2x"></i></span>
                    </div>
                </div>
            <?php } ?>
        <?php } else { ?>
            <div class="col-md-12 col-lg-12 col-sm-12 stage-empty">
                <span class="label label-danger">No tuning required</span>
            </div>
        <?php } ?>
    </div>
</div>
<?= $this->render('/partial/project_view_additions_modal', ['model' => $model, 'additionsDataArray' => $additionsDataArray]);?>



