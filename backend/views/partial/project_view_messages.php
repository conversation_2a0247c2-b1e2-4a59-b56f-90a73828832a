<?php

use common\models\ChipAddition;
use common\models\ChipBrand;
use common\models\ChipEngine;
use common\models\ChipGeneration;
use common\models\ChipModel;
use common\models\ChipStages;
use kartik\widgets\DepDrop;
use kartik\widgets\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\bootstrap\ActiveForm;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $additionsDataArray array */
/* @var $stagesDict array */
/* @var $stagesEcuDataArray array */
//echo '<pre>';
//print_r($priceData);
//die;
?>
<div class="row">
    <?php foreach (ChipAddition::find()->all() as $addition) { ?>
    <?php if (!isset($additionsDataArray[$addition->id])) {continue;} ?>
    <div class="col-sm-12 col-xl-12 col-md-12 m-b-10">
        <div class="col-sm-5 col-xl-5 col-md-5 m-b-10">
            <label
                    for="addit_<?=$addition->id?>"
                <?php if (!empty($addition->comment)) { ?>
                    data-toggle="popover"
                    data-html="true"
                    data-placement="top"
                    title="<b class='danger'><?= Yii::t('backend', 'Notice'); ?></b>"
                    data-content="<?=$addition->comment?>"
                <?php } ?>
            ><input
                    type="checkbox"
                    name="Projects[additions][<?=$addition->id?>][addition_id]"
                    id="addit_<?=$addition->id?>"
                    value="<?=$addition->id?>"
                <?php if (!empty($addition->comment)) { ?>
                    data-toggle="popover"
                    data-html="true"
                    data-placement="top"
                    title="<b><?= Yii::t('backend', 'Notice'); ?></b>"
                    data-content="<?=$addition->comment?>"
                <?php } ?>
                    class="js-switch" <?= !empty($model->quickAdditionsList[$addition->id]) ? 'checked' :
                    ''?>/>&nbsp<?=$addition->title?></label>
        </div>
        <div class="col-sm-1 col-xl-1 col-md-1 ">
            <?= !$additionsDataArray[$addition->id]->comment ? '' : ('<i class="fa fa-exclamation-triangle mytooltip text-warning fa-lg m-t-10"><span class="tooltip-content5"><span class="tooltip-text3 card card-border-warning"><span class="tooltip-inner2">'.$additionsDataArray[$addition->id]->comment.'</span></span></span></i>');?>
        </div>
        <div class="col-sm-2 col-xl-2 col-md-2 m-b-10">
            <div class="dropdown-warning dropdown">
                <button class="btn btn-warning dropdown-toggle waves-effect waves-light " type="button" id="dropdown-5" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">DTC</button>
                <div class="dropdown-menu" aria-labelledby="dropdown-5" data-dropdown-in="fadeIn" data-dropdown-out="fadeOut">
                    <a class="dropdown-item waves-light waves-effect comment_yes" data-id="<?=$addition->id?>" href="javascript:void(0);"><?= Yii::t('backend', 'Yes'); ?></a>
                    <a class="dropdown-item waves-light waves-effect comment_no" data-id="<?=$addition->id?>" href="javascript:void(0);"><?= Yii::t('backend', 'No'); ?></a>
                </div>
            </div>
        </div>
        <div class="col-sm-4 col-xl-4 col-md-4 ">
            <textarea rows="5" cols="5" name="Projects[additions][<?=$addition->id?>][comment]" class="form-control
            hidden text_<?=$addition->id?>" placeholder="<?= Yii::t('backend', 'Dtc items'); ?>"></textarea>
        </div>
    </div>
<?php } ?>
</div>



