<?php

use common\models\ChipAddition;
use common\models\ChipBrand;
use common\models\ChipEngine;
use common\models\ChipGeneration;
use common\models\ChipModel;
use kartik\widgets\DepDrop;
use kartik\widgets\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\bootstrap\ActiveForm;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $model common\models\ChipEcu */
/* @var $form yii\bootstrap\ActiveForm */
/* @var $viewType string */
//echo '<pre>';
//print_r($model->quickAdditionsList);
//die;
switch ($viewType) {
    case 'quick':
        if (isset($model->quickAdditionsList)) {
            $model->additionsList = $model->quickAdditionsList;
        }
        break;
}
?>
<?php foreach (ChipAddition::find()->all() as $addition) { ?>

    <label for="addit_del_<?=$addition->id?>" class="col-sm-2 col-xl-2 col-md-2"><input type="checkbox"  name="ChipPrice[additions][]" id="addit_del_<?=$addition->id?>" value="<?=$addition->id?>" class="js-switch"/> <?=$addition->title?></label>
<?php } ?>
