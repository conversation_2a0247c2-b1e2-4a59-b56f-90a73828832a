<?php

use common\models\ChipAddition;
use common\models\ChipBrand;
use common\models\ChipEngine;
use common\models\ChipGeneration;
use common\models\ChipModel;
use common\models\ChipStages;
use kartik\widgets\DepDrop;
use kartik\widgets\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\bootstrap\ActiveForm;
use yii\helpers\Url;
use yii\web\View;

/* @var $this yii\web\View */
/* @var $models common\models\ChipEcuStages[] */
/* @var $form yii\bootstrap\ActiveForm */
/* @var $stagesDict common\models\ChipStages[] */
/* @var $tarifsDict common\models\ChipTarifs[] */
/* @var $ecuStagesDataArray array */
/* @var $stagesEcuDataArray array */
/* @var $firstStageSelect integer */
//echo '<pre>';
//print_r($dinostend_file);
//die;
$script = <<< JS
    // $(document).on('click', '.dino_file', function (e) {
    //     console.log(e);
    //     // var image = $(this);
    //     // $('.modal-body').html(image);
    //     // $('#ajaxCrudModal').modal('show');
    // });

   //  $(document)('.dino_file').click(function(){
   //      var image = $(this);
   //      console.log(image);
   //      $('.modal-body').html($(this));
   //      $('#ajaxCrudModal').modal('show');
   // });

JS;
//маркер конца строки, обязательно сразу, без пробелов и табуляции
$this->registerJs($script, yii\web\View::POS_END);

?>
<div class="row m-b-10 p-t-10 p-b-10">
    <div class="col-sm-12 col-xl-12 col-md-12 m-b-10 m-t-10 m-l-10">
    <!-- Nav tabs -->
        <ul class="nav b-none nav-pills" role="">
            <?php foreach ($stagesDict as $keyStage => $stage) { ?>
            <?php if (isset($stagesEcuDataArray[$stage->id])) { ?>
                <li class="nav-item <?= ($keyStage == 1) ? 'active' : ''?>">
                    <a class="nav-link btn btn-default stage_id" data-id="<?=$stage->id?>" data-toggle="tab"
                       href="#home<?=$stage->id?>" role="tab" aria-expanded="false"><?=$stage->title?></a>
                </li>
            <?php } ?>
            <?php } ?>
        </ul>
    </div>
    <!-- Tab panes -->
    <div class="col-sm-12 col-xl-12 col-md-12 m-b-10 m-t-10">
        <div class="tab-content">
            <?php foreach ($stagesDict as $keyStage => $stage) { ?>
            <?php if (isset($stagesEcuDataArray['standard'])) { ?>

                <?php if (!isset($stagesEcuDataArray[$stage->id])) { continue; } ?>
                <div class="tab-pane <?= ($keyStage == 1) ? 'active' : ''?>" id="home<?=$stage->id?>" role="tabpanel" aria-expanded="false">
                    <table class="table params-table">
                        <thead>
                        <tr>
                            <th></th>
                            <th class="params-head"><?= Yii::t('backend', 'Before'); ?></th>
                            <th class="params-head"><?= Yii::t('backend', 'After'); ?></th>
                            <th class="params-head"><?= Yii::t('backend', 'Difference'); ?></th>
                        </tr>
                        </thead>
                        <tbody>
                                <tr class="">
                                    <td class="params-head text-left"><?= Yii::t('backend', 'Power'); ?></td>
                                    <td class="params-data params-before"><?=(int)$stagesEcuDataArray['standard']->inc_hp?> <?= Yii::t('backend', 'HP'); ?></td>
                                    <td class="params-data params-after"><?=(int)$stagesEcuDataArray[$stage->id]->inc_hp?> <?= Yii::t('backend', 'HP'); ?></td>
                                    <td class="params-data params-after">+<?=(int)$stagesEcuDataArray[$stage->id]->inc_hp - (int)$stagesEcuDataArray['standard']->inc_hp ?> <?= Yii::t('backend', 'HP'); ?></td>
                                </tr>
                                <tr class="">
                                    <td class="params-head text-left"><?= Yii::t('backend', 'Torque'); ?></td>
                                    <td class="params-data params-before"><?=(int)$stagesEcuDataArray['standard']->inc_tork?> <?= Yii::t('backend', 'NM'); ?></td>
                                    <td class="params-data params-after"><?=(int)$stagesEcuDataArray[$stage->id]->inc_tork?> <?= Yii::t('backend', 'NM'); ?></td>
                                    <td class="params-data params-after">+<?=(int)$stagesEcuDataArray[$stage->id]->inc_tork - (int)$stagesEcuDataArray['standard']->inc_tork ?> <?= Yii::t('backend', 'NM'); ?></td>
                                </tr>
                        </tbody>
                    </table>
                    <div class="col-sm-12 col-xl-12 col-md-12 m-b-10 m-t-10 sub-title">
                         <?=!empty($stagesEcuDataArray[$stage->id]->comment) ?
                             '<i class="fa fa-exclamation-triangle mytooltip text-warning fa-2x"></i> '.$stagesEcuDataArray[$stage->id]->comment: ''; ?>
                    </div>
                    <?php if ($stagesEcuDataArray[$stage->id]->dinostend_file) {?>
                        <div class="col-sm-12 col-xl-12 col-md-12 m-b-10 m-t-10">
                            <a href="<?php echo Url::to('/uploads/dinostend/'.$stagesEcuDataArray[$stage->id]->dinostend_file, true);?>" data-toggle="lightbox" data-title="<?php echo $stage->title?>" data-footer="<?php echo $stage->title?>">
                                <img style="max-width: 100%; max-height: 362px" src="<?php echo Url::to('/uploads/dinostend/'.$stagesEcuDataArray[$stage->id]->dinostend_file, true);?>" class="img-fluid m-b-10">
                            </a>
                        </div>
                    <?php } ?>
                </div>
            <?php } ?>
            <?php } ?>
        </div>
    </div>
</div>



