<?php

use common\models\ChipAddition;
use common\models\ChipBrand;
use common\models\ChipEngine;
use common\models\ChipGeneration;
use common\models\ChipModel;
use common\models\ChipStages;
use kartik\widgets\DepDrop;
use kartik\widgets\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\bootstrap\ActiveForm;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $models common\models\ChipEcuStages[] */
/* @var $form yii\bootstrap\ActiveForm */
/* @var $stagesDict common\models\ChipStages[] */
/* @var $tarifsDict common\models\ChipTarifs[] */
/* @var $viewType string */
//echo '<pre>';
//print_r($priceData);
//die;
?>
        <?php foreach (ChipStages::find()->where('title != "Standard"')->all() as $keyStage => $stage) { ?>
            <div class="z-depth-top-0 m-b-10 p-t-10 p-b-10 <?=$viewType=='full' ? ' col-sm-3 col-xl-3 col-md-3 ' : ' row '?>">
                <div class="row col-sm-12 col-xl-12 col-md-12">
                    <div class="col-sm-3 col-xl-3 col-md-3 m-b-10">
                        <?= Yii::t('backend', 'On')?>/<?= Yii::t('backend', 'Off')?>
                        <input type="checkbox"  name="ChipEcuAdditions[stages][<?=$stage->id?>][stage_id]" id="stage_del_<?=$stage->id?>" value="<?=$stage->id?>" class="js-switch"/>
                    </div>
                    <div class="col-sm-4 col-xl-4 col-md-4 m-b-10">
                        <label for="stage_del_<?=$stage->id?>" class="text-dark"><h3><?=$stage->title?></h3></label>
                    </div>
                    <div class="col-sm-5 col-xl-5 col-md-5 m-b-10 p-l-0  p-r-0 text-center">
                        <label for="dinostend_files_stage_del_<?=$stage->id?>" class="text-dark"><h3><?= Yii::t('backend', 'Dinostend file')?></h3></label>
                        <input type="checkbox"  name="dinostend_files[<?=$stage->id?>]" id="dinostend_files_stage_del_<?=$stage->id?>" value="<?=$stage->id?>" class="js-switch"/>
                    </div>
                </div>
            </div>
        <?php } ?>



