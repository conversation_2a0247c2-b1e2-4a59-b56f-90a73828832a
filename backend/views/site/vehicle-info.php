<?php

use common\models\ChipReadmethod;
use common\models\ChipVehicle;
use kartik\widgets\DepDrop;
use kartik\widgets\Select2;
use yii\bootstrap\ActiveForm;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;

/* @var $this yii\web\View */
/* @var $name string */
/* @var $message string */
/* @var $exception Exception */
$script = <<< JS
    $(document).on('select2:open', () => {
        document.querySelector('.select2-search__field').focus();
      });
    $('#ecu_stages_form').on('beforeValidate', function (e) {
        console.log('beforeValidate');
    });
    function getProjectAllData(params) {
        clearInfo();
        params.lite = 1;
        $.ajax({
            method: "POST",
            url: "/ctadmin/projects/get-all-data",
            data: params,
        })
            .done(function( msg ) {
                $('#stages_div').html(msg.priceHtml);
                $('#ecu_div').html(msg.ecuHtml);
                // initSwithers();
                // console.log();
                // alert( "Data Saved: " + msg );
                // initSwithers();
            });

    }
    
    function getImg(genId) {
         var params = {};
       params.id = genId;
        $.ajax({
            method: "GET",
            url: "/ctadmin/site/get-img",
            data: params,
        })
            .done(function( msg ) {
                $('.img-block').html(msg);
            });
    }
    
    function clearInfo() {
            $('#stages_div').html('');
            $('#ecu_div').html('');
    }

    function getStages() {
        console.log('getStages');
        var params = {};
        params.vehicle_id = $("#vehicle_id").val();
        params.brand_id = $("#brand_id").val();
        
        params.model_id = $("#model_id").val();
        // console.log(model_id);
        
        params.generation_id = $("#generation_id").val();
        // console.log(generation_id);
        
        params.engine_id = $("#engine_id").val();
        // console.log(engine_id);
        
        params.ecu_id = $("#ecu_id").val();
        // console.log(ecu_id);
        console.log(params);
        
         // getStagesList(params);
         getProjectAllData(params);
    }
        // установка бэкграунда
    $('#pcoded').attr('sidebar-img-type','auto1');
    


JS;
//маркер конца строки, обязательно сразу, без пробелов и табуляции
$this->registerJs($script, yii\web\View::POS_READY);

$this->title = Yii::t('backend', 'Vehicle Information');
$this->params['breadcrumbs'][] = $this->title;
$model = new \common\models\Projects();

?>

    <?php $form = ActiveForm::begin([
        'id'                   => 'projectForm',
        'options'              => ['accept-charset'=>'utf-8'],
        'enableAjaxValidation' => false,
    ]); ?>
    <div class="row vehicle-info">
        <div class="col-md-12 col-lg-12 col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h5><?=Yii::t('backend', 'Vehicle information')?></h5>
                </div>
                <div class="card-block b-t-default p-t-10">
                    <div class="row">
                        <div class="col-md-2 col-lg-2 col-sm-4 img-block">
                        </div>
                        <div class="col-md-2 col-lg-2 col-sm-4">
                            <?= $form->field($model, 'brand_id')->widget(Select2::classname(), [
                                'theme' => Select2::THEME_KRAJEE,
                                'language' => 'ru',
                                'data' => ArrayHelper::map(common\models\ChipBrand::find()->notDeleted()->orderBy('title')->all(),'id', 'title'),
                                'options' => [
                                    'placeholder' => Yii::t('backend', 'Select'),
                                    'id'=>'brand_id'
                                ],
                                'pluginEvents' => [
                                    "change" => "function() { console.log(this.value); clearInfo(); }",
                                ],
                                'pluginOptions' => [
                                    'allowClear' => true,
                                    'initialize' => true,
                                ],
                            ]); ?>
                        </div>
                        <div class="col-md-2 col-lg-2 col-sm-4">
                            <?= $form->field($model, 'model_id')->widget(DepDrop::classname(), [
                                'type' => DepDrop::TYPE_SELECT2,
                                'language' => 'ru',
                                'options' => [
                                    'placeholder' => Yii::t('backend', 'Select'),
                                    'id'=>'model_id'
                                ],
                                'pluginEvents' => [
                                    "change" => "function() { console.log(this.value); clearInfo(); }",
                                ],
                                'select2Options' => ['pluginOptions' => ['allowClear' => true]],
                                'pluginOptions' => [
                                    'depends' => ['brand_id'],
                                    'placeholder' => Yii::t('backend', 'Select Brand first'),
                                    'url' => Url::to(['/chip-model/json-list']),
                                ],
                            ]); ?>
                        </div>
                        <div class="col-md-2 col-lg-2 col-sm-4">
                            <?= $form->field($model, 'generation_id')->widget(DepDrop::classname(), [
                                'type' => DepDrop::TYPE_SELECT2,
                                'language' => 'ru',
                                'options' => [
                                    'placeholder' => Yii::t('backend', 'Select'),
                                    'id'=>'generation_id'
                                ],
                                'pluginEvents' => [
                                    "change" => "function() { if (this.value) {console.log(this.value); clearInfo(); getImg(this.value);}}",
                                ],
                                'select2Options' => ['pluginOptions' => ['allowClear' => true]],
                                'pluginOptions' => [
                                    'depends'=>['model_id'],
                                    'placeholder' => Yii::t('backend', 'Select Model first'),
                                    'url'=>Url::to(['/chip-generation/json-list']),
                                ],
                            ]); ?>
                        </div>
                        <div class="col-md-2 col-lg-2 col-sm-4">
                            <?= $form->field($model, 'engine_id')->widget(DepDrop::classname(), [
                                'type' => DepDrop::TYPE_SELECT2,
                                'language' => 'ru',
                                'options' => [
                                    'placeholder' => Yii::t('backend', 'Select'),
                                    'id'=>'engine_id'
                                ],
                                'pluginEvents' => [
                                    "change" => "function() { console.log(this.value); clearInfo(); }",
                                ],
                                'select2Options' => ['pluginOptions' => ['allowClear' => true]],
                                'pluginOptions' => [
                                    'depends'=>['generation_id'],
                                    'placeholder' => Yii::t('backend', 'Select Generation first'),
                                    'url'=>Url::to(['/chip-engine/json-list']),
                                ],
                            ]); ?>
                        </div>
                        <div class="col-md-2 col-lg-2 col-sm-4">
                            <?= $form->field($model, 'ecu_id')->widget(DepDrop::classname(), [
                                'type' => DepDrop::TYPE_SELECT2,
                                'language' => 'ru',
                                'options' => [
                                    'placeholder' => Yii::t('backend', 'Select'),
                                    'id'=>'ecu_id'
                                ],
                                'pluginEvents' => [
                                    "change" => "function() { console.log(this.value); getStages(); }",
                                ],
                                'select2Options' => ['pluginOptions' => ['allowClear' => true]],
                                'pluginOptions' => [
                                    'depends'=>['engine_id'],
                                    'placeholder' => Yii::t('backend', 'Select Engine first'),
                                    'url'=>Url::to(['/chip-ecu/json-list']),
                                ],
                            ]); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6 col-lg-6 col-sm-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="m-l-10"><?=Yii::t('backend', 'Info')?></h5>
                </div>
                <div class="card-block b-t-default p-t-10">
                    <div class="row">
                        <div class="col-md-12 col-lg-12 col-sm-12" id="stages_div"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-6 col-sm-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="m-l-15"><?=Yii::t('backend', 'Options')?></h5>
                </div>
                <div class="card-block b-t-default p-t-10">
                    <div class="row">
                        <div class="col-md-12 col-lg-12 col-sm-12" id="ecu_div"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php ActiveForm::end(); ?>

