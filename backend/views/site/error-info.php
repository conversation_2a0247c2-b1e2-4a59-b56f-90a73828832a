<?php
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use yii\captcha\Captcha;

/* @var $this yii\web\View */
/* @var $letters array */
/* @var $models \common\models\ChipObdError


 */
$this->registerJs("
    $('#errorsTabs').tab('show')
    // установка бэкграунда
//    $('#errorsTabs a').on('click', function (e) {
//        e.preventDefault()
//        $(this).tab('show')
//    })
    $('.searchErrorInput').on('input', function (e) {
        let tab = e.target.dataset.tabid
        let text = e.target.value
        console.log(tab)
        console.log(text)
        var regex = new RegExp(text, 'i');
        
        $('#letter'+tab).find('.errorItem').each(function(){
//            console.log(index);
//            console.log(item);
            if ($(this).text().search(regex) < 0) {
                $(this).hide();
            } else {
                $(this).show();
//                count++;
            } 
            
//                       if ($(this).filter('[data-search-term *= ' + searchTerm + ']').length > 0 || searchTerm.length < 1) {
//                $(this).show();
//            } else {
//                $(this).hide();
//            }
//            if ($( this ).text().toLowerCase().indexOf( text.toLowerCase() ) >= 0) {
//                $( this ).css('background-color', 'red');
//            }
        });
//        e.preventDefault()
//        $(this).tab('show')
    })
    
");

$this->title = 'DTC help';
$this->params['breadcrumbs'][] = $this->title;
?>

    <!-- tabs card start -->
    <div class="col-sm-12">
        <div class="card tabs-card">
            <div class="card-block p-0">
                <!-- Nav tabs -->
                <ul class="nav nav-tabs md-tabs" id="errorsTabs" role="tablist">
                    <?php foreach ($letters as $key => $letter) { ?>
                    <li class="nav-item">
                        <a class="nav-link f-18 <?= $key == 0 ? 'active' : ''?>" data-toggle="tab" href="#letter<?=$key?>" role="tab"><?=$letter?></a>
                    </li>
                    <?php } ?>
                </ul>
                <!-- Tab panes -->
                <div class="tab-content card-block">
                    <?php foreach ($letters as $key => $letter) { ?>
                        <div class="tab-pane <?= $key == 0 ? 'active' : ''?>" id="letter<?=$key?>" role="tabpanel">
                            <div class="row">
                                <div class="col-md-12 m-b-10 m-t-10 f-24 text-center"><input type="text" class="searchErrorInput" data-tabid="<?=$key?>" placeholder="Error code"/></div>
                            </div>

                            <div class="row">
                            <ul>
                                <?php foreach ($models[$letter] as $model) {?>
                                    <li class="errorItem">
                                        <div class="row">
                                            <div class="error_title col-md-2 f-24"><?=$model['title']?></div>
                                            <div class="error_description col-md-10 m-b-10 m-t-10 f-14"><?=$model['description']?></div>
                                        </div>
                                    </li>
                                <?php } ?>
                            </ul>
                            </div>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>
