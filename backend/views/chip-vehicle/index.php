<?php

use yii\helpers\Html;
use backend\widgets\GridView;

/* @var $this yii\web\View */
/* @var $searchModel common\models\ChipVehicleSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = Yii::t('backend', 'Chip Vehicles');
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="chip-vehicle-index">

    <?php // echo $this->render('_search', ['model' => $searchModel]); ?>

    <p>
        <?php echo Html::a(Yii::t('backend', 'Create {modelClass}', [
    'modelClass' => 'Chip Vehicle',
]), ['create'], ['class' => 'btn btn-success']) ?>
    </p>

    <?php echo GridView::widget([
        'bsVersion' => '4.x',
        'id'=>'crud-datatable',
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'as filterBehavior' => \thrieu\grid\FilterStateBehavior::className(),
        'exportConfig' => [\kartik\grid\GridView::EXCEL => true, \kartik\grid\GridView::PDF => true],
        'pjax' => true,
        'striped' => true,
        'condensed' => true,
        'clearFilter' => true,
        'responsive' => true,
        'panel' => [
//                'type' => 'success',
            'heading' => '<i class="glyphicon glyphicon-list"></i> ' . Yii::t('backend', 'Chip Vehicles'),
        ],
        'columns' => [
//            ['class' => 'yii\grid\SerialColumn'],

//            'id',
            'title',
//            'created_at',
//            'deleted_at',
//            'updated_at',
            // 'deleted_by',

            ['class' => 'yii\grid\ActionColumn'],
        ],
    ]); ?>

</div>
