<?php
/**
 * @var $this yii\web\View
 * @var $content string
 */

use backend\modules\system\models\SystemLog;
use backend\widgets\Menu;
use common\models\ProjectMessages;
use common\models\TimelineEvent;
use yii\bootstrap\Alert;
use kartik\nav\NavX;
use yii\bootstrap\Modal;
use yii\bootstrap\Nav;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\log\Logger;
use yii\widgets\Breadcrumbs;
use kartik\widgets\AlertBlock;
use yii\widgets\Pjax;
use \common\assets\AdminAsset;
use johnitvn\ajaxcrud\CrudAsset;
//$appBundle = \common\assets\AppAsset::register($this);
//var_dump(Yii::$app->user->identity->getChipTools());
//phpinfo();
//die;
//print_r($this->params['messages']);
//die;

//$bundle = AdminAsset::register($this);
$bundle2 = \common\assets\ChipAsset::register($this);
$bundle1 = CrudAsset::register($this);
Yii::info(Yii::$app->components["i18n"]["translations"]['*']['class'], 'test');
//$this->params['user'] = 'customValue';
if (!isset($this->params['messages'])) {
    $this->params['messages'] =[];
}
?>
<style>
    .pcoded-inner-navbar {overflow: auto;}
</style>
<?php $this->beginContent('@backend/views/layouts/admin-base.php'); ?>
<!-- Pre-loader start -->

<div class="theme-loader">
    <div class="loader-track">
        <div class="loader-bar"></div>
    </div>
</div>
<div id="pcoded" class="pcoded">
    <div class="pcoded-overlay-box"></div>
    <div class="pcoded-container navbar-wrapper">
        <nav class="navbar header-navbar pcoded-header" >
            <div class="navbar-wrapper">
                <div class="navbar-logo">
                    <a class="mobile-menu" id="mobile-collapse" href="#!">
                        <i class="ti-menu"></i>
                    </a>
                    <a href="<?php echo Yii::$app->urlManagerFrontend->createAbsoluteUrl('/') ?>">
                        <img class="img-fluid" src="<?=Yii::getAlias('@chipassets')?>/images/logo.png" alt="<?=$this->title?>" />
                    </a>
                    <a class="mobile-options">
                        <i class="ti-more"></i>
                    </a>
                </div>

                <div class="navbar-container container-fluid">
                    <ul class="nav-left">
                        <li>
                            <div class="sidebar_toggle"><a href="javascript:void(0)"><i class="ti-menu"></i></a></div>
                        </li>
                        <li>
                            <a href="#!" onclick="javascript:toggleFullScreen()">
                                <i class="ti-fullscreen"></i>
                            </a>
                        </li>
                    </ul>

                    <?php echo \kartik\nav\NavX::widget([
                        'options' => ['class' => ['widget'=>'nav-right'],],
                        'encodeLabels' => false,
                        'items' => [
                            ['label' => Yii::t('frontend', 'Signup'), 'url' => ['/user/sign-in/signup'], 'visible'=>Yii::$app->user->isGuest],
                            ['label' => Yii::t('frontend', 'Login'), 'url' => ['/user/sign-in/login'], 'visible'=>Yii::$app->user->isGuest],
//                            ['label' => '<i class="fa fa-lock"></i> '.Yii::t('backend', 'Balance').': '.(int)Yii::$app->balanceManager->calculateBalance(['user_id' => Yii::$app->user->identity->id]) . ' Credits'],
                            [
                                'label' => Yii::$app->user->identity->username,
                                'visible'=>!Yii::$app->user->isGuest,
                                'options' => ['class' => 'user-profile header-notification'],
                                'items'=>[
                                    [
                                        'linkOptions' => ['class' => 'text-dark'],
                                        'label' => '<i class="ti-settings"></i>' . Yii::t('backend', 'Account'),
                                        'url' => ['/sign-in/account']
                                    ],
                                    [
                                        'linkOptions' => ['class' => 'text-dark', 'data-method' => 'post'],
                                        'label' => '<i class="ti-layout-sidebar-left"></i>' . Yii::t('backend', 'Logout'),
                                        'url' => Yii::$app->urlManagerFrontend->createUrl(['/user/sign-in/logout']),
                                    ]
                                ]
                            ],
                        ]
                    ]); ?>
                    <ul class="nav-right">
                        <li class="header-notification">
                            <a href="<?=Yii::$app->user->can('administrator') ? Url::to(['/user-messages/index']) : Url::to(['/user-messages/list'])?>" title="Notifications">
                                <i class="ti-bell hidden"></i>
                                <?= 0 < \common\models\UserMessagesUsers::find()->where(['user_id' => Yii::$app->user->identity->id, 'readed' => 0])->count() ? '<span class="badge bg-c-pink">'.\common\models\UserMessagesUsers::find()->where(['user_id' => Yii::$app->user->identity->id, 'readed' => 0])->count().'</span>' : ''?>
                            </a>
                        </li>
<!--                        --><?php //if (Yii::$app->user->can('manager')) { ?>
                            <li class="header-notification">
                                <?php Pjax::begin([ 'id' => 'message_list_top']);
                                echo $this->render('@backend/views/partial/message_list_top', ['messages' => \common\helpers\MessageHelper::getSupportMessages()]);
                                Pjax::end(); ?>
                            </li>
<!--                        --><?php //}?>
                    </ul>
                        <?php Pjax::begin([ 'id' => 'message_main_top']); ?>
                        <?= $this->render('@backend/views/partial/message_main_top', ['messages' => \common\helpers\MessageHelper::getMessages()]);?>
                        <?php Pjax::end(); ?>
                </div>
            </div>
        </nav>
        <div class="pcoded-main-container" >
            <div class="pcoded-wrapper">
                <nav class="pcoded-navbar">
                    <div class="sidebar_toggle"><a href="#"><i class="icon-close icons"></i></a></div>
                    <div class="pcoded-inner-navbar main-menu">
                    <?php echo Menu::widget([
                        'options' => ['class' => 'pcoded-item pcoded-left-item sidebar-menu tree', 'data' => ['widget' => 'tree']],
                        'linkTemplate' => '<a href="{url}"><span class="pcoded-micon">{icon}</span><span class="pcoded-mtext">{label}</span><span class="pcoded-mcaret"></span>{right-icon}{badge}</a>',
                        'submenuTemplate' => "\n<ul class=\"pcoded-submenu\">\n{items}\n</ul>\n",
                        'activateParents' => false,
                        'items' => [
                            [
                                'label' => Yii::t('backend', 'Home'),
                                'url' => ['/'],
                                'options' => ['class' => 'header text-center text-muted'],
                                'active' => ($this->context->route == 'timeline-event/index'),
                            ],
                            [
                                'label' => Yii::t('backend', 'Vehicle Info'),
//                                'icon' => '<i class="ti-tablet"></i>',
                                'url' => ['/site/vehicle-info'],
                                'active' => ($this->context->route == 'site/vehicle-info'),
                                'badgeBgClass' => 'label-success',
                            ],
                            [
                                'label' => Yii::t('backend', 'New Project'),
                                'url' => ['/projects/create'],
//                                'icon' => '<i class="fa fa-map-pin"></i>',
                                'active' => ($this->context->route == 'projects/create'),
                                'visible'=>!Yii::$app->user->can('manager')

//                                'active' => (Yii::$app->controller->id == 'projects'),
                            ],
                            [
                                'label' => Yii::t('backend', 'Project history'),
                                'url' => ['projects/index'],
//                                'icon' => '<i class="fa fa-map-pin"></i>',
                                'active' => ($this->context->route == 'projects/index'),
                            ],
                            [
                                'label' => Yii::t('backend', 'DTC help'),
                                'url' => ['/site/error-info'],
//                                'icon' => '<i class="fa fa-map-pin"></i>',
                                'active' => ($this->context->route == 'site/error-info'),
                            ],
                            [
//                                                                        <a href="javascript:void(0)">
//                                        <span class="pcoded-micon"><i class="ti-layout-grid2-alt"></i><b>BC</b></span>
//                                        <span class="pcoded-mtext">Basic</span>
//                                        <span class="pcoded-mcaret"></span>
//                                    </a>
//                                'right-icon' => '<i class="ti-tablet"></i>',
//                                'url' => ['javascript:void(0)'],
                                'label' => Yii::t('backend', 'Catalogs'),
//                                'icon' => '<i class="ti-receipt"></i>',
                                'options' => ['class' => 'pcoded-hasmenu f-16 text-center text-danger'],
                                'visible'=>Yii::$app->user->can('manager'),
                                'url' => '#',
                                'items' => [
                                    [
                                        'label' => Yii::t('backend', 'Brands'),
                                        'url' => ['/chip-brand/index'],
//                                'icon' => '<i class="fa fa-car"></i>',
                                        'active' => (Yii::$app->controller->id == 'chip-brand'),
                                        'visible'=>Yii::$app->user->can('manager')
                                    ],
                                    [
                                        'label' => Yii::t('backend', 'Models'),
                                        'url' => ['/chip-model/index'],
//                                'icon' => '<i class="fa fa-car"></i>',
                                        'active' => (Yii::$app->controller->id == 'chip-model'),
                                        'visible'=>Yii::$app->user->can('manager')
                                    ],
                                    [
                                        'label' => Yii::t('backend', 'Generations'),
                                        'url' => ['/chip-generation/index'],
//                                'icon' => '<i class="fa fa-car"></i>',
                                        'active' => (Yii::$app->controller->id == 'chip-generation'),
                                        'visible'=>Yii::$app->user->can('manager')
                                    ],
                                    [
                                        'label' => Yii::t('backend', 'Engines'),
                                        'url' => ['/chip-engine/index'],
//                                'icon' => '<i class="fa fa-car"></i>',
                                        'active' => (Yii::$app->controller->id == 'chip-engine'),
                                        'visible'=>Yii::$app->user->can('manager')
                                    ],
                                    [
                                        'label' => Yii::t('backend', 'Chip Ecus'),
                                        'url' => ['/chip-ecu-dict/index'],
//                                'icon' => '<i class="fa fa-car"></i>',
                                        'active' => (Yii::$app->controller->id == 'chip-ecu-dict'),
                                        'visible'=>Yii::$app->user->can('manager')
                                    ],
                                    [
                                        'label' => Yii::t('backend', 'Vehicle types'),
                                        'url' => ['/chip-vehicle/index'],
//                                'icon' => '<i class="fa fa-map-pin"></i>',
                                        'active' => (Yii::$app->controller->id == 'chip-vehicle'),
                                        'visible'=>Yii::$app->user->can('manager')
                                    ],
                                    [
                                        'label' => Yii::t('backend', 'Stages'),
                                        'url' => ['/chip-stages/index'],
//                                'icon' => '<i class="fa fa-car"></i>',
                                        'active' => (Yii::$app->controller->id == 'chip-stages'),
                                        'visible'=>Yii::$app->user->can('manager')
                                    ],
                                    [
                                        'label' => Yii::t('backend', 'Additions'),
                                        'url' => ['/chip-addition/index'],
//                                'icon' => '<i class="fa fa-car"></i>',
                                        'active' => (Yii::$app->controller->id == 'chip-addition'),
                                        'visible'=>Yii::$app->user->can('manager')
                                    ],
                                    [
                                        'label' => Yii::t('backend', 'Gearbox list'),
                                        'url' => ['/chip-gearbox/index'],
//                                'icon' => '<i class="fa fa-car"></i>',
                                        'active' => (Yii::$app->controller->id == 'chip-gearbox'),
                                        'visible'=>Yii::$app->user->can('manager')
                                    ],
                                    [
                                        'label' => Yii::t('backend', 'Chiptuning tools'),
                                        'url' => ['/chip-readmethod/index'],
//                                'icon' => '<i class="fa fa-car"></i>',
                                        'active' => (Yii::$app->controller->id == 'chip-readmethod'),
                                        'visible'=>Yii::$app->user->can('manager')
                                    ],
                                ],
                            ],
//
//


                            [
                                'label' => Yii::t('backend', 'Management'),
                                'visible'=>Yii::$app->user->can('administrator'),
//                                'icon' => '<i class="ti-receipt"></i>',
                                'options' => ['class' => 'pcoded-hasmenu f-16 text-center text-danger'],
                                'url' => '#',
                                'items' => [
                                    [
                                        'label' => Yii::t('backend', 'Full Ecu Settings'),
                                        'url' => ['/chip-ecu/index'],
        //                                'icon' => '<i class="fa fa-car"></i>',
                                        'active' => ($this->context->route == 'chip-ecu/index'),
                                        'visible'=>Yii::$app->user->can('manager')
                                    ],
                                    [
                                        'label' => Yii::t('backend', 'Full Add form'),
                                        'url' => ['/chip-ecu/full-add'],
        //                                'icon' => '<i class="fa fa-car"></i>',
                                        'active' => ($this->context->route == 'chip-ecu/full-add'),
                                        'visible'=>Yii::$app->user->can('manager')
                                    ],
                                    [
                                        'label' => Yii::t('backend', 'Quick Ecu Settings'),
                                        'url' => ['/chip-ecu/quick'],
        //                                'icon' => '<i class="fa fa-language"></i>',
                                        'visible'=>Yii::$app->user->can('manager'),
                                        'active' => ($this->context->route == '/chip-ecu/quick'),
                                    ],
                                    [
                                        'label' => Yii::t('backend', 'Quick Ecu Delete Settings'),
                                        'url' => ['/chip-ecu/quick-delete'],
        //                                'icon' => '<i class="fa fa-language"></i>',
                                        'visible'=>Yii::$app->user->can('manager'),
                                        'active' => ($this->context->route == '/chip-ecu/quick-delete'),
                                    ],
                                    [
                                        'label' => Yii::t('backend', 'System'),
                                        'options' => ['class' => 'header'],
                                        'visible'=>Yii::$app->user->can('manager'),
                                    ],
                                    [
                                        'label' => Yii::t('backend', 'Users'),
        //                                'icon' => '<i class="fa fa-users"></i>',
                                        'url' => ['/user/index'],
                                        'active' => Yii::$app->controller->id === 'user',
                                        'visible'=>Yii::$app->user->can('administrator'),
                                    ],
                                    [
                                        'label' => Yii::t('backend', 'Opening hours'),
        //                                'icon' => '<i class="fa fa-users"></i>',
                                        'url' => ['/site/opening-management'],
                                        'active' => $this->context->route === '/site/opening-management',
                                        'visible'=>Yii::$app->user->can('administrator'),
                                    ],
                                ],

                            ],

                            [
                                'label' => Yii::t('backend', 'Autopack'),
                                'visible'=>Yii::$app->user->can('administrator'),
//                                'icon' => '<i class="ti-receipt"></i>',
                                'options' => ['class' => 'pcoded-hasmenu f-16 text-center text-danger'],
                                'url' => '#',
                                'items' => [
                                    [
                                        'label' => Yii::t('backend', 'Group'),
                                        'url' => ['/autopack/group'],
//                                'icon' => '<i class="fa fa-car"></i>',
                                        'active' => ($this->context->route == 'autopack/group'),
                                        'visible'=>Yii::$app->user->can('manager')
                                    ],
                                    [
                                        'label' => Yii::t('backend', 'Management'),
                                        'url' => ['/autopack/manage'],
//                                'icon' => '<i class="fa fa-car"></i>',
                                        'active' => ($this->context->route == 'autopack/manage'),
                                        'visible'=>Yii::$app->user->can('manager')
                                    ],
                                    [
                                        'label' => Yii::t('backend', 'Administration'),
                                        'url' => ['/autopack/admin'],
//                                'icon' => '<i class="fa fa-car"></i>',
                                        'active' => ($this->context->route == 'autopack/admin'),
                                        'visible'=>Yii::$app->user->can('manager')
                                    ],

                                ]
                            ],

                                    [
                                'label' => Yii::t('backend', 'News Admin'),
                                'url' => ['/content/article/index'],
//                                'icon' => '<i class="ti-write"></i>',
                                'options' => ['class' => 'treeview'],
                                'active' => Yii::$app->controller->id === 'article',
                                'visible'=>Yii::$app->user->can('manager'),
                            ],
                            [
                                'label' => Yii::t('backend', 'Terms & Conditions'),
//                                'icon' => '<i class="fa fa-users"></i>',
                                'url' => ['/site/terms'],
                                'active' => ($this->context->route == '/site/terms'),
                                'visible'=>Yii::$app->user->can('manager'),
                            ],
                            [
                                'label' => Yii::t('backend', 'Support'),
//                                'icon' => '<i class="fa fa-users"></i>',
                                'url' => Yii::$app->user->can('manager') ? ['/ticket/admin/index'] : ['/ticket/ticket/index'],
                                'active' => ($this->context->route == 'ticket/admin/index' || $this->context->route == 'ticket/ticket/index'),
//                                'visible'=>Yii::$app->user->can('manager'),
                            ],


                        ],

                    ]) ?>
                    </div>
                </nav>
                <div class="pcoded-content">
                    <div class="pcoded-inner-content">
                        <!-- Main-body start -->
                        <div class="main-body">
                            <section class="content <?=$this->params['contentClass']?>">
                                <?php if (Yii::$app->session->hasFlash('alert')): ?>
                                    <?php echo Alert::widget([
                                        'body' => ArrayHelper::getValue(Yii::$app->session->getFlash('alert'), 'body'),
                                        'options' => ArrayHelper::getValue(Yii::$app->session->getFlash('alert'), 'options'),
                                    ]) ?>
                                <?php endif; ?>
                                <?php echo $content ?>
                            </section><!-- /.content -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php Modal::begin([
    "id"=>"ajaxCrudModal",
    "footer"=>"",// always need it for jquery plugin
])?>
<?php Modal::end(); ?>

<?php $this->endContent(); ?>
