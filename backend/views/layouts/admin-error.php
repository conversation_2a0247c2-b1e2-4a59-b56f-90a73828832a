<?php
/**
 * @var $this yii\web\View
 * @var $content string
 */

use backend\modules\system\models\SystemLog;
use backend\widgets\Menu;
use common\assets\ErrorAsset;
use common\models\ProjectMessages;
use common\models\TimelineEvent;
use yii\bootstrap\Alert;
use kartik\nav\NavX;
use yii\bootstrap\Nav;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\log\Logger;
use yii\widgets\Breadcrumbs;
use kartik\widgets\AlertBlock;
use yii\widgets\Pjax;
//use \common\assets\AdminAsset;

//$appBundle = \common\assets\AppAsset::register($this);
//var_dump('123');
//phpinfo();
//die;
//$bundle = AdminAsset::register($this);
$bundle = ErrorAsset::register($this);
Yii::info(Yii::$app->components["i18n"]["translations"]['*']['class'], 'test');
//$this->params['user'] = 'customValue';
if (!isset($this->params['messages'])) {
    $this->params['messages'] =[];
}
?>

<style>
    .pcoded-inner-navbar {overflow: auto;}
</style>
<?php $this->beginContent('@backend/views/layouts/admin-base.php'); ?>
<!-- Pre-loader start -->

<a id="bgndVideo" class="player" data-property="{videoURL:'http://youtu.be/RdGVz104b3E',containment:'body',autoPlay:true, mute:false, startAt:0, stopAt:0, opacity:1}"></a>

<!-- Your logo on the top left -->
<a href="#" class="logo-link" title="back home">

    <img src="<?=Yii::getAlias('@chipassets')?>/images/logo.png" class="logo" alt="MS logo" />

</a>

<div class="content">

    <div class="content-box">

        <div class="big-content">

            <!-- Main squares for the content logo in the background -->
            <div class="list-square">
                <span class="square"></span>
                <span class="square"></span>
                <span class="square"></span>
            </div>

            <!-- Main lines for the content logo in the background -->
            <div class="list-line">
                <span class="line"></span>
                <span class="line"></span>
                <span class="line"></span>
                <span class="line"></span>
                <span class="line"></span>
                <span class="line"></span>
            </div>

            <!-- The animated searching tool -->
            <i class="fa fa-search" aria-hidden="true"></i>

            <!-- div clearing the float -->
            <div class="clear"></div>

        </div>
        <?php echo $content?>

    </div>

</div>

<footer class="light">
    <ul>
        <li><a href="<?php echo Yii::$app->urlManagerBackend->createAbsoluteUrl('/') ?>">Home</a></li>
        <li>
            <form method="post" action="<?=Yii::$app->urlManagerFrontend->createUrl(['/user/sign-in/logout'])?>">
                <input type="hidden" name="_csrf" value="<?=Yii::$app->request->getCsrfToken()?>" />
                <input type="submit" class="btn btn-link text-light" value="Logout"/>
            </form>
        </li>
    </ul>
</footer>

<script>

</script>


<?php $this->endContent(); ?>
