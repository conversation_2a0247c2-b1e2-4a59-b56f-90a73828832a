<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $result string */

$this->title = 'Тест метода notifyFileUploaded';
$this->params['breadcrumbs'][] = ['label' => 'Тестирование NotificationFacade', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="notification-test-notify-file-uploaded">
    <h1><?= Html::encode($this->title) ?></h1>

    <div class="row">
        <div class="col-md-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">Параметры</h3>
                </div>
                <div class="panel-body">
                    <?php $form = ActiveForm::begin(); ?>

                    <div class="form-group">
                        <label for="project_id">ID проекта</label>
                        <input type="number" class="form-control" id="project_id" name="project_id" value="1">
                    </div>

                    <div class="form-group">
                        <label for="file_id">ID файла</label>
                        <input type="number" class="form-control" id="file_id" name="file_id" value="1">
                    </div>

                    <div class="form-group">
                        <label for="file_name">Имя файла</label>
                        <input type="text" class="form-control" id="file_name" name="file_name" value="test.bin">
                    </div>

                    <div class="form-group">
                        <label for="comment">Комментарий</label>
                        <textarea class="form-control" id="comment" name="comment" rows="3">Test file comment</textarea>
                    </div>

                    <div class="form-group">
                        <label for="user_role">Роль пользователя</label>
                        <select class="form-control" id="user_role" name="user_role">
                            <option value="manager">Менеджер</option>
                            <option value="admin">Администратор</option>
                            <option value="client">Клиент</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">Отправить уведомление</button>
                        <?= Html::a('Назад', ['index'], ['class' => 'btn btn-default']) ?>
                    </div>

                    <?php ActiveForm::end(); ?>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">Результат</h3>
                </div>
                <div class="panel-body">
                    <?= $result ?>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">Описание метода</h3>
                </div>
                <div class="panel-body">
                    <p>Метод <code>notifyFileUploaded</code> отправляет уведомление о загрузке файла в проект.</p>
                    <p>Параметры:</p>
                    <ul>
                        <li><strong>project_id</strong> - ID проекта</li>
                        <li><strong>file_id</strong> - ID файла</li>
                        <li><strong>file_name</strong> - Имя файла</li>
                        <li><strong>comment</strong> - Комментарий к файлу</li>
                        <li><strong>user_role</strong> - Роль пользователя</li>
                        <li><strong>project_author_id</strong> - ID автора проекта (автоматически)</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
