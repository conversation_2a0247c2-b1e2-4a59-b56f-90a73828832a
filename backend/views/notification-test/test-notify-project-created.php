<?php
// backend/views/notification-test/test-notify-project-created.php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $result string */
/* @var $hasAsyncService bool */

$this->title = 'Тест метода notifyProjectCreated';
$this->params['breadcrumbs'][] = ['label' => 'Тестирование NotificationFacade', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="notification-test-notify-project-created">
    <h1><?= Html::encode($this->title) ?></h1>

    <div class="row">
        <div class="col-md-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">Параметры</h3>
                </div>
                <div class="panel-body">
                    <?php $form = ActiveForm::begin(); ?>

                    <div class="form-group">
                        <label for="project_id">ID проекта</label>
                        <input type="number" class="form-control" id="project_id" name="project_id" value="1">
                    </div>

                    <div class="form-group">
                        <label for="project_name">Название проекта</label>
                        <input type="text" class="form-control" id="project_name" name="project_name" value="Test Project">
                    </div>

                    <div class="form-group">
                        <label for="message">Сообщение</label>
                        <input type="text" class="form-control" id="message" name="message" value="Test message">
                    </div>

                    <div class="form-group">
                        <label for="content">Содержимое</label>
                        <textarea class="form-control" id="content" name="content" rows="3">Test content</textarea>
                    </div>

                    <?php if ($hasAsyncService): ?>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="async" value="1"> Отправить асинхронно
                        </label>
                        <p class="help-block">При включении этой опции уведомление будет обработано асинхронно через очередь задач.</p>
                    </div>
                    <?php endif; ?>

                    <!-- Новые поля для источника -->
                    <div class="form-group">
                        <label for="source_type">Тип источника</label>
                        <select class="form-control" id="source_type" name="source_type">
                            <option value="user">Пользователь</option>
                            <option value="service">Сервис</option>
                            <option value="system">Система</option>
                            <option value="external">Внешняя система</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="source_id">ID источника</label>
                        <input type="text" class="form-control" id="source_id" name="source_id" value="<?= Yii::$app->user->id ?>">
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">Отправить уведомление</button>
                        <?= Html::a('Назад', ['index'], ['class' => 'btn btn-default']) ?>
                    </div>

                    <?php ActiveForm::end(); ?>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">Результат</h3>
                </div>
                <div class="panel-body">
                    <?= $result ?>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">Описание метода</h3>
                </div>
                <div class="panel-body">
                    <p>Метод <code>notifyProjectCreated</code> отправляет уведомление о создании проекта.</p>
                    <p>Параметры:</p>
                    <ul>
                        <li><strong>id</strong> - ID проекта</li>
                        <li><strong>name</strong> - Название проекта</li>
                        <li><strong>message</strong> - Сообщение</li>
                        <li><strong>content</strong> - Содержимое</li>
                        <li><strong>createdBy</strong> - ID создателя проекта</li>
                        <li><strong>async</strong> - Флаг асинхронной обработки (true/false)</li>
                    </ul>

                    <?php if ($hasAsyncService): ?>
                    <div class="alert alert-info">
                        <strong>Асинхронная обработка доступна!</strong><br>
                        При включении опции "Отправить асинхронно" уведомление будет помещено в очередь задач и обработано в фоновом режиме.
                    </div>
                    <?php else: ?>
                    <div class="alert alert-warning">
                        <strong>Асинхронная обработка недоступна!</strong><br>
                        AsyncEventService не найден в контейнере зависимостей. Уведомления будут отправляться только синхронно.
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>