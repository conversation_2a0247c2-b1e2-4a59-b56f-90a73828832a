<?php

use yii\helpers\Html;

/* @var $this yii\web\View */
/* @var $result string */

$this->title = 'Тест метода getEventHandlerService';
$this->params['breadcrumbs'][] = ['label' => 'Тестирование NotificationFacade', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="notification-test-get-event-handler-service">
    <h1><?= Html::encode($this->title) ?></h1>

    <div class="row">
        <div class="col-md-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">Результат</h3>
                </div>
                <div class="panel-body">
                    <?= $result ?>
                    
                    <div class="form-group">
                        <?= Html::a('Назад', ['index'], ['class' => 'btn btn-default']) ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">Описание метода</h3>
                </div>
                <div class="panel-body">
                    <p>Метод <code>getEventHandlerService</code> возвращает экземпляр EventHandlerService, который используется для обработки событий.</p>
                    <p>Возвращает:</p>
                    <ul>
                        <li><strong>EventHandlerService</strong> - экземпляр класса EventHandlerService</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
