<?php
use yii\helpers\Url;
use yii\helpers\Html;
use yii\bootstrap\Modal;
use kartik\grid\GridView;
use johnitvn\ajaxcrud\CrudAsset; 
use johnitvn\ajaxcrud\BulkButtonWidget;

/* @var $this yii\web\View */
/* @var $searchModel common\models\search\UserMessagesSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = Yii::t('app', 'Messages');
$this->params['breadcrumbs'][] = $this->title;

CrudAsset::register($this);
$action = 'read';
if (Yii::$app->user->can('administrator')) {
    $action = 'update';
}
?>
<?php \yii\widgets\Pjax::begin() ?>
<div class="row">

    <div class="col-md-12 col-lg-12 col-sm-12">
        <div class="col-xl-12 col-md-12 col-sm-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-header-text"><i class="icofont icofont-ui-note m-r-10"></i> <?php echo Yii::t('backend', 'Messages') ?></h5>
                </div>
                <div class="card-block task-details">
                    <?php if ($dataProvider->count > 0): ?>
                        <?php foreach($dataProvider->getModels() as $model): ?>
                            <hr/>
                            <div class="article-item row">
                                <div class="col-xs-12">
                                    <h2 class="article-title m-b-25">
                                        <?php echo Html::a(!empty($model->message->title) ? $model->message->title : 'No data', Url::to(['user-messages/view', 'id' => $model->message->id])) ?>
                                    </h2>
                                    <div class="col-xl-10 col-md-10 col-sm-10 text-left p-l-0" style="line-height: 3;">
                                        <?php echo Yii::$app->formatter->asDatetime($model->created_at, "d-MM-y") ?>
                                    </div>
                                    <div class="col-xl-2 col-md-2 col-sm-2 text-right">
                                        <span class="text-dark btn btn-grd-primary"><?php echo Html::a(Yii::t('backend', 'Read more'), ['user-messages/view', 'id' => $model->message->id]) ?></span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <span class="text-dark"><?php echo Yii::t('backend', 'No messages yet') ?></span>
                    <?php endif; ?>
                </div>
                <div class="card-footer">
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-12 text-center">
        <?php echo \yii\widgets\LinkPager::widget([
            'pagination'=>$dataProvider->pagination,
            'options' => ['class' => 'pagination']
        ]) ?>
    </div>

    <?php \yii\widgets\Pjax::end() ?>
