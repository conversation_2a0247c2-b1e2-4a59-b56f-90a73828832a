<?php

use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\UserMessages */
?>
<div class="mail-body">
    <div class="mail-body-content email-read">
        <div class="card">
            <div class="card-header">
                <h5><?=!empty($model->title) ? $model->title : 'No title'?></h5>
                <h6 class="f-right"><?=Yii::$app->formatter->format($model->created_at, 'date')?></h6>
            </div>
            <div class="">
                <div class="media m-b-20 col-md-12">
                    <div class="media-body photo-contant">
                        <div>
                            <p class="email-content">
                                <?=$model->message?>
                            </p>
                        </div>
                        <?php if (count($model->files) > 0) { ?>
                        <div class="m-t-15">
                            <i class="icofont icofont-clip f-20 m-r-10"></i>Attachments <b>(<?=count($model->files)?>)</b>
                            <div class="row mail-img">
                                <?php foreach($model->files as $file) { ?>
                                    <div class="col-sm-4 col-md-2 col-xs-12">
                                        <a target="_blank" href="<?=$file->base_url.$file->path?>"><img class="card-img-top img-fluid img-thumbnail" src="<?=$file->base_url.$file->path?>" alt="<?=$file->name?>"></a>
                                    </div>
                                <?php } ?>
                            </div>
                        </div>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
