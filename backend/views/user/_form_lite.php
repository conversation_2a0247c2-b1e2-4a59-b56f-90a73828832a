<?php

use common\models\ChipEcuDict;
use common\models\ChipTarifs;
use common\models\User;
use kartik\widgets\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\bootstrap\ActiveForm;
use yii\helpers\Url;
use yii\web\JsExpression;
use yii\web\View;

/* @var $this yii\web\View */
/* @var $model backend\models\UserForm */
/* @var $form yii\bootstrap\ActiveForm */
/* @var $roles yii\rbac\Role[] */
/* @var $permissions yii\rbac\Permission[] */
/* @var $modelProfile \common\models\UserProfile */
$script = <<< JS
     setLayoutLight();
JS;
//маркер конца строки, обязательно сразу, без пробелов и табуляции
$this->registerJs($script, yii\web\View::POS_READY);
$url = \yii\helpers\Url::to(['/projects/find-clients1c']);
//if ($modelProfile !== null) {
//    Html::errorSummary($modelProfile, ['encode' => false]);
//}
?>
<?php $form = ActiveForm::begin(['id' => 'msform', 'method' => 'post', 'enableAjaxValidation' => true, 'enableClientValidation' => false]) ?>
<?= Html::errorSummary($model, ['encode' => false]) ?>

<div class="col-lg-12 col-xl-12">
    <!-- Tab panes -->
            <fieldset class="">
                <?php echo $form->field($model, 'email')->textInput(['placeholder' => 'Email']) ?>
                <?php echo $form->field($model, 'phone')->textInput(['placeholder' => 'Contact phone', ]) ?>
            </fieldset>

    <div class="form-group col-md-6">
        <?php echo Html::submitButton(Yii::t('frontend', 'Save'), ['class' => 'btn btn-primary', 'name' => 'save-button']) ?>
    </div>
</div>
<?php ActiveForm::end() ?>

