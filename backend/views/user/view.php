<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\User */

$this->title = $model->getPublicIdentity();
$this->params['breadcrumbs'][] = ['label' => Yii::t('backend', 'Users'), 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="row">
    <div class="col-lg-12 col-xl-12">
        <?php echo Html::a(Yii::t('backend', 'Update'), ['update', 'id' => $model->id], ['class' => 'btn btn-primary']) ?>
        <?php echo Html::a(Yii::t('backend', 'Delete'), ['delete', 'id' => $model->id], [
            'class' => 'btn btn-danger',
            'data' => [
                'confirm' => Yii::t('backend', 'Are you sure you want to delete this item?'),
                'method' => 'post',
            ],
        ]) ?>
    </div>
</div>
<p></p>
<div class="row">
    <div class="col-lg-12 col-xl-12">
        <!-- Nav tabs -->
        <ul class="nav nav-tabs  tabs" role="tablist">
            <li class="nav-item active">
                <a class="nav-link" data-toggle="tab" href="#tab1" role="tab"><?=Yii::t('backend', 'Information')?></a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-toggle="tab" href="#tab2" role="tab"><?=Yii::t('backend', 'Financial')?></a>
            </li>
        </ul>
        <!-- Tab panes -->
                <div class="card">
                    <div class="card-block">
                        <div class="tab-content tabs card-block">
                <div class="tab-pane active" id="tab1" role="tabpanel">
                        <div class="form-group row">
                            <?php echo DetailView::widget([
                                'model' => $model,
                                'attributes' => [
//                                    'id',
                                    'username',
//                                    'auth_key',
                                    'email:email',
                                    'status',
//                                    'created_at:datetime',
//                                    'updated_at:datetime',
                                    'logged_at:datetime',
                                ],
                            ]) ?>
                        </div>
                </div>
            <div class="tab-pane" id="tab2" role="tabpanel">
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label"><?=Yii::t('backend', 'Live user balance').': '?></label>
                    <div class="col-sm-4">
                        <?=(int)Yii::$app->balanceManager->calculateBalance(['user_id' => $model->id]) . ' Credits'?>
                    </div>
                </div>
                <?php foreach (Yii::$app->params['availableCreditSumToSend'] as $sum) { ?>
                    <div class="form-group row">
                            <div class="col-sm-8 col-lg-6">

                                <?php echo Html::a(Yii::t('backend', 'Send {sum} credits', ['sum' => $sum]), ['send-credits', 'id' => $model->id, 'sum' => $sum], [
                                    'class' => 'input-group-addon btn-success',
                                    'data' => [
                                        'confirm' => Yii::t('backend', 'Are you sure you want to send credits to this client?'),
                                        'method' => 'post',
                                    ],
                                ]) ?>
                            </div>
                    </div>
                <?php } ?>
            </div>
        </div>
                    </div>
                </div>
    </div>
</div>
