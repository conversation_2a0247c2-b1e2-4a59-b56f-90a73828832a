<?php

use app\modules\user\models\User;use common\models\ChipBrand;
use common\models\ChipEcuDict;
use common\models\ChipEngine;
use common\models\ChipGeneration;
use common\models\ChipModel;
use common\models\ChipReadmethod;
use common\models\ChipVehicle;
use kartik\widgets\DepDrop;
use kartik\widgets\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\bootstrap\ActiveForm;
use yii\helpers\Url;
use yii\web\View;

/* @var $this yii\web\View */
/* @var $model common\models\Projects */
/* @var $form yii\bootstrap\ActiveForm */

?>
<form action="<?=Url::to(['/projects/edit-note', 'id' => $model->id])?>" id="add_note_form" method="post" enctype="multipart/form-data">
    <input name="project_id" type="hidden" value="<?=isset($project_id) ? $project_id : $model->project_id?>"/>
    <input type="hidden" name="id" id="file_message_comment_id" value="<?=$model->id?>"/>
    <div class="form-group row">
        <div class="col-md-12 col-lg-12 col-sm-12">
            <textarea class="form-control" name="comment" id="note_comment"><?=$model->comment?></textarea>
        </div>
    </div>
    <div class="form-group row m-b-0">
        <div class="col-md-12 col-lg-12 col-sm-12">
            <p><?=Yii::t('backend', 'Optional File Attachment: AFR Logs, Dyno Charts or Photo')?></p>
            <p class="text-danger bold"><?=Yii::t('backend', 'Do not attach ECU Files')?></p>
        </div>
    </div>
    <div class="form-group row">
        <div class="col-md-12 col-lg-12 col-sm-12">
            <input type="file" class="" value="<?=$model->filename?>" id="note_attach" name="filename"/>
        </div>
    </div>
</form>
