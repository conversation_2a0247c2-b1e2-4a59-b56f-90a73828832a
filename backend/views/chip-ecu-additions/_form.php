<?php

use common\models\ChipAddition;
use common\models\ChipBrand;
use common\models\ChipEngine;
use kartik\widgets\DepDrop;
use kartik\widgets\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\bootstrap\ActiveForm;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $model common\models\ChipEcuAdditions */
/* @var $adds common\models\ChipAddition[] */
/* @var $form yii\bootstrap\ActiveForm */

//print_r($adds);
//die;
?>

<div class="card">
    <div class="card-block">

    <?php $form = ActiveForm::begin(); ?>

    <?php echo $form->errorSummary($model); ?>
        <div class="form-group">
            <?php echo Html::submitButton($model->isNewRecord ? Yii::t('backend', 'Save') : Yii::t('backend', 'Save'), ['class' => $model->isNewRecord ? 'btn btn-warning' : 'btn btn-warning']) ?>
        </div>

        <div class="row">
            <div class="col-sm-12 col-xl-5 col-md-5">
                <?= $form->field($model, 'brand_id')->widget(Select2::classname(), [
                    'theme' => Select2::THEME_KRAJEE,
                    'language' => 'ru',
                    'data' => ArrayHelper::map(common\models\ChipBrand::find()->orderBy('title')->all(),'id', 'title'),
                    'options' => [
                        'placeholder' => 'Выберите',
                        'id'=>'brand_id'
                    ],
                    'pluginEvents' => [
                        "change" => "function() { console.log(this.value); addBrandToEcu(this.value); }",
                    ],
                    'pluginOptions' => [
                        'allowClear' => true,
                        'initialize' => true,
                    ],
                ]); ?>

                <?= $form->field($model, 'ecu_id')->widget(DepDrop::classname(), [
                    'type' => DepDrop::TYPE_SELECT2,
                    'language' => 'ru',
                    'data' => ArrayHelper::map(common\models\ChipEcuDict::find()->leftJoin('chip_ecu', 'chip_ecu.ecu_id = chip_ecu_dict.id')->where(['chip_ecu.brand_id' => $model->brand_id])->orderBy('title')->all(),'id', 'title'),
                    'options' => [
                        'placeholder' => 'Выберите',
                        'id'=>'ecu_id'
                    ],
                    'select2Options' => ['pluginOptions' => ['allowClear' => true]],
                    'pluginEvents' => [
                        "change" => "function() { console.log(this.value); getAdditions(this); }",
                    ],
                    'pluginOptions' => [
                        'depends'=>['brand_id'],
                        'placeholder'=>'Выберите марку',
                        'url'=>Url::to(['/chip-ecu/json-list-brand']),
                    ],
                ]); ?>
            </div>
            <div class="col-sm-12 col-xl-7 col-md-7" id="additions_div">
                <?=$this->render('/partial/ecu_additions', ['model' => $model, 'viewType' => 'full'])?>
            </div>
        </div>

    <?php ActiveForm::end(); ?>

    </div>
</div>
