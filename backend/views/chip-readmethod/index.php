<?php

use common\models\ChipGeneration;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use backend\widgets\GridView;

/* @var $this yii\web\View */
/* @var $searchModel common\models\ChipReadmethodSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = Yii::t('backend', 'Chip Readmethods');
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="chip-readmethod-index">

    <?php // echo $this->render('_search', ['model' => $searchModel]); ?>

    <p>
        <?php echo Html::a(Yii::t('backend', 'Create {modelClass}', [
    'modelClass' => 'Chip Readmethod',
]), ['create'], ['class' => 'btn btn-success']) ?>
    </p>

    <?php echo GridView::widget([
        'bsVersion' => '4.x',
        'id'=>'crud-datatable',
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'as filterBehavior' => \thrieu\grid\FilterStateBehavior::className(),
        'exportConfig' => [\kartik\grid\GridView::EXCEL => true, \kartik\grid\GridView::PDF => true],
        'pjax' => true,
        'striped' => true,
        'condensed' => true,
        'clearFilter' => true,
        'responsive' => true,
        'panel' => [
//                'type' => 'success',
            'heading' => '<i class="glyphicon glyphicon-list"></i>',
        ],
        'columns' => [
//            ['class' => 'yii\grid\SerialColumn'],

//            'id',
            'title',
            [
                'attribute' => 'master',
                'format' => 'html',
                'header' => Yii::t('backend', 'Master or Slave'),
                'width' => '550px',
                'class'=>'\kartik\grid\DataColumn',
                'value' => function($model){
                    return !empty($model->master) ? 'Master' : 'Slave';
                },
                'filterType'=> \kartik\grid\GridView::FILTER_SELECT2,
                'filter'=>[1=>'Master', 0=>'Slave'],
                'filterWidgetOptions'=>[
                    'pluginOptions'=>['allowClear'=>true],
                ],
                'filterInputOptions'=>['placeholder'=>'Выберите'],
            ],
//            'created_at',
//            'deleted_at',

            ['class' => 'yii\grid\ActionColumn'],
        ],
    ]); ?>

</div>
