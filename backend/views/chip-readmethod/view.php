<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\ChipReadmethod */

$this->title = $model->title;
$this->params['breadcrumbs'][] = ['label' => Yii::t('backend', 'Chip Readmethods'), 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="chip-readmethod-view">

    <p>
        <?php echo Html::a(Yii::t('backend', 'Update'), ['update', 'id' => $model->id], ['class' => 'btn btn-primary']) ?>
        <?php echo Html::a(Yii::t('backend', 'Delete'), ['delete', 'id' => $model->id], [
            'class' => 'btn btn-danger',
            'data' => [
                'confirm' => Yii::t('backend', 'Are you sure you want to delete this item?'),
                'method' => 'post',
            ],
        ]) ?>
    </p>

    <?php echo DetailView::widget([
        'model' => $model,
        'attributes' => [
//            'id',
            'title',
            [
                'attribute' => 'master',
                'value' => !empty($model->master) ? 'Master' : 'Slave',
            ],
//            'deleted_at',
        ],
    ]) ?>

</div>
