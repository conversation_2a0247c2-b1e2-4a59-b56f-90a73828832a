<?php

use common\models\ChipBrand;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\bootstrap\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\ChipBrand */
/* @var $form yii\bootstrap\ActiveForm */
?>
<div class="card">

    <div class="chip-brand-update card-body">

    <?php $form = ActiveForm::begin(); ?>

    <?php echo $form->errorSummary($model); ?>
        <div class="row">
            <div class="col-sm-6">

                <?php echo $form->field($model, 'title')->textInput(['maxlength' => true]) ?>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-6">

                <?php echo $form->field($model, 'vehicle_id')->dropDownList(ArrayHelper::map
                (common\models\ChipVehicle::find()->orderBy('title')->all(),'id', 'title')) ?>
            </div>
        </div>

    <div class="form-group">
        <?php echo Html::submitButton($model->isNewRecord ? Yii::t('backend', 'Create') : Yii::t('backend', 'Update'), ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
    </div>

    <?php ActiveForm::end(); ?>

    </div>
</div>
