<?php

namespace backend\modules\content\controllers;

use backend\controllers\ChipController;
use backend\modules\content\models\search\ArticleSearch;
use common\models\Article;
use common\models\ArticleAttachment;
use common\models\ArticleCategory;
use common\traits\FormAjaxValidationTrait;
use Yii;
use yii\filters\VerbFilter;
use yii\web\Controller;
use yii\web\NotFoundHttpException;

class ArticleController extends ChipController
{
    use FormAjaxValidationTrait;
    public $contentClass = 'panel card';


    /** @inheritdoc */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['post'],
                ],
            ],
        ];
    }

    /**
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new ArticleSearch();
        $dataProvider = $searchModel->search(array_merge(Yii::$app->request->queryParams, ['ArticleSearch' =>['status' => 1]]));
        $dataProvider->sort = [
            'defaultOrder' => ['id' => SORT_DESC],
        ];

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * @return mixed
     */
    public function actionIndexClient()
    {
        $searchModel = new ArticleSearch();
        $dataProvider = $searchModel->search(array_merge(Yii::$app->request->queryParams, ['ArticleSearch' =>['status' => 1]]));
        $dataProvider->sort = [
            'defaultOrder' => ['id' => SORT_DESC],
        ];
        $dataProvider->pagination = [
            'pageSize' => 5
        ];

        return $this->render('index-client', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }
    /**
     * @return mixed
     */
    public function actionView($slug)
    {
        $model = Article::find()->published()->andWhere(['slug' => $slug])->one();
        if (!$model) {
            throw new NotFoundHttpException;
        }

        $viewFile = $model->view ?: 'view';
        return $this->render($viewFile, ['model' => $model]);
    }

    /**
     * @return mixed
     */
    public function actionCreate()
    {
        $article = new Article();

        $this->performAjaxValidation($article);

        if ($article->load(Yii::$app->request->post()) && $article->save()) {
            return $this->redirect(['index']);
        }

        return $this->render('create', [
            'model' => $article,
            'categories' => ArticleCategory::find()->active()->all(),
        ]);
    }

    /**
     * @param integer $id
     *
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $article = $this->findModel($id);

        $this->performAjaxValidation($article);

        if ($article->load(Yii::$app->request->post()) && $article->save()) {
            return $this->redirect(['index']);
        }
        return $this->render('update', [
            'model' => $article,
            'categories' => ArticleCategory::find()->active()->all(),
        ]);
    }

    /**
     * @param integer $id
     *
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * @param integer $id
     *
     * @return Article the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Article::findOne($id)) !== null) {
            return $model;
        }
        throw new NotFoundHttpException('The requested page does not exist.');

    }
    /**
     * @param $id
     * @return \frontend\controllers\ArticleController
     * @throws NotFoundHttpException
     * @throws \yii\web\HttpException
     */
    public function actionAttachmentDownload($id)
    {
        $model = ArticleAttachment::findOne($id);
        if (!$model) {
            throw new NotFoundHttpException;
        }

        return Yii::$app->response->sendStreamAsFile(
            Yii::$app->fileStorage->getFilesystem()->readStream($model->path),
            $model->name
        );
    }

}
