<?php
/** @var \ricco\ticket\models\TicketHead $newTicket */
use yii\helpers\Html;

/** @var \ricco\ticket\models\TicketBody $thisTicket */
/** @var \ricco\ticket\models\TicketFile $fileTicket */
$this->title = 'Support > Answer ticket';

?>
<div class="panel page-block">
    <div class="container-fluid row">
        <div class="col-lg-12">
            <a class="m-b-10 f-left m-r-50" href="<?= \yii\helpers\Url::toRoute(['admin/index']) ?>"><?=Yii::t('backend', 'Back')?></a>
            <span class="m-b-10  f-left  m-l-50"><?= 'Department: '.$ticket->department.', subject: '.$ticket->topic?></span>
            <a class="btn btn-link ms-color f-right" role="button" data-toggle="collapse" href="#collapseExample"
               aria-expanded="false" aria-controls="collapseExample">
                <i class="glyphicon glyphicon-pencil pull-left"></i><span><?=Yii::t('backend', 'Answer')?></span>
            </a>
            <div class="collapse" id="collapseExample">
                <div class="well">
                    <?php $form = \yii\widgets\ActiveForm::begin() ?>
                    <?= $form->field($newTicket,
                        'text')->textarea(['style' => 'height: 150px; resize: none;'])->label('Message')->error() ?>
                    <?= $form->field($fileTicket, 'fileName[]')->fileInput([
                        'multiple' => true,
                        'accept'   => 'image/*',
                    ])->label(false); ?>

                    <div class="text-center">
                        <button class='btn btn-primary'><?=Yii::t('backend', 'Send')?></button>
                    </div>
                    <?= $form->errorSummary($newTicket) ?>
                    <?php $form->end() ?>
                </div>
            </div>
            <div class="clearfix" style="margin-bottom: 20px"></div>
            <?php foreach ($thisTicket as $ticket) : ?>
                <div class="row m-b-10 p-b-5 p-t-5 b-radius-5 project-note-<?= ($ticket['client'] == 1) ? 'client' : 'master' ?> m-l-0 m-r-0">
                    <div class="col-md-3 col-lg-3 col-sm-3 f-12">
                        <?= nl2br(Html::encode($ticket['text'])) ?>
                    </div>
                    <div class="col-md-5 col-lg-5 col-sm-5 f-12">
                        <?php if (!empty($ticket['file'])) : ?>
                            <hr>
                            <?php foreach ($ticket['file'] as $file) : ?>
                                <a href="<?=Yii::getAlias('@backendUrl')?>/fileTicket/<?= $file['fileName'] ?>" target="_blank"><img
                                            src="<?=Yii::getAlias('@backendUrl')?>/fileTicket/reduced/<?= $file['fileName'] ?> " alt="..."
                                            class="img-thumbnail"></a>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-4 col-lg-4 col-sm-4 p-l-0">
                        <div class="text-left f-right f-12">
                            <?= $ticket['name_user'] ?>
                            <br>
                            <span class=""><?=date("d/m/Y H:i", strtotime($ticket['date'])) ?></span>
                        </div>
                    </div>
                </div>

            <?php endforeach; ?>
        </div>
    </div>
</div>