<?php
ini_set('memory_limit', -1);
defined('YII_APP_BASE_PATH') or define('YII_APP_BASE_PATH', __DIR__.'/../../');
defined('YII_DEBUG') or define('YII_DEBUG', true);
defined('YII_ENV') or define('YII_ENV', 'test');

//require(YII_APP_BASE_PATH . '/common/config/coverage_start.php');
require_once YII_APP_BASE_PATH . '/vendor/autoload.php';
require_once YII_APP_BASE_PATH . '/vendor/yiisoft/yii2/Yii.php';
require_once YII_APP_BASE_PATH . '/common/config/bootstrap.php';
require_once __DIR__ . '/../config/bootstrap.php';



//require(YII_APP_BASE_PATH . '/common/events/LanguageEvent.php');
require_once __DIR__ . '/../web/constants.php';

//$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../../');
//$dotenv->load();


$config = yii\helpers\ArrayHelper::merge(
    require(__DIR__ . '/../../common/config/main.php'),
    require(__DIR__ . '/../config/main.php')
);

//require(__DIR__ . '/../../common/config/coverage_finish.php');