<?php

namespace common\chip\alientech\services;

use PHPUnit\Framework\TestCase;

class AlientechProjectServiceTest extends TestCase
{

    public function testProcessSuccessOperationDecode()
    {
        $this->assertTrue(1===1);
    }

    public function testProcessErrorOperationDecode()
    {

    }

    public function testProcessErrorAddDecodedFile()
    {

    }

    public function testProcessErrorOperationEncode()
    {

    }

    public function test__construct()
    {

    }

    public function testProcessSuccessAddDecodedFile()
    {

    }

    public function testProcessSuccessAddEncodedFile()
    {

    }

    public function testProcessSuccessOperationEncode()
    {

    }
}
