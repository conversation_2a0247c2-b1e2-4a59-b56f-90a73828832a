[1] Log opened at 2024-11-02 19:09:36.091163
[1] [Step Debug] INFO: Connecting to configured address/port: host.docker.internal:9003.
[1] [Step Debug] INFO: Connected to debugging client: host.docker.internal:9003 (through xdebug.client_host/xdebug.client_port).
[1] [Step Debug] -> <init xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" fileuri="file:///opt/project/vendor/phpunit/phpunit/phpunit" language="PHP" xdebug:language_version="8.2.25" protocol_version="1.0" appid="1" idekey="PHPSTORM"><engine version="3.3.0alpha3"><![CDATA[Xdebug]]></engine><author><![CDATA[<PERSON><PERSON>]]></author><url><![CDATA[https://xdebug.org]]></url><copyright><![CDATA[Copyright (c) 2002-2023 by <PERSON><PERSON>]]></copyright></init>

[1] [Step Debug] <- feature_set -i 1 -n show_hidden -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="1" feature="show_hidden" success="1"></response>

[1] [Step Debug] <- feature_set -i 2 -n max_depth -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="2" feature="max_depth" success="1"></response>

[1] [Step Debug] <- feature_set -i 3 -n max_children -v 100
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="3" feature="max_children" success="1"></response>

[1] [Step Debug] <- feature_set -i 4 -n extended_properties -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="4" feature="extended_properties" success="1"></response>

[1] [Step Debug] <- feature_set -i 5 -n notify_ok -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="5" feature="notify_ok" success="1"></response>

[1] [Step Debug] <- feature_set -i 6 -n resolved_breakpoints -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="6" feature="resolved_breakpoints" success="1"></response>

[1] [Step Debug] <- feature_set -i 7 -n breakpoint_include_return_value -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="7" feature="breakpoint_include_return_value" success="1"></response>

[1] [Step Debug] <- stdout -i 8 -c 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="stdout" transaction_id="8" success="1"></response>

[1] [Step Debug] <- status -i 9
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="status" transaction_id="9" status="starting" reason="ok"></response>

[1] [Step Debug] <- step_into -i 10
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="step_into" transaction_id="10" status="break" reason="ok"><xdebug:message filename="file:///opt/project/vendor/phpunit/phpunit/phpunit" lineno="12"></xdebug:message></response>

[1] [Step Debug] <- eval -i 11 -- aXNzZXQoJF9TRVJWRVJbJ1BIUF9JREVfQ09ORklHJ10p
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="eval" transaction_id="11"><property type="bool"><![CDATA[0]]></property></response>

[1] [Step Debug] <- eval -i 12 -- aXNzZXQoJF9TRVJWRVJbJ1NFUlZFUl9OQU1FJ10p
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="eval" transaction_id="12"><property type="bool"><![CDATA[0]]></property></response>

[1] [Step Debug] <- eval -i 13 -- aXNzZXQoJF9TRVJWRVJbJ1NTSF9DT05ORUNUSU9OJ10p
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="eval" transaction_id="13"><property type="bool"><![CDATA[0]]></property></response>

[1] [Step Debug] <- eval -i 14 -- aXNzZXQoJF9TRVJWRVJbJ1NFUlZFUl9BRERSJ10p
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="eval" transaction_id="14"><property type="bool"><![CDATA[0]]></property></response>

[1] [Step Debug] <- breakpoint_set -i 15 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 91
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="15" id="10001" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 16 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 104
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="16" id="10002" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 17 -t line -f file://D:/000/chiptuning/common/helpers/ProjectHelper.php -n 100
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/helpers/ProjectHelper.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="17" id="10003" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 18 -t line -f file://D:/000/chiptuning/common/helpers/ProjectHelper.php -n 123
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/helpers/ProjectHelper.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="18" id="10004" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 19 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 108
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="19" id="10005" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 20 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 113
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="20" id="10006" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 21 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 99
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="21" id="10007" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 22 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 109
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="22" id="10008" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 23 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 98
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="23" id="10009" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 24 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 94
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="24" id="10010" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 25 -t line -f file://D:/000/chiptuning/common/chip/autopack/entities/dto/AutopackFillConfigScriptResponseDto.php -n 24
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/chip/autopack/entities/dto/AutopackFillConfigScriptResponseDto.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="25" id="10011" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 26 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 101
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="26" id="10012" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 27 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 106
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="27" id="10013" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 28 -t line -f file://D:/000/chiptuning/common/chip/alientech/entities/dto/AsyncOperationResultDto.php -n 184
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/chip/alientech/entities/dto/AsyncOperationResultDto.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="28" id="10014" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 29 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 110
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="29" id="10015" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 30 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 105
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="30" id="10016" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 31 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 97
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="31" id="10017" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 32 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 92
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="32" id="10018" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 33 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 112
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="33" id="10019" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 34 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 103
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="34" id="10020" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 35 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 114
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="35" id="10021" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 36 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 111
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="36" id="10022" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 37 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 96
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="37" id="10023" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 38 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 95
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="38" id="10024" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 39 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 107
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="39" id="10025" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 40 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 100
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="40" id="10026" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 41 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 93
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="41" id="10027" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 42 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 102
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="42" id="10028" resolved="unresolved"></response>

[1] [Step Debug] <- stack_get -i 43
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="stack_get" transaction_id="43"><stack where="{main}" level="0" type="file" filename="file:///opt/project/vendor/phpunit/phpunit/phpunit" lineno="12"></stack></response>

[1] [Step Debug] <- stack_get -i 44
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="stack_get" transaction_id="44"><stack where="{main}" level="0" type="file" filename="file:///opt/project/vendor/phpunit/phpunit/phpunit" lineno="12"></stack></response>

[1] [Step Debug] <- context_names -i 45
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="context_names" transaction_id="45"><context name="Locals" id="0"></context><context name="Superglobals" id="1"></context><context name="User defined constants" id="2"></context></response>

[1] [Step Debug] <- context_get -i 46 -d 0 -c 0
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="context_get" transaction_id="46" context="0"><property name="$file" fullname="$file" type="uninitialized"></property><property name="$requiredExtensions" fullname="$requiredExtensions" type="uninitialized"></property><property name="$unavailableExtensions" fullname="$unavailableExtensions" type="uninitialized"></property></response>

[1] [Step Debug] <- context_get -i 47 -d 0 -c 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="context_get" transaction_id="47" context="1"><property name="$_GET" fullname="$_GET" type="array" children="0" numchildren="0" page="0" pagesize="100"></property><property name="$_POST" fullname="$_POST" type="array" children="0" numchildren="0" page="0" pagesize="100"></property><property name="$_COOKIE" fullname="$_COOKIE" type="array" children="0" numchildren="0" page="0" pagesize="100"></property><property name="$_FILES" fullname="$_FILES" type="array" children="0" numchildren="0" page="0" pagesize="100"></property><property name="$argv" fullname="$argv" type="array" children="1" numchildren="9" page="0" pagesize="100"><property name="0" fullname="$argv[0]" type="string" size="43" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9waHB1bml0L3BocHVuaXQvcGhwdW5pdA==]]></property><property name="1" fullname="$argv[1]" type="string" size="18" encoding="base64"><![CDATA[LS1uby1jb25maWd1cmF0aW9u]]></property><property name="2" fullname="$argv[2]" type="string" size="8" encoding="base64"><![CDATA[LS1maWx0ZXI=]]></property><property name="3" fullname="$argv[3]" type="string" size="108" encoding="base64"><![CDATA[Lyhjb21tb25cXGNoaXBcXGFsaWVudGVjaFxcc2VydmljZXNcXEFsaWVudGVjaFByb2plY3RTZXJ2aWNlVGVzdDo6dGVzdFByb2Nlc3NTdWNjZXNzT3BlcmF0aW9uRGVjb2RlKSggLiopPyQv]]></property><property name="4" fullname="$argv[4]" type="string" size="13" encoding="base64"><![CDATA[LS10ZXN0LXN1ZmZpeA==]]></property><property name="5" fullname="$argv[5]" type="string" size="31" encoding="base64"><![CDATA[QWxpZW50ZWNoUHJvamVjdFNlcnZpY2VUZXN0LnBocA==]]></property><property name="6" fullname="$argv[6]" type="string" size="57" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L2JhY2tlbmQvdGVzdHMvY29tbW9uL2NoaXAvYWxpZW50ZWNoL3NlcnZpY2Vz]]></property><property name="7" fullname="$argv[7]" type="string" size="10" encoding="base64"><![CDATA[LS10ZWFtY2l0eQ==]]></property><property name="8" fullname="$argv[8]" type="string" size="54" encoding="base64"><![CDATA[LS1jYWNoZS1yZXN1bHQtZmlsZT0vb3B0L3Byb2plY3QvLnBocHVuaXQucmVzdWx0LmNhY2hl]]></property></property><property name="$argc" fullname="$argc" type="int"><![CDATA[9]]></property><property name="$_ENV" fullname="$_ENV" type="array" children="1" numchildren="16" page="0" pagesize="100"><property name="PATH" fullname="$_ENV[&quot;PATH&quot;]" type="string" size="60" encoding="base64"><![CDATA[L3Vzci9sb2NhbC9zYmluOi91c3IvbG9jYWwvYmluOi91c3Ivc2JpbjovdXNyL2Jpbjovc2JpbjovYmlu]]></property><property name="HOSTNAME" fullname="$_ENV[&quot;HOSTNAME&quot;]" type="string" size="12" encoding="base64"><![CDATA[YjAwNDkzNzdmYjU3]]></property><property name="JETBRAINS_REMOTE_RUN" fullname="$_ENV[&quot;JETBRAINS_REMOTE_RUN&quot;]" type="string" size="1" encoding="base64"><![CDATA[MQ==]]></property><property name="IDE_PHPUNIT_CUSTOM_LOADER" fullname="$_ENV[&quot;IDE_PHPUNIT_CUSTOM_LOADER&quot;]" type="string" size="32" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9hdXRvbG9hZC5waHA=]]></property><property name="TERM" fullname="$_ENV[&quot;TERM&quot;]" type="string" size="5" encoding="base64"><![CDATA[eHRlcm0=]]></property><property type="string" size="76"><name encoding="base64"><![CDATA[UEhQSVpFX0RFUFM=]]></name><fullname encoding="base64"><![CDATA[JF9FTlZbIlBIUElaRV9ERVBTIl0=]]></fullname><value encoding="base64"><![CDATA[YXV0b2NvbmYgCQlkcGtnLWRldiAJCWZpbGUgCQlnKysgCQlnY2MgCQlsaWJjLWRldiAJCW1ha2UgCQlwa2ctY29uZmlnIAkJcmUyYw==]]></value></property><property name="PHP_INI_DIR" fullname="$_ENV[&quot;PHP_INI_DIR&quot;]" type="string" size="18" encoding="base64"><![CDATA[L3Vzci9sb2NhbC9ldGMvcGhw]]></property><property name="PHP_CFLAGS" fullname="$_ENV[&quot;PHP_CFLAGS&quot;]" type="string" size="83" encoding="base64"><![CDATA[LWZzdGFjay1wcm90ZWN0b3Itc3Ryb25nIC1mcGljIC1mcGllIC1PMiAtRF9MQVJHRUZJTEVfU09VUkNFIC1EX0ZJTEVfT0ZGU0VUX0JJVFM9NjQ=]]></property><property name="PHP_CPPFLAGS" fullname="$_ENV[&quot;PHP_CPPFLAGS&quot;]" type="string" size="83" encoding="base64"><![CDATA[LWZzdGFjay1wcm90ZWN0b3Itc3Ryb25nIC1mcGljIC1mcGllIC1PMiAtRF9MQVJHRUZJTEVfU09VUkNFIC1EX0ZJTEVfT0ZGU0VUX0JJVFM9NjQ=]]></property><property name="PHP_LDFLAGS" fullname="$_ENV[&quot;PHP_LDFLAGS&quot;]" type="string" size="12" encoding="base64"><![CDATA[LVdsLC1PMSAtcGll]]></property><property name="GPG_KEYS" fullname="$_ENV[&quot;GPG_KEYS&quot;]" type="string" size="122" encoding="base64"><![CDATA[MzlCNjQxMzQzRDhDMTA0QjJCMTQ2REMzRjlDMzlEQzBCOTY5ODU0NCBFNjA5MTNFNERGMjA5OTA3RDhFMzBEOTY2NTlBOTdDOUNGMkE3OTVBIDExOThDMDExNzU5MzQ5N0E1RUM1QzE5OTI4NkFGMUY5ODk3NDY5REM=]]></property><property name="PHP_VERSION" fullname="$_ENV[&quot;PHP_VERSION&quot;]" type="string" size="6" encoding="base64"><![CDATA[OC4yLjI1]]></property><property name="PHP_URL" fullname="$_ENV[&quot;PHP_URL&quot;]" type="string" size="51" encoding="base64"><![CDATA[aHR0cHM6Ly93d3cucGhwLm5ldC9kaXN0cmlidXRpb25zL3BocC04LjIuMjUudGFyLnh6]]></property><property name="PHP_ASC_URL" fullname="$_ENV[&quot;PHP_ASC_URL&quot;]" type="string" size="55" encoding="base64"><![CDATA[aHR0cHM6Ly93d3cucGhwLm5ldC9kaXN0cmlidXRpb25zL3BocC04LjIuMjUudGFyLnh6LmFzYw==]]></property><property name="PHP_SHA256" fullname="$_ENV[&quot;PHP_SHA256&quot;]" type="string" size="64" encoding="base64"><![CDATA[MzMwYjU0ODc2ZWExZDA1YWRlMTJlZTk3MjYxNjczMzIwNThiY2NkNThkZmZhMWQ0ZTEyMTE3ZjZiNGY2MTZiOQ==]]></property><property name="HOME" fullname="$_ENV[&quot;HOME&quot;]" type="string" size="5" encoding="base64"><![CDATA[L3Jvb3Q=]]></property></property><property name="$_REQUEST" fullname="$_REQUEST" type="array" children="0" numchildren="0" page="0" pagesize="100"></property><property name="$_SERVER" fullname="$_SERVER" type="array" children="1" numchildren="25" page="0" pagesize="100"><property name="PATH" fullname="$_SERVER[&quot;PATH&quot;]" type="string" size="60" encoding="base64"><![CDATA[L3Vzci9sb2NhbC9zYmluOi91c3IvbG9jYWwvYmluOi91c3Ivc2JpbjovdXNyL2Jpbjovc2JpbjovYmlu]]></property><property name="HOSTNAME" fullname="$_SERVER[&quot;HOSTNAME&quot;]" type="string" size="12" encoding="base64"><![CDATA[YjAwNDkzNzdmYjU3]]></property><property name="JETBRAINS_REMOTE_RUN" fullname="$_SERVER[&quot;JETBRAINS_REMOTE_RUN&quot;]" type="string" size="1" encoding="base64"><![CDATA[MQ==]]></property><property name="IDE_PHPUNIT_CUSTOM_LOADER" fullname="$_SERVER[&quot;IDE_PHPUNIT_CUSTOM_LOADER&quot;]" type="string" size="32" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9hdXRvbG9hZC5waHA=]]></property><property name="TERM" fullname="$_SERVER[&quot;TERM&quot;]" type="string" size="5" encoding="base64"><![CDATA[eHRlcm0=]]></property><property type="string" size="76"><name encoding="base64"><![CDATA[UEhQSVpFX0RFUFM=]]></name><fullname encoding="base64"><![CDATA[JF9TRVJWRVJbIlBIUElaRV9ERVBTIl0=]]></fullname><value encoding="base64"><![CDATA[YXV0b2NvbmYgCQlkcGtnLWRldiAJCWZpbGUgCQlnKysgCQlnY2MgCQlsaWJjLWRldiAJCW1ha2UgCQlwa2ctY29uZmlnIAkJcmUyYw==]]></value></property><property name="PHP_INI_DIR" fullname="$_SERVER[&quot;PHP_INI_DIR&quot;]" type="string" size="18" encoding="base64"><![CDATA[L3Vzci9sb2NhbC9ldGMvcGhw]]></property><property name="PHP_CFLAGS" fullname="$_SERVER[&quot;PHP_CFLAGS&quot;]" type="string" size="83" encoding="base64"><![CDATA[LWZzdGFjay1wcm90ZWN0b3Itc3Ryb25nIC1mcGljIC1mcGllIC1PMiAtRF9MQVJHRUZJTEVfU09VUkNFIC1EX0ZJTEVfT0ZGU0VUX0JJVFM9NjQ=]]></property><property name="PHP_CPPFLAGS" fullname="$_SERVER[&quot;PHP_CPPFLAGS&quot;]" type="string" size="83" encoding="base64"><![CDATA[LWZzdGFjay1wcm90ZWN0b3Itc3Ryb25nIC1mcGljIC1mcGllIC1PMiAtRF9MQVJHRUZJTEVfU09VUkNFIC1EX0ZJTEVfT0ZGU0VUX0JJVFM9NjQ=]]></property><property name="PHP_LDFLAGS" fullname="$_SERVER[&quot;PHP_LDFLAGS&quot;]" type="string" size="12" encoding="base64"><![CDATA[LVdsLC1PMSAtcGll]]></property><property name="GPG_KEYS" fullname="$_SERVER[&quot;GPG_KEYS&quot;]" type="string" size="122" encoding="base64"><![CDATA[MzlCNjQxMzQzRDhDMTA0QjJCMTQ2REMzRjlDMzlEQzBCOTY5ODU0NCBFNjA5MTNFNERGMjA5OTA3RDhFMzBEOTY2NTlBOTdDOUNGMkE3OTVBIDExOThDMDExNzU5MzQ5N0E1RUM1QzE5OTI4NkFGMUY5ODk3NDY5REM=]]></property><property name="PHP_VERSION" fullname="$_SERVER[&quot;PHP_VERSION&quot;]" type="string" size="6" encoding="base64"><![CDATA[OC4yLjI1]]></property><property name="PHP_URL" fullname="$_SERVER[&quot;PHP_URL&quot;]" type="string" size="51" encoding="base64"><![CDATA[aHR0cHM6Ly93d3cucGhwLm5ldC9kaXN0cmlidXRpb25zL3BocC04LjIuMjUudGFyLnh6]]></property><property name="PHP_ASC_URL" fullname="$_SERVER[&quot;PHP_ASC_URL&quot;]" type="string" size="55" encoding="base64"><![CDATA[aHR0cHM6Ly93d3cucGhwLm5ldC9kaXN0cmlidXRpb25zL3BocC04LjIuMjUudGFyLnh6LmFzYw==]]></property><property name="PHP_SHA256" fullname="$_SERVER[&quot;PHP_SHA256&quot;]" type="string" size="64" encoding="base64"><![CDATA[MzMwYjU0ODc2ZWExZDA1YWRlMTJlZTk3MjYxNjczMzIwNThiY2NkNThkZmZhMWQ0ZTEyMTE3ZjZiNGY2MTZiOQ==]]></property><property name="HOME" fullname="$_SERVER[&quot;HOME&quot;]" type="string" size="5" encoding="base64"><![CDATA[L3Jvb3Q=]]></property><property name="PHP_SELF" fullname="$_SERVER[&quot;PHP_SELF&quot;]" type="string" size="43" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9waHB1bml0L3BocHVuaXQvcGhwdW5pdA==]]></property><property name="SCRIPT_NAME" fullname="$_SERVER[&quot;SCRIPT_NAME&quot;]" type="string" size="43" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9waHB1bml0L3BocHVuaXQvcGhwdW5pdA==]]></property><property name="SCRIPT_FILENAME" fullname="$_SERVER[&quot;SCRIPT_FILENAME&quot;]" type="string" size="43" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9waHB1bml0L3BocHVuaXQvcGhwdW5pdA==]]></property><property name="PATH_TRANSLATED" fullname="$_SERVER[&quot;PATH_TRANSLATED&quot;]" type="string" size="43" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9waHB1bml0L3BocHVuaXQvcGhwdW5pdA==]]></property><property name="DOCUMENT_ROOT" fullname="$_SERVER[&quot;DOCUMENT_ROOT&quot;]" type="string" size="0" encoding="base64"><![CDATA[]]></property><property name="REQUEST_TIME_FLOAT" fullname="$_SERVER[&quot;REQUEST_TIME_FLOAT&quot;]" type="float"><![CDATA[1730574576.0914]]></property><property name="REQUEST_TIME" fullname="$_SERVER[&quot;REQUEST_TIME&quot;]" type="int"><![CDATA[1730574576]]></property><property name="argv" fullname="$_SERVER[&quot;argv&quot;]" type="array" children="1" numchildren="9"></property><property name="argc" fullname="$_SERVER[&quot;argc&quot;]" type="int"><![CDATA[9]]></property></property><property name="$requiredExtensions" fullname="$requiredExtensions" type="uninitialized"></property><property name="$unavailableExtensions" fullname="$unavailableExtensions" type="uninitialized"></property><property name="$file" fullname="$file" type="uninitialized"></property></response>

[1] [Step Debug] <- context_get -i 48 -d 0 -c 2
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="context_get" transaction_id="48" context="2"></response>

[1] [Step Debug] <- run -i 49
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="run" transaction_id="49" status="stopping" reason="ok"></response>

[1] [Step Debug] <- detach -i 50
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="detach" transaction_id="50" status="stopping" reason="ok"></response>

[1] Log closed at 2024-11-02 19:09:41.408667

[1] Log opened at 2024-11-02 19:10:05.374269
[1] [Step Debug] INFO: Connecting to configured address/port: host.docker.internal:9003.
[1] [Step Debug] INFO: Connected to debugging client: host.docker.internal:9003 (through xdebug.client_host/xdebug.client_port).
[1] [Step Debug] -> <init xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" fileuri="file:///opt/project/vendor/phpunit/phpunit/phpunit" language="PHP" xdebug:language_version="8.2.25" protocol_version="1.0" appid="1" idekey="PHPSTORM"><engine version="3.3.0alpha3"><![CDATA[Xdebug]]></engine><author><![CDATA[Derick Rethans]]></author><url><![CDATA[https://xdebug.org]]></url><copyright><![CDATA[Copyright (c) 2002-2023 by Derick Rethans]]></copyright></init>

[1] [Step Debug] <- feature_set -i 1 -n show_hidden -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="1" feature="show_hidden" success="1"></response>

[1] [Step Debug] <- feature_set -i 2 -n max_depth -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="2" feature="max_depth" success="1"></response>

[1] [Step Debug] <- feature_set -i 3 -n max_children -v 100
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="3" feature="max_children" success="1"></response>

[1] [Step Debug] <- feature_set -i 4 -n extended_properties -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="4" feature="extended_properties" success="1"></response>

[1] [Step Debug] <- feature_set -i 5 -n notify_ok -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="5" feature="notify_ok" success="1"></response>

[1] [Step Debug] <- feature_set -i 6 -n resolved_breakpoints -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="6" feature="resolved_breakpoints" success="1"></response>

[1] [Step Debug] <- feature_set -i 7 -n breakpoint_include_return_value -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="7" feature="breakpoint_include_return_value" success="1"></response>

[1] [Step Debug] <- stdout -i 8 -c 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="stdout" transaction_id="8" success="1"></response>

[1] [Step Debug] <- status -i 9
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="status" transaction_id="9" status="starting" reason="ok"></response>

[1] [Step Debug] <- step_into -i 10
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="step_into" transaction_id="10" status="break" reason="ok"><xdebug:message filename="file:///opt/project/vendor/phpunit/phpunit/phpunit" lineno="12"></xdebug:message></response>

[1] [Step Debug] <- eval -i 11 -- aXNzZXQoJF9TRVJWRVJbJ1BIUF9JREVfQ09ORklHJ10p
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="eval" transaction_id="11"><property type="bool"><![CDATA[0]]></property></response>

[1] [Step Debug] <- eval -i 12 -- aXNzZXQoJF9TRVJWRVJbJ1NFUlZFUl9OQU1FJ10p
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="eval" transaction_id="12"><property type="bool"><![CDATA[0]]></property></response>

[1] [Step Debug] <- eval -i 13 -- aXNzZXQoJF9TRVJWRVJbJ1NTSF9DT05ORUNUSU9OJ10p
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="eval" transaction_id="13"><property type="bool"><![CDATA[0]]></property></response>

[1] [Step Debug] <- eval -i 14 -- aXNzZXQoJF9TRVJWRVJbJ1NFUlZFUl9BRERSJ10p
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="eval" transaction_id="14"><property type="bool"><![CDATA[0]]></property></response>

[1] [Step Debug] <- breakpoint_set -i 15 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 91
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="15" id="10001" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 16 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 104
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="16" id="10002" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 17 -t line -f file://D:/000/chiptuning/common/helpers/ProjectHelper.php -n 100
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/helpers/ProjectHelper.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="17" id="10003" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 18 -t line -f file://D:/000/chiptuning/common/helpers/ProjectHelper.php -n 123
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/helpers/ProjectHelper.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="18" id="10004" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 19 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 108
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="19" id="10005" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 20 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 113
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="20" id="10006" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 21 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 99
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="21" id="10007" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 22 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 109
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="22" id="10008" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 23 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 98
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="23" id="10009" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 24 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 94
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="24" id="10010" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 25 -t line -f file://D:/000/chiptuning/common/chip/autopack/entities/dto/AutopackFillConfigScriptResponseDto.php -n 24
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/chip/autopack/entities/dto/AutopackFillConfigScriptResponseDto.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="25" id="10011" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 26 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 101
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="26" id="10012" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 27 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 106
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="27" id="10013" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 28 -t line -f file://D:/000/chiptuning/common/chip/alientech/entities/dto/AsyncOperationResultDto.php -n 184
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/chip/alientech/entities/dto/AsyncOperationResultDto.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="28" id="10014" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 29 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 110
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="29" id="10015" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 30 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 105
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="30" id="10016" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 31 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 97
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="31" id="10017" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 32 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 92
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="32" id="10018" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 33 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 112
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="33" id="10019" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 34 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 103
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="34" id="10020" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 35 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 114
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="35" id="10021" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 36 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 111
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="36" id="10022" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 37 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 96
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="37" id="10023" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 38 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 95
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="38" id="10024" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 39 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 107
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="39" id="10025" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 40 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 100
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="40" id="10026" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 41 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 93
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="41" id="10027" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 42 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 102
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="42" id="10028" resolved="unresolved"></response>

[1] [Step Debug] <- stack_get -i 43
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="stack_get" transaction_id="43"><stack where="{main}" level="0" type="file" filename="file:///opt/project/vendor/phpunit/phpunit/phpunit" lineno="12"></stack></response>

[1] [Step Debug] <- stack_get -i 44
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="stack_get" transaction_id="44"><stack where="{main}" level="0" type="file" filename="file:///opt/project/vendor/phpunit/phpunit/phpunit" lineno="12"></stack></response>

[1] [Step Debug] <- context_names -i 45
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="context_names" transaction_id="45"><context name="Locals" id="0"></context><context name="Superglobals" id="1"></context><context name="User defined constants" id="2"></context></response>

[1] [Step Debug] <- context_get -i 46 -d 0 -c 0
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="context_get" transaction_id="46" context="0"><property name="$file" fullname="$file" type="uninitialized"></property><property name="$requiredExtensions" fullname="$requiredExtensions" type="uninitialized"></property><property name="$unavailableExtensions" fullname="$unavailableExtensions" type="uninitialized"></property></response>

[1] [Step Debug] <- context_get -i 47 -d 0 -c 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="context_get" transaction_id="47" context="1"><property name="$_GET" fullname="$_GET" type="array" children="0" numchildren="0" page="0" pagesize="100"></property><property name="$_POST" fullname="$_POST" type="array" children="0" numchildren="0" page="0" pagesize="100"></property><property name="$_COOKIE" fullname="$_COOKIE" type="array" children="0" numchildren="0" page="0" pagesize="100"></property><property name="$_FILES" fullname="$_FILES" type="array" children="0" numchildren="0" page="0" pagesize="100"></property><property name="$argv" fullname="$argv" type="array" children="1" numchildren="9" page="0" pagesize="100"><property name="0" fullname="$argv[0]" type="string" size="43" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9waHB1bml0L3BocHVuaXQvcGhwdW5pdA==]]></property><property name="1" fullname="$argv[1]" type="string" size="18" encoding="base64"><![CDATA[LS1uby1jb25maWd1cmF0aW9u]]></property><property name="2" fullname="$argv[2]" type="string" size="8" encoding="base64"><![CDATA[LS1maWx0ZXI=]]></property><property name="3" fullname="$argv[3]" type="string" size="108" encoding="base64"><![CDATA[Lyhjb21tb25cXGNoaXBcXGFsaWVudGVjaFxcc2VydmljZXNcXEFsaWVudGVjaFByb2plY3RTZXJ2aWNlVGVzdDo6dGVzdFByb2Nlc3NTdWNjZXNzT3BlcmF0aW9uRGVjb2RlKSggLiopPyQv]]></property><property name="4" fullname="$argv[4]" type="string" size="13" encoding="base64"><![CDATA[LS10ZXN0LXN1ZmZpeA==]]></property><property name="5" fullname="$argv[5]" type="string" size="31" encoding="base64"><![CDATA[QWxpZW50ZWNoUHJvamVjdFNlcnZpY2VUZXN0LnBocA==]]></property><property name="6" fullname="$argv[6]" type="string" size="57" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L2JhY2tlbmQvdGVzdHMvY29tbW9uL2NoaXAvYWxpZW50ZWNoL3NlcnZpY2Vz]]></property><property name="7" fullname="$argv[7]" type="string" size="10" encoding="base64"><![CDATA[LS10ZWFtY2l0eQ==]]></property><property name="8" fullname="$argv[8]" type="string" size="54" encoding="base64"><![CDATA[LS1jYWNoZS1yZXN1bHQtZmlsZT0vb3B0L3Byb2plY3QvLnBocHVuaXQucmVzdWx0LmNhY2hl]]></property></property><property name="$argc" fullname="$argc" type="int"><![CDATA[9]]></property><property name="$_ENV" fullname="$_ENV" type="array" children="1" numchildren="16" page="0" pagesize="100"><property name="PATH" fullname="$_ENV[&quot;PATH&quot;]" type="string" size="60" encoding="base64"><![CDATA[L3Vzci9sb2NhbC9zYmluOi91c3IvbG9jYWwvYmluOi91c3Ivc2JpbjovdXNyL2Jpbjovc2JpbjovYmlu]]></property><property name="HOSTNAME" fullname="$_ENV[&quot;HOSTNAME&quot;]" type="string" size="12" encoding="base64"><![CDATA[NDk4ZjlhZjI0ZDNm]]></property><property name="JETBRAINS_REMOTE_RUN" fullname="$_ENV[&quot;JETBRAINS_REMOTE_RUN&quot;]" type="string" size="1" encoding="base64"><![CDATA[MQ==]]></property><property name="IDE_PHPUNIT_CUSTOM_LOADER" fullname="$_ENV[&quot;IDE_PHPUNIT_CUSTOM_LOADER&quot;]" type="string" size="32" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9hdXRvbG9hZC5waHA=]]></property><property name="TERM" fullname="$_ENV[&quot;TERM&quot;]" type="string" size="5" encoding="base64"><![CDATA[eHRlcm0=]]></property><property type="string" size="76"><name encoding="base64"><![CDATA[UEhQSVpFX0RFUFM=]]></name><fullname encoding="base64"><![CDATA[JF9FTlZbIlBIUElaRV9ERVBTIl0=]]></fullname><value encoding="base64"><![CDATA[YXV0b2NvbmYgCQlkcGtnLWRldiAJCWZpbGUgCQlnKysgCQlnY2MgCQlsaWJjLWRldiAJCW1ha2UgCQlwa2ctY29uZmlnIAkJcmUyYw==]]></value></property><property name="PHP_INI_DIR" fullname="$_ENV[&quot;PHP_INI_DIR&quot;]" type="string" size="18" encoding="base64"><![CDATA[L3Vzci9sb2NhbC9ldGMvcGhw]]></property><property name="PHP_CFLAGS" fullname="$_ENV[&quot;PHP_CFLAGS&quot;]" type="string" size="83" encoding="base64"><![CDATA[LWZzdGFjay1wcm90ZWN0b3Itc3Ryb25nIC1mcGljIC1mcGllIC1PMiAtRF9MQVJHRUZJTEVfU09VUkNFIC1EX0ZJTEVfT0ZGU0VUX0JJVFM9NjQ=]]></property><property name="PHP_CPPFLAGS" fullname="$_ENV[&quot;PHP_CPPFLAGS&quot;]" type="string" size="83" encoding="base64"><![CDATA[LWZzdGFjay1wcm90ZWN0b3Itc3Ryb25nIC1mcGljIC1mcGllIC1PMiAtRF9MQVJHRUZJTEVfU09VUkNFIC1EX0ZJTEVfT0ZGU0VUX0JJVFM9NjQ=]]></property><property name="PHP_LDFLAGS" fullname="$_ENV[&quot;PHP_LDFLAGS&quot;]" type="string" size="12" encoding="base64"><![CDATA[LVdsLC1PMSAtcGll]]></property><property name="GPG_KEYS" fullname="$_ENV[&quot;GPG_KEYS&quot;]" type="string" size="122" encoding="base64"><![CDATA[MzlCNjQxMzQzRDhDMTA0QjJCMTQ2REMzRjlDMzlEQzBCOTY5ODU0NCBFNjA5MTNFNERGMjA5OTA3RDhFMzBEOTY2NTlBOTdDOUNGMkE3OTVBIDExOThDMDExNzU5MzQ5N0E1RUM1QzE5OTI4NkFGMUY5ODk3NDY5REM=]]></property><property name="PHP_VERSION" fullname="$_ENV[&quot;PHP_VERSION&quot;]" type="string" size="6" encoding="base64"><![CDATA[OC4yLjI1]]></property><property name="PHP_URL" fullname="$_ENV[&quot;PHP_URL&quot;]" type="string" size="51" encoding="base64"><![CDATA[aHR0cHM6Ly93d3cucGhwLm5ldC9kaXN0cmlidXRpb25zL3BocC04LjIuMjUudGFyLnh6]]></property><property name="PHP_ASC_URL" fullname="$_ENV[&quot;PHP_ASC_URL&quot;]" type="string" size="55" encoding="base64"><![CDATA[aHR0cHM6Ly93d3cucGhwLm5ldC9kaXN0cmlidXRpb25zL3BocC04LjIuMjUudGFyLnh6LmFzYw==]]></property><property name="PHP_SHA256" fullname="$_ENV[&quot;PHP_SHA256&quot;]" type="string" size="64" encoding="base64"><![CDATA[MzMwYjU0ODc2ZWExZDA1YWRlMTJlZTk3MjYxNjczMzIwNThiY2NkNThkZmZhMWQ0ZTEyMTE3ZjZiNGY2MTZiOQ==]]></property><property name="HOME" fullname="$_ENV[&quot;HOME&quot;]" type="string" size="5" encoding="base64"><![CDATA[L3Jvb3Q=]]></property></property><property name="$_REQUEST" fullname="$_REQUEST" type="array" children="0" numchildren="0" page="0" pagesize="100"></property><property name="$_SERVER" fullname="$_SERVER" type="array" children="1" numchildren="25" page="0" pagesize="100"><property name="PATH" fullname="$_SERVER[&quot;PATH&quot;]" type="string" size="60" encoding="base64"><![CDATA[L3Vzci9sb2NhbC9zYmluOi91c3IvbG9jYWwvYmluOi91c3Ivc2JpbjovdXNyL2Jpbjovc2JpbjovYmlu]]></property><property name="HOSTNAME" fullname="$_SERVER[&quot;HOSTNAME&quot;]" type="string" size="12" encoding="base64"><![CDATA[NDk4ZjlhZjI0ZDNm]]></property><property name="JETBRAINS_REMOTE_RUN" fullname="$_SERVER[&quot;JETBRAINS_REMOTE_RUN&quot;]" type="string" size="1" encoding="base64"><![CDATA[MQ==]]></property><property name="IDE_PHPUNIT_CUSTOM_LOADER" fullname="$_SERVER[&quot;IDE_PHPUNIT_CUSTOM_LOADER&quot;]" type="string" size="32" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9hdXRvbG9hZC5waHA=]]></property><property name="TERM" fullname="$_SERVER[&quot;TERM&quot;]" type="string" size="5" encoding="base64"><![CDATA[eHRlcm0=]]></property><property type="string" size="76"><name encoding="base64"><![CDATA[UEhQSVpFX0RFUFM=]]></name><fullname encoding="base64"><![CDATA[JF9TRVJWRVJbIlBIUElaRV9ERVBTIl0=]]></fullname><value encoding="base64"><![CDATA[YXV0b2NvbmYgCQlkcGtnLWRldiAJCWZpbGUgCQlnKysgCQlnY2MgCQlsaWJjLWRldiAJCW1ha2UgCQlwa2ctY29uZmlnIAkJcmUyYw==]]></value></property><property name="PHP_INI_DIR" fullname="$_SERVER[&quot;PHP_INI_DIR&quot;]" type="string" size="18" encoding="base64"><![CDATA[L3Vzci9sb2NhbC9ldGMvcGhw]]></property><property name="PHP_CFLAGS" fullname="$_SERVER[&quot;PHP_CFLAGS&quot;]" type="string" size="83" encoding="base64"><![CDATA[LWZzdGFjay1wcm90ZWN0b3Itc3Ryb25nIC1mcGljIC1mcGllIC1PMiAtRF9MQVJHRUZJTEVfU09VUkNFIC1EX0ZJTEVfT0ZGU0VUX0JJVFM9NjQ=]]></property><property name="PHP_CPPFLAGS" fullname="$_SERVER[&quot;PHP_CPPFLAGS&quot;]" type="string" size="83" encoding="base64"><![CDATA[LWZzdGFjay1wcm90ZWN0b3Itc3Ryb25nIC1mcGljIC1mcGllIC1PMiAtRF9MQVJHRUZJTEVfU09VUkNFIC1EX0ZJTEVfT0ZGU0VUX0JJVFM9NjQ=]]></property><property name="PHP_LDFLAGS" fullname="$_SERVER[&quot;PHP_LDFLAGS&quot;]" type="string" size="12" encoding="base64"><![CDATA[LVdsLC1PMSAtcGll]]></property><property name="GPG_KEYS" fullname="$_SERVER[&quot;GPG_KEYS&quot;]" type="string" size="122" encoding="base64"><![CDATA[MzlCNjQxMzQzRDhDMTA0QjJCMTQ2REMzRjlDMzlEQzBCOTY5ODU0NCBFNjA5MTNFNERGMjA5OTA3RDhFMzBEOTY2NTlBOTdDOUNGMkE3OTVBIDExOThDMDExNzU5MzQ5N0E1RUM1QzE5OTI4NkFGMUY5ODk3NDY5REM=]]></property><property name="PHP_VERSION" fullname="$_SERVER[&quot;PHP_VERSION&quot;]" type="string" size="6" encoding="base64"><![CDATA[OC4yLjI1]]></property><property name="PHP_URL" fullname="$_SERVER[&quot;PHP_URL&quot;]" type="string" size="51" encoding="base64"><![CDATA[aHR0cHM6Ly93d3cucGhwLm5ldC9kaXN0cmlidXRpb25zL3BocC04LjIuMjUudGFyLnh6]]></property><property name="PHP_ASC_URL" fullname="$_SERVER[&quot;PHP_ASC_URL&quot;]" type="string" size="55" encoding="base64"><![CDATA[aHR0cHM6Ly93d3cucGhwLm5ldC9kaXN0cmlidXRpb25zL3BocC04LjIuMjUudGFyLnh6LmFzYw==]]></property><property name="PHP_SHA256" fullname="$_SERVER[&quot;PHP_SHA256&quot;]" type="string" size="64" encoding="base64"><![CDATA[MzMwYjU0ODc2ZWExZDA1YWRlMTJlZTk3MjYxNjczMzIwNThiY2NkNThkZmZhMWQ0ZTEyMTE3ZjZiNGY2MTZiOQ==]]></property><property name="HOME" fullname="$_SERVER[&quot;HOME&quot;]" type="string" size="5" encoding="base64"><![CDATA[L3Jvb3Q=]]></property><property name="PHP_SELF" fullname="$_SERVER[&quot;PHP_SELF&quot;]" type="string" size="43" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9waHB1bml0L3BocHVuaXQvcGhwdW5pdA==]]></property><property name="SCRIPT_NAME" fullname="$_SERVER[&quot;SCRIPT_NAME&quot;]" type="string" size="43" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9waHB1bml0L3BocHVuaXQvcGhwdW5pdA==]]></property><property name="SCRIPT_FILENAME" fullname="$_SERVER[&quot;SCRIPT_FILENAME&quot;]" type="string" size="43" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9waHB1bml0L3BocHVuaXQvcGhwdW5pdA==]]></property><property name="PATH_TRANSLATED" fullname="$_SERVER[&quot;PATH_TRANSLATED&quot;]" type="string" size="43" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9waHB1bml0L3BocHVuaXQvcGhwdW5pdA==]]></property><property name="DOCUMENT_ROOT" fullname="$_SERVER[&quot;DOCUMENT_ROOT&quot;]" type="string" size="0" encoding="base64"><![CDATA[]]></property><property name="REQUEST_TIME_FLOAT" fullname="$_SERVER[&quot;REQUEST_TIME_FLOAT&quot;]" type="float"><![CDATA[1730574605.3745]]></property><property name="REQUEST_TIME" fullname="$_SERVER[&quot;REQUEST_TIME&quot;]" type="int"><![CDATA[1730574605]]></property><property name="argv" fullname="$_SERVER[&quot;argv&quot;]" type="array" children="1" numchildren="9"></property><property name="argc" fullname="$_SERVER[&quot;argc&quot;]" type="int"><![CDATA[9]]></property></property><property name="$requiredExtensions" fullname="$requiredExtensions" type="uninitialized"></property><property name="$unavailableExtensions" fullname="$unavailableExtensions" type="uninitialized"></property><property name="$file" fullname="$file" type="uninitialized"></property></response>

[1] [Step Debug] <- context_get -i 48 -d 0 -c 2
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="context_get" transaction_id="48" context="2"></response>

[1] [Step Debug] <- run -i 49
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="run" transaction_id="49" status="stopping" reason="ok"></response>

[1] [Step Debug] <- detach -i 50
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="detach" transaction_id="50" status="stopping" reason="ok"></response>

[1] Log closed at 2024-11-02 19:10:08.766437

[1] Log opened at 2024-11-02 19:11:05.566846
[1] [Step Debug] INFO: Connecting to configured address/port: host.docker.internal:9003.
[1] [Step Debug] INFO: Connected to debugging client: host.docker.internal:9003 (through xdebug.client_host/xdebug.client_port).
[1] [Step Debug] -> <init xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" fileuri="file:///opt/project/vendor/phpunit/phpunit/phpunit" language="PHP" xdebug:language_version="8.2.25" protocol_version="1.0" appid="1" idekey="PHPSTORM"><engine version="3.3.0alpha3"><![CDATA[Xdebug]]></engine><author><![CDATA[Derick Rethans]]></author><url><![CDATA[https://xdebug.org]]></url><copyright><![CDATA[Copyright (c) 2002-2023 by Derick Rethans]]></copyright></init>

[1] [Step Debug] <- feature_set -i 1 -n show_hidden -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="1" feature="show_hidden" success="1"></response>

[1] [Step Debug] <- feature_set -i 2 -n max_depth -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="2" feature="max_depth" success="1"></response>

[1] [Step Debug] <- feature_set -i 3 -n max_children -v 100
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="3" feature="max_children" success="1"></response>

[1] [Step Debug] <- feature_set -i 4 -n extended_properties -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="4" feature="extended_properties" success="1"></response>

[1] [Step Debug] <- feature_set -i 5 -n notify_ok -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="5" feature="notify_ok" success="1"></response>

[1] [Step Debug] <- feature_set -i 6 -n resolved_breakpoints -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="6" feature="resolved_breakpoints" success="1"></response>

[1] [Step Debug] <- feature_set -i 7 -n breakpoint_include_return_value -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="7" feature="breakpoint_include_return_value" success="1"></response>

[1] [Step Debug] <- stdout -i 8 -c 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="stdout" transaction_id="8" success="1"></response>

[1] [Step Debug] <- status -i 9
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="status" transaction_id="9" status="starting" reason="ok"></response>

[1] [Step Debug] <- step_into -i 10
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="step_into" transaction_id="10" status="break" reason="ok"><xdebug:message filename="file:///opt/project/vendor/phpunit/phpunit/phpunit" lineno="12"></xdebug:message></response>

[1] [Step Debug] <- eval -i 11 -- aXNzZXQoJF9TRVJWRVJbJ1BIUF9JREVfQ09ORklHJ10p
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="eval" transaction_id="11"><property type="bool"><![CDATA[0]]></property></response>

[1] [Step Debug] <- eval -i 12 -- aXNzZXQoJF9TRVJWRVJbJ1NFUlZFUl9OQU1FJ10p
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="eval" transaction_id="12"><property type="bool"><![CDATA[0]]></property></response>

[1] [Step Debug] <- eval -i 13 -- aXNzZXQoJF9TRVJWRVJbJ1NTSF9DT05ORUNUSU9OJ10p
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="eval" transaction_id="13"><property type="bool"><![CDATA[0]]></property></response>

[1] [Step Debug] <- eval -i 14 -- aXNzZXQoJF9TRVJWRVJbJ1NFUlZFUl9BRERSJ10p
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="eval" transaction_id="14"><property type="bool"><![CDATA[0]]></property></response>

[1] [Step Debug] <- breakpoint_set -i 15 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 91
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="15" id="10001" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 16 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 104
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="16" id="10002" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 17 -t line -f file://D:/000/chiptuning/common/helpers/ProjectHelper.php -n 100
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/helpers/ProjectHelper.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="17" id="10003" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 18 -t line -f file://D:/000/chiptuning/common/helpers/ProjectHelper.php -n 123
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/helpers/ProjectHelper.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="18" id="10004" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 19 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 108
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="19" id="10005" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 20 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 113
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="20" id="10006" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 21 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 99
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="21" id="10007" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 22 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 109
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="22" id="10008" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 23 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 98
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="23" id="10009" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 24 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 94
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="24" id="10010" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 25 -t line -f file://D:/000/chiptuning/common/chip/autopack/entities/dto/AutopackFillConfigScriptResponseDto.php -n 24
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/chip/autopack/entities/dto/AutopackFillConfigScriptResponseDto.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="25" id="10011" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 26 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 101
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="26" id="10012" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 27 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 106
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="27" id="10013" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 28 -t line -f file://D:/000/chiptuning/common/chip/alientech/entities/dto/AsyncOperationResultDto.php -n 184
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/chip/alientech/entities/dto/AsyncOperationResultDto.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="28" id="10014" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 29 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 110
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="29" id="10015" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 30 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 105
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="30" id="10016" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 31 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 97
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="31" id="10017" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 32 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 92
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="32" id="10018" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 33 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 112
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="33" id="10019" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 34 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 103
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="34" id="10020" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 35 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 114
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="35" id="10021" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 36 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 111
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="36" id="10022" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 37 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 96
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="37" id="10023" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 38 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 95
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="38" id="10024" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 39 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 107
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="39" id="10025" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 40 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 100
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="40" id="10026" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 41 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 93
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="41" id="10027" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 42 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 102
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="42" id="10028" resolved="unresolved"></response>

[1] [Step Debug] <- stack_get -i 43
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="stack_get" transaction_id="43"><stack where="{main}" level="0" type="file" filename="file:///opt/project/vendor/phpunit/phpunit/phpunit" lineno="12"></stack></response>

[1] [Step Debug] <- stack_get -i 44
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="stack_get" transaction_id="44"><stack where="{main}" level="0" type="file" filename="file:///opt/project/vendor/phpunit/phpunit/phpunit" lineno="12"></stack></response>

[1] [Step Debug] <- context_names -i 45
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="context_names" transaction_id="45"><context name="Locals" id="0"></context><context name="Superglobals" id="1"></context><context name="User defined constants" id="2"></context></response>

[1] [Step Debug] <- context_get -i 46 -d 0 -c 0
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="context_get" transaction_id="46" context="0"><property name="$file" fullname="$file" type="uninitialized"></property><property name="$requiredExtensions" fullname="$requiredExtensions" type="uninitialized"></property><property name="$unavailableExtensions" fullname="$unavailableExtensions" type="uninitialized"></property></response>

[1] [Step Debug] <- context_get -i 47 -d 0 -c 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="context_get" transaction_id="47" context="1"><property name="$_GET" fullname="$_GET" type="array" children="0" numchildren="0" page="0" pagesize="100"></property><property name="$_POST" fullname="$_POST" type="array" children="0" numchildren="0" page="0" pagesize="100"></property><property name="$_COOKIE" fullname="$_COOKIE" type="array" children="0" numchildren="0" page="0" pagesize="100"></property><property name="$_FILES" fullname="$_FILES" type="array" children="0" numchildren="0" page="0" pagesize="100"></property><property name="$argv" fullname="$argv" type="array" children="1" numchildren="9" page="0" pagesize="100"><property name="0" fullname="$argv[0]" type="string" size="43" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9waHB1bml0L3BocHVuaXQvcGhwdW5pdA==]]></property><property name="1" fullname="$argv[1]" type="string" size="18" encoding="base64"><![CDATA[LS1uby1jb25maWd1cmF0aW9u]]></property><property name="2" fullname="$argv[2]" type="string" size="8" encoding="base64"><![CDATA[LS1maWx0ZXI=]]></property><property name="3" fullname="$argv[3]" type="string" size="108" encoding="base64"><![CDATA[Lyhjb21tb25cXGNoaXBcXGFsaWVudGVjaFxcc2VydmljZXNcXEFsaWVudGVjaFByb2plY3RTZXJ2aWNlVGVzdDo6dGVzdFByb2Nlc3NTdWNjZXNzT3BlcmF0aW9uRGVjb2RlKSggLiopPyQv]]></property><property name="4" fullname="$argv[4]" type="string" size="13" encoding="base64"><![CDATA[LS10ZXN0LXN1ZmZpeA==]]></property><property name="5" fullname="$argv[5]" type="string" size="31" encoding="base64"><![CDATA[QWxpZW50ZWNoUHJvamVjdFNlcnZpY2VUZXN0LnBocA==]]></property><property name="6" fullname="$argv[6]" type="string" size="57" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L2JhY2tlbmQvdGVzdHMvY29tbW9uL2NoaXAvYWxpZW50ZWNoL3NlcnZpY2Vz]]></property><property name="7" fullname="$argv[7]" type="string" size="10" encoding="base64"><![CDATA[LS10ZWFtY2l0eQ==]]></property><property name="8" fullname="$argv[8]" type="string" size="54" encoding="base64"><![CDATA[LS1jYWNoZS1yZXN1bHQtZmlsZT0vb3B0L3Byb2plY3QvLnBocHVuaXQucmVzdWx0LmNhY2hl]]></property></property><property name="$argc" fullname="$argc" type="int"><![CDATA[9]]></property><property name="$_ENV" fullname="$_ENV" type="array" children="1" numchildren="16" page="0" pagesize="100"><property name="PATH" fullname="$_ENV[&quot;PATH&quot;]" type="string" size="60" encoding="base64"><![CDATA[L3Vzci9sb2NhbC9zYmluOi91c3IvbG9jYWwvYmluOi91c3Ivc2JpbjovdXNyL2Jpbjovc2JpbjovYmlu]]></property><property name="HOSTNAME" fullname="$_ENV[&quot;HOSTNAME&quot;]" type="string" size="12" encoding="base64"><![CDATA[Mjg2NmM0ZTg0NDky]]></property><property name="JETBRAINS_REMOTE_RUN" fullname="$_ENV[&quot;JETBRAINS_REMOTE_RUN&quot;]" type="string" size="1" encoding="base64"><![CDATA[MQ==]]></property><property name="IDE_PHPUNIT_CUSTOM_LOADER" fullname="$_ENV[&quot;IDE_PHPUNIT_CUSTOM_LOADER&quot;]" type="string" size="32" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9hdXRvbG9hZC5waHA=]]></property><property name="TERM" fullname="$_ENV[&quot;TERM&quot;]" type="string" size="5" encoding="base64"><![CDATA[eHRlcm0=]]></property><property type="string" size="76"><name encoding="base64"><![CDATA[UEhQSVpFX0RFUFM=]]></name><fullname encoding="base64"><![CDATA[JF9FTlZbIlBIUElaRV9ERVBTIl0=]]></fullname><value encoding="base64"><![CDATA[YXV0b2NvbmYgCQlkcGtnLWRldiAJCWZpbGUgCQlnKysgCQlnY2MgCQlsaWJjLWRldiAJCW1ha2UgCQlwa2ctY29uZmlnIAkJcmUyYw==]]></value></property><property name="PHP_INI_DIR" fullname="$_ENV[&quot;PHP_INI_DIR&quot;]" type="string" size="18" encoding="base64"><![CDATA[L3Vzci9sb2NhbC9ldGMvcGhw]]></property><property name="PHP_CFLAGS" fullname="$_ENV[&quot;PHP_CFLAGS&quot;]" type="string" size="83" encoding="base64"><![CDATA[LWZzdGFjay1wcm90ZWN0b3Itc3Ryb25nIC1mcGljIC1mcGllIC1PMiAtRF9MQVJHRUZJTEVfU09VUkNFIC1EX0ZJTEVfT0ZGU0VUX0JJVFM9NjQ=]]></property><property name="PHP_CPPFLAGS" fullname="$_ENV[&quot;PHP_CPPFLAGS&quot;]" type="string" size="83" encoding="base64"><![CDATA[LWZzdGFjay1wcm90ZWN0b3Itc3Ryb25nIC1mcGljIC1mcGllIC1PMiAtRF9MQVJHRUZJTEVfU09VUkNFIC1EX0ZJTEVfT0ZGU0VUX0JJVFM9NjQ=]]></property><property name="PHP_LDFLAGS" fullname="$_ENV[&quot;PHP_LDFLAGS&quot;]" type="string" size="12" encoding="base64"><![CDATA[LVdsLC1PMSAtcGll]]></property><property name="GPG_KEYS" fullname="$_ENV[&quot;GPG_KEYS&quot;]" type="string" size="122" encoding="base64"><![CDATA[MzlCNjQxMzQzRDhDMTA0QjJCMTQ2REMzRjlDMzlEQzBCOTY5ODU0NCBFNjA5MTNFNERGMjA5OTA3RDhFMzBEOTY2NTlBOTdDOUNGMkE3OTVBIDExOThDMDExNzU5MzQ5N0E1RUM1QzE5OTI4NkFGMUY5ODk3NDY5REM=]]></property><property name="PHP_VERSION" fullname="$_ENV[&quot;PHP_VERSION&quot;]" type="string" size="6" encoding="base64"><![CDATA[OC4yLjI1]]></property><property name="PHP_URL" fullname="$_ENV[&quot;PHP_URL&quot;]" type="string" size="51" encoding="base64"><![CDATA[aHR0cHM6Ly93d3cucGhwLm5ldC9kaXN0cmlidXRpb25zL3BocC04LjIuMjUudGFyLnh6]]></property><property name="PHP_ASC_URL" fullname="$_ENV[&quot;PHP_ASC_URL&quot;]" type="string" size="55" encoding="base64"><![CDATA[aHR0cHM6Ly93d3cucGhwLm5ldC9kaXN0cmlidXRpb25zL3BocC04LjIuMjUudGFyLnh6LmFzYw==]]></property><property name="PHP_SHA256" fullname="$_ENV[&quot;PHP_SHA256&quot;]" type="string" size="64" encoding="base64"><![CDATA[MzMwYjU0ODc2ZWExZDA1YWRlMTJlZTk3MjYxNjczMzIwNThiY2NkNThkZmZhMWQ0ZTEyMTE3ZjZiNGY2MTZiOQ==]]></property><property name="HOME" fullname="$_ENV[&quot;HOME&quot;]" type="string" size="5" encoding="base64"><![CDATA[L3Jvb3Q=]]></property></property><property name="$_REQUEST" fullname="$_REQUEST" type="array" children="0" numchildren="0" page="0" pagesize="100"></property><property name="$_SERVER" fullname="$_SERVER" type="array" children="1" numchildren="25" page="0" pagesize="100"><property name="PATH" fullname="$_SERVER[&quot;PATH&quot;]" type="string" size="60" encoding="base64"><![CDATA[L3Vzci9sb2NhbC9zYmluOi91c3IvbG9jYWwvYmluOi91c3Ivc2JpbjovdXNyL2Jpbjovc2JpbjovYmlu]]></property><property name="HOSTNAME" fullname="$_SERVER[&quot;HOSTNAME&quot;]" type="string" size="12" encoding="base64"><![CDATA[Mjg2NmM0ZTg0NDky]]></property><property name="JETBRAINS_REMOTE_RUN" fullname="$_SERVER[&quot;JETBRAINS_REMOTE_RUN&quot;]" type="string" size="1" encoding="base64"><![CDATA[MQ==]]></property><property name="IDE_PHPUNIT_CUSTOM_LOADER" fullname="$_SERVER[&quot;IDE_PHPUNIT_CUSTOM_LOADER&quot;]" type="string" size="32" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9hdXRvbG9hZC5waHA=]]></property><property name="TERM" fullname="$_SERVER[&quot;TERM&quot;]" type="string" size="5" encoding="base64"><![CDATA[eHRlcm0=]]></property><property type="string" size="76"><name encoding="base64"><![CDATA[UEhQSVpFX0RFUFM=]]></name><fullname encoding="base64"><![CDATA[JF9TRVJWRVJbIlBIUElaRV9ERVBTIl0=]]></fullname><value encoding="base64"><![CDATA[YXV0b2NvbmYgCQlkcGtnLWRldiAJCWZpbGUgCQlnKysgCQlnY2MgCQlsaWJjLWRldiAJCW1ha2UgCQlwa2ctY29uZmlnIAkJcmUyYw==]]></value></property><property name="PHP_INI_DIR" fullname="$_SERVER[&quot;PHP_INI_DIR&quot;]" type="string" size="18" encoding="base64"><![CDATA[L3Vzci9sb2NhbC9ldGMvcGhw]]></property><property name="PHP_CFLAGS" fullname="$_SERVER[&quot;PHP_CFLAGS&quot;]" type="string" size="83" encoding="base64"><![CDATA[LWZzdGFjay1wcm90ZWN0b3Itc3Ryb25nIC1mcGljIC1mcGllIC1PMiAtRF9MQVJHRUZJTEVfU09VUkNFIC1EX0ZJTEVfT0ZGU0VUX0JJVFM9NjQ=]]></property><property name="PHP_CPPFLAGS" fullname="$_SERVER[&quot;PHP_CPPFLAGS&quot;]" type="string" size="83" encoding="base64"><![CDATA[LWZzdGFjay1wcm90ZWN0b3Itc3Ryb25nIC1mcGljIC1mcGllIC1PMiAtRF9MQVJHRUZJTEVfU09VUkNFIC1EX0ZJTEVfT0ZGU0VUX0JJVFM9NjQ=]]></property><property name="PHP_LDFLAGS" fullname="$_SERVER[&quot;PHP_LDFLAGS&quot;]" type="string" size="12" encoding="base64"><![CDATA[LVdsLC1PMSAtcGll]]></property><property name="GPG_KEYS" fullname="$_SERVER[&quot;GPG_KEYS&quot;]" type="string" size="122" encoding="base64"><![CDATA[MzlCNjQxMzQzRDhDMTA0QjJCMTQ2REMzRjlDMzlEQzBCOTY5ODU0NCBFNjA5MTNFNERGMjA5OTA3RDhFMzBEOTY2NTlBOTdDOUNGMkE3OTVBIDExOThDMDExNzU5MzQ5N0E1RUM1QzE5OTI4NkFGMUY5ODk3NDY5REM=]]></property><property name="PHP_VERSION" fullname="$_SERVER[&quot;PHP_VERSION&quot;]" type="string" size="6" encoding="base64"><![CDATA[OC4yLjI1]]></property><property name="PHP_URL" fullname="$_SERVER[&quot;PHP_URL&quot;]" type="string" size="51" encoding="base64"><![CDATA[aHR0cHM6Ly93d3cucGhwLm5ldC9kaXN0cmlidXRpb25zL3BocC04LjIuMjUudGFyLnh6]]></property><property name="PHP_ASC_URL" fullname="$_SERVER[&quot;PHP_ASC_URL&quot;]" type="string" size="55" encoding="base64"><![CDATA[aHR0cHM6Ly93d3cucGhwLm5ldC9kaXN0cmlidXRpb25zL3BocC04LjIuMjUudGFyLnh6LmFzYw==]]></property><property name="PHP_SHA256" fullname="$_SERVER[&quot;PHP_SHA256&quot;]" type="string" size="64" encoding="base64"><![CDATA[MzMwYjU0ODc2ZWExZDA1YWRlMTJlZTk3MjYxNjczMzIwNThiY2NkNThkZmZhMWQ0ZTEyMTE3ZjZiNGY2MTZiOQ==]]></property><property name="HOME" fullname="$_SERVER[&quot;HOME&quot;]" type="string" size="5" encoding="base64"><![CDATA[L3Jvb3Q=]]></property><property name="PHP_SELF" fullname="$_SERVER[&quot;PHP_SELF&quot;]" type="string" size="43" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9waHB1bml0L3BocHVuaXQvcGhwdW5pdA==]]></property><property name="SCRIPT_NAME" fullname="$_SERVER[&quot;SCRIPT_NAME&quot;]" type="string" size="43" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9waHB1bml0L3BocHVuaXQvcGhwdW5pdA==]]></property><property name="SCRIPT_FILENAME" fullname="$_SERVER[&quot;SCRIPT_FILENAME&quot;]" type="string" size="43" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9waHB1bml0L3BocHVuaXQvcGhwdW5pdA==]]></property><property name="PATH_TRANSLATED" fullname="$_SERVER[&quot;PATH_TRANSLATED&quot;]" type="string" size="43" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9waHB1bml0L3BocHVuaXQvcGhwdW5pdA==]]></property><property name="DOCUMENT_ROOT" fullname="$_SERVER[&quot;DOCUMENT_ROOT&quot;]" type="string" size="0" encoding="base64"><![CDATA[]]></property><property name="REQUEST_TIME_FLOAT" fullname="$_SERVER[&quot;REQUEST_TIME_FLOAT&quot;]" type="float"><![CDATA[1730574665.567]]></property><property name="REQUEST_TIME" fullname="$_SERVER[&quot;REQUEST_TIME&quot;]" type="int"><![CDATA[1730574665]]></property><property name="argv" fullname="$_SERVER[&quot;argv&quot;]" type="array" children="1" numchildren="9"></property><property name="argc" fullname="$_SERVER[&quot;argc&quot;]" type="int"><![CDATA[9]]></property></property><property name="$requiredExtensions" fullname="$requiredExtensions" type="uninitialized"></property><property name="$unavailableExtensions" fullname="$unavailableExtensions" type="uninitialized"></property><property name="$file" fullname="$file" type="uninitialized"></property></response>

[1] [Step Debug] <- context_get -i 48 -d 0 -c 2
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="context_get" transaction_id="48" context="2"></response>

[1] [Step Debug] <- run -i 49
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="run" transaction_id="49" status="stopping" reason="ok"></response>

[1] [Step Debug] <- detach -i 50
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="detach" transaction_id="50" status="stopping" reason="ok"></response>

[1] Log closed at 2024-11-02 19:11:08.641684

[1] Log opened at 2024-11-02 19:11:47.648811
[1] [Step Debug] INFO: Connecting to configured address/port: host.docker.internal:9003.
[1] [Step Debug] INFO: Connected to debugging client: host.docker.internal:9003 (through xdebug.client_host/xdebug.client_port).
[1] [Step Debug] -> <init xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" fileuri="file:///opt/project/vendor/phpunit/phpunit/phpunit" language="PHP" xdebug:language_version="8.2.25" protocol_version="1.0" appid="1" idekey="PHPSTORM"><engine version="3.3.0alpha3"><![CDATA[Xdebug]]></engine><author><![CDATA[Derick Rethans]]></author><url><![CDATA[https://xdebug.org]]></url><copyright><![CDATA[Copyright (c) 2002-2023 by Derick Rethans]]></copyright></init>

[1] [Step Debug] <- feature_set -i 1 -n show_hidden -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="1" feature="show_hidden" success="1"></response>

[1] [Step Debug] <- feature_set -i 2 -n max_depth -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="2" feature="max_depth" success="1"></response>

[1] [Step Debug] <- feature_set -i 3 -n max_children -v 100
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="3" feature="max_children" success="1"></response>

[1] [Step Debug] <- feature_set -i 4 -n extended_properties -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="4" feature="extended_properties" success="1"></response>

[1] [Step Debug] <- feature_set -i 5 -n notify_ok -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="5" feature="notify_ok" success="1"></response>

[1] [Step Debug] <- feature_set -i 6 -n resolved_breakpoints -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="6" feature="resolved_breakpoints" success="1"></response>

[1] [Step Debug] <- feature_set -i 7 -n breakpoint_include_return_value -v 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="feature_set" transaction_id="7" feature="breakpoint_include_return_value" success="1"></response>

[1] [Step Debug] <- stdout -i 8 -c 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="stdout" transaction_id="8" success="1"></response>

[1] [Step Debug] <- status -i 9
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="status" transaction_id="9" status="starting" reason="ok"></response>

[1] [Step Debug] <- step_into -i 10
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="step_into" transaction_id="10" status="break" reason="ok"><xdebug:message filename="file:///opt/project/vendor/phpunit/phpunit/phpunit" lineno="12"></xdebug:message></response>

[1] [Step Debug] <- eval -i 11 -- aXNzZXQoJF9TRVJWRVJbJ1BIUF9JREVfQ09ORklHJ10p
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="eval" transaction_id="11"><property type="bool"><![CDATA[0]]></property></response>

[1] [Step Debug] <- eval -i 12 -- aXNzZXQoJF9TRVJWRVJbJ1NFUlZFUl9OQU1FJ10p
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="eval" transaction_id="12"><property type="bool"><![CDATA[0]]></property></response>

[1] [Step Debug] <- eval -i 13 -- aXNzZXQoJF9TRVJWRVJbJ1NTSF9DT05ORUNUSU9OJ10p
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="eval" transaction_id="13"><property type="bool"><![CDATA[0]]></property></response>

[1] [Step Debug] <- eval -i 14 -- aXNzZXQoJF9TRVJWRVJbJ1NFUlZFUl9BRERSJ10p
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="eval" transaction_id="14"><property type="bool"><![CDATA[0]]></property></response>

[1] [Step Debug] <- breakpoint_set -i 15 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 91
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="15" id="10001" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 16 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 104
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="16" id="10002" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 17 -t line -f file://D:/000/chiptuning/common/helpers/ProjectHelper.php -n 100
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/helpers/ProjectHelper.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="17" id="10003" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 18 -t line -f file://D:/000/chiptuning/common/helpers/ProjectHelper.php -n 123
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/helpers/ProjectHelper.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="18" id="10004" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 19 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 108
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="19" id="10005" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 20 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 113
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="20" id="10006" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 21 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 99
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="21" id="10007" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 22 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 109
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="22" id="10008" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 23 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 98
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="23" id="10009" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 24 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 94
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="24" id="10010" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 25 -t line -f file://D:/000/chiptuning/common/chip/autopack/entities/dto/AutopackFillConfigScriptResponseDto.php -n 24
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/chip/autopack/entities/dto/AutopackFillConfigScriptResponseDto.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="25" id="10011" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 26 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 101
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="26" id="10012" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 27 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 106
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="27" id="10013" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 28 -t line -f file://D:/000/chiptuning/common/chip/alientech/entities/dto/AsyncOperationResultDto.php -n 184
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/chip/alientech/entities/dto/AsyncOperationResultDto.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="28" id="10014" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 29 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 110
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="29" id="10015" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 30 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 105
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="30" id="10016" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 31 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 97
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="31" id="10017" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 32 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 92
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="32" id="10018" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 33 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 112
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="33" id="10019" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 34 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 103
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="34" id="10020" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 35 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 114
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="35" id="10021" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 36 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 111
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="36" id="10022" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 37 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 96
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="37" id="10023" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 38 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 95
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="38" id="10024" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 39 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 107
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="39" id="10025" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 40 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 100
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="40" id="10026" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 41 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 93
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="41" id="10027" resolved="unresolved"></response>

[1] [Step Debug] <- breakpoint_set -i 42 -t line -f file://D:/000/chiptuning/common/models/forms/CreateProject.php -n 102
[1] [Step Debug] WARN: Breakpoint file name does not exist: D:/000/chiptuning/common/models/forms/CreateProject.php (No such file or directory).
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="breakpoint_set" transaction_id="42" id="10028" resolved="unresolved"></response>

[1] [Step Debug] <- stack_get -i 43
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="stack_get" transaction_id="43"><stack where="{main}" level="0" type="file" filename="file:///opt/project/vendor/phpunit/phpunit/phpunit" lineno="12"></stack></response>

[1] [Step Debug] <- stack_get -i 44
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="stack_get" transaction_id="44"><stack where="{main}" level="0" type="file" filename="file:///opt/project/vendor/phpunit/phpunit/phpunit" lineno="12"></stack></response>

[1] [Step Debug] <- context_names -i 45
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="context_names" transaction_id="45"><context name="Locals" id="0"></context><context name="Superglobals" id="1"></context><context name="User defined constants" id="2"></context></response>

[1] [Step Debug] <- context_get -i 46 -d 0 -c 0
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="context_get" transaction_id="46" context="0"><property name="$file" fullname="$file" type="uninitialized"></property><property name="$requiredExtensions" fullname="$requiredExtensions" type="uninitialized"></property><property name="$unavailableExtensions" fullname="$unavailableExtensions" type="uninitialized"></property></response>

[1] [Step Debug] <- context_get -i 47 -d 0 -c 1
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="context_get" transaction_id="47" context="1"><property name="$_GET" fullname="$_GET" type="array" children="0" numchildren="0" page="0" pagesize="100"></property><property name="$_POST" fullname="$_POST" type="array" children="0" numchildren="0" page="0" pagesize="100"></property><property name="$_COOKIE" fullname="$_COOKIE" type="array" children="0" numchildren="0" page="0" pagesize="100"></property><property name="$_FILES" fullname="$_FILES" type="array" children="0" numchildren="0" page="0" pagesize="100"></property><property name="$argv" fullname="$argv" type="array" children="1" numchildren="9" page="0" pagesize="100"><property name="0" fullname="$argv[0]" type="string" size="43" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9waHB1bml0L3BocHVuaXQvcGhwdW5pdA==]]></property><property name="1" fullname="$argv[1]" type="string" size="18" encoding="base64"><![CDATA[LS1uby1jb25maWd1cmF0aW9u]]></property><property name="2" fullname="$argv[2]" type="string" size="8" encoding="base64"><![CDATA[LS1maWx0ZXI=]]></property><property name="3" fullname="$argv[3]" type="string" size="108" encoding="base64"><![CDATA[Lyhjb21tb25cXGNoaXBcXGFsaWVudGVjaFxcc2VydmljZXNcXEFsaWVudGVjaFByb2plY3RTZXJ2aWNlVGVzdDo6dGVzdFByb2Nlc3NTdWNjZXNzT3BlcmF0aW9uRGVjb2RlKSggLiopPyQv]]></property><property name="4" fullname="$argv[4]" type="string" size="13" encoding="base64"><![CDATA[LS10ZXN0LXN1ZmZpeA==]]></property><property name="5" fullname="$argv[5]" type="string" size="31" encoding="base64"><![CDATA[QWxpZW50ZWNoUHJvamVjdFNlcnZpY2VUZXN0LnBocA==]]></property><property name="6" fullname="$argv[6]" type="string" size="57" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L2JhY2tlbmQvdGVzdHMvY29tbW9uL2NoaXAvYWxpZW50ZWNoL3NlcnZpY2Vz]]></property><property name="7" fullname="$argv[7]" type="string" size="10" encoding="base64"><![CDATA[LS10ZWFtY2l0eQ==]]></property><property name="8" fullname="$argv[8]" type="string" size="54" encoding="base64"><![CDATA[LS1jYWNoZS1yZXN1bHQtZmlsZT0vb3B0L3Byb2plY3QvLnBocHVuaXQucmVzdWx0LmNhY2hl]]></property></property><property name="$argc" fullname="$argc" type="int"><![CDATA[9]]></property><property name="$_ENV" fullname="$_ENV" type="array" children="1" numchildren="16" page="0" pagesize="100"><property name="PATH" fullname="$_ENV[&quot;PATH&quot;]" type="string" size="60" encoding="base64"><![CDATA[L3Vzci9sb2NhbC9zYmluOi91c3IvbG9jYWwvYmluOi91c3Ivc2JpbjovdXNyL2Jpbjovc2JpbjovYmlu]]></property><property name="HOSTNAME" fullname="$_ENV[&quot;HOSTNAME&quot;]" type="string" size="12" encoding="base64"><![CDATA[YjQxYTZmYWZiNmRm]]></property><property name="JETBRAINS_REMOTE_RUN" fullname="$_ENV[&quot;JETBRAINS_REMOTE_RUN&quot;]" type="string" size="1" encoding="base64"><![CDATA[MQ==]]></property><property name="IDE_PHPUNIT_CUSTOM_LOADER" fullname="$_ENV[&quot;IDE_PHPUNIT_CUSTOM_LOADER&quot;]" type="string" size="32" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9hdXRvbG9hZC5waHA=]]></property><property name="TERM" fullname="$_ENV[&quot;TERM&quot;]" type="string" size="5" encoding="base64"><![CDATA[eHRlcm0=]]></property><property type="string" size="76"><name encoding="base64"><![CDATA[UEhQSVpFX0RFUFM=]]></name><fullname encoding="base64"><![CDATA[JF9FTlZbIlBIUElaRV9ERVBTIl0=]]></fullname><value encoding="base64"><![CDATA[YXV0b2NvbmYgCQlkcGtnLWRldiAJCWZpbGUgCQlnKysgCQlnY2MgCQlsaWJjLWRldiAJCW1ha2UgCQlwa2ctY29uZmlnIAkJcmUyYw==]]></value></property><property name="PHP_INI_DIR" fullname="$_ENV[&quot;PHP_INI_DIR&quot;]" type="string" size="18" encoding="base64"><![CDATA[L3Vzci9sb2NhbC9ldGMvcGhw]]></property><property name="PHP_CFLAGS" fullname="$_ENV[&quot;PHP_CFLAGS&quot;]" type="string" size="83" encoding="base64"><![CDATA[LWZzdGFjay1wcm90ZWN0b3Itc3Ryb25nIC1mcGljIC1mcGllIC1PMiAtRF9MQVJHRUZJTEVfU09VUkNFIC1EX0ZJTEVfT0ZGU0VUX0JJVFM9NjQ=]]></property><property name="PHP_CPPFLAGS" fullname="$_ENV[&quot;PHP_CPPFLAGS&quot;]" type="string" size="83" encoding="base64"><![CDATA[LWZzdGFjay1wcm90ZWN0b3Itc3Ryb25nIC1mcGljIC1mcGllIC1PMiAtRF9MQVJHRUZJTEVfU09VUkNFIC1EX0ZJTEVfT0ZGU0VUX0JJVFM9NjQ=]]></property><property name="PHP_LDFLAGS" fullname="$_ENV[&quot;PHP_LDFLAGS&quot;]" type="string" size="12" encoding="base64"><![CDATA[LVdsLC1PMSAtcGll]]></property><property name="GPG_KEYS" fullname="$_ENV[&quot;GPG_KEYS&quot;]" type="string" size="122" encoding="base64"><![CDATA[MzlCNjQxMzQzRDhDMTA0QjJCMTQ2REMzRjlDMzlEQzBCOTY5ODU0NCBFNjA5MTNFNERGMjA5OTA3RDhFMzBEOTY2NTlBOTdDOUNGMkE3OTVBIDExOThDMDExNzU5MzQ5N0E1RUM1QzE5OTI4NkFGMUY5ODk3NDY5REM=]]></property><property name="PHP_VERSION" fullname="$_ENV[&quot;PHP_VERSION&quot;]" type="string" size="6" encoding="base64"><![CDATA[OC4yLjI1]]></property><property name="PHP_URL" fullname="$_ENV[&quot;PHP_URL&quot;]" type="string" size="51" encoding="base64"><![CDATA[aHR0cHM6Ly93d3cucGhwLm5ldC9kaXN0cmlidXRpb25zL3BocC04LjIuMjUudGFyLnh6]]></property><property name="PHP_ASC_URL" fullname="$_ENV[&quot;PHP_ASC_URL&quot;]" type="string" size="55" encoding="base64"><![CDATA[aHR0cHM6Ly93d3cucGhwLm5ldC9kaXN0cmlidXRpb25zL3BocC04LjIuMjUudGFyLnh6LmFzYw==]]></property><property name="PHP_SHA256" fullname="$_ENV[&quot;PHP_SHA256&quot;]" type="string" size="64" encoding="base64"><![CDATA[MzMwYjU0ODc2ZWExZDA1YWRlMTJlZTk3MjYxNjczMzIwNThiY2NkNThkZmZhMWQ0ZTEyMTE3ZjZiNGY2MTZiOQ==]]></property><property name="HOME" fullname="$_ENV[&quot;HOME&quot;]" type="string" size="5" encoding="base64"><![CDATA[L3Jvb3Q=]]></property></property><property name="$_REQUEST" fullname="$_REQUEST" type="array" children="0" numchildren="0" page="0" pagesize="100"></property><property name="$_SERVER" fullname="$_SERVER" type="array" children="1" numchildren="25" page="0" pagesize="100"><property name="PATH" fullname="$_SERVER[&quot;PATH&quot;]" type="string" size="60" encoding="base64"><![CDATA[L3Vzci9sb2NhbC9zYmluOi91c3IvbG9jYWwvYmluOi91c3Ivc2JpbjovdXNyL2Jpbjovc2JpbjovYmlu]]></property><property name="HOSTNAME" fullname="$_SERVER[&quot;HOSTNAME&quot;]" type="string" size="12" encoding="base64"><![CDATA[YjQxYTZmYWZiNmRm]]></property><property name="JETBRAINS_REMOTE_RUN" fullname="$_SERVER[&quot;JETBRAINS_REMOTE_RUN&quot;]" type="string" size="1" encoding="base64"><![CDATA[MQ==]]></property><property name="IDE_PHPUNIT_CUSTOM_LOADER" fullname="$_SERVER[&quot;IDE_PHPUNIT_CUSTOM_LOADER&quot;]" type="string" size="32" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9hdXRvbG9hZC5waHA=]]></property><property name="TERM" fullname="$_SERVER[&quot;TERM&quot;]" type="string" size="5" encoding="base64"><![CDATA[eHRlcm0=]]></property><property type="string" size="76"><name encoding="base64"><![CDATA[UEhQSVpFX0RFUFM=]]></name><fullname encoding="base64"><![CDATA[JF9TRVJWRVJbIlBIUElaRV9ERVBTIl0=]]></fullname><value encoding="base64"><![CDATA[YXV0b2NvbmYgCQlkcGtnLWRldiAJCWZpbGUgCQlnKysgCQlnY2MgCQlsaWJjLWRldiAJCW1ha2UgCQlwa2ctY29uZmlnIAkJcmUyYw==]]></value></property><property name="PHP_INI_DIR" fullname="$_SERVER[&quot;PHP_INI_DIR&quot;]" type="string" size="18" encoding="base64"><![CDATA[L3Vzci9sb2NhbC9ldGMvcGhw]]></property><property name="PHP_CFLAGS" fullname="$_SERVER[&quot;PHP_CFLAGS&quot;]" type="string" size="83" encoding="base64"><![CDATA[LWZzdGFjay1wcm90ZWN0b3Itc3Ryb25nIC1mcGljIC1mcGllIC1PMiAtRF9MQVJHRUZJTEVfU09VUkNFIC1EX0ZJTEVfT0ZGU0VUX0JJVFM9NjQ=]]></property><property name="PHP_CPPFLAGS" fullname="$_SERVER[&quot;PHP_CPPFLAGS&quot;]" type="string" size="83" encoding="base64"><![CDATA[LWZzdGFjay1wcm90ZWN0b3Itc3Ryb25nIC1mcGljIC1mcGllIC1PMiAtRF9MQVJHRUZJTEVfU09VUkNFIC1EX0ZJTEVfT0ZGU0VUX0JJVFM9NjQ=]]></property><property name="PHP_LDFLAGS" fullname="$_SERVER[&quot;PHP_LDFLAGS&quot;]" type="string" size="12" encoding="base64"><![CDATA[LVdsLC1PMSAtcGll]]></property><property name="GPG_KEYS" fullname="$_SERVER[&quot;GPG_KEYS&quot;]" type="string" size="122" encoding="base64"><![CDATA[MzlCNjQxMzQzRDhDMTA0QjJCMTQ2REMzRjlDMzlEQzBCOTY5ODU0NCBFNjA5MTNFNERGMjA5OTA3RDhFMzBEOTY2NTlBOTdDOUNGMkE3OTVBIDExOThDMDExNzU5MzQ5N0E1RUM1QzE5OTI4NkFGMUY5ODk3NDY5REM=]]></property><property name="PHP_VERSION" fullname="$_SERVER[&quot;PHP_VERSION&quot;]" type="string" size="6" encoding="base64"><![CDATA[OC4yLjI1]]></property><property name="PHP_URL" fullname="$_SERVER[&quot;PHP_URL&quot;]" type="string" size="51" encoding="base64"><![CDATA[aHR0cHM6Ly93d3cucGhwLm5ldC9kaXN0cmlidXRpb25zL3BocC04LjIuMjUudGFyLnh6]]></property><property name="PHP_ASC_URL" fullname="$_SERVER[&quot;PHP_ASC_URL&quot;]" type="string" size="55" encoding="base64"><![CDATA[aHR0cHM6Ly93d3cucGhwLm5ldC9kaXN0cmlidXRpb25zL3BocC04LjIuMjUudGFyLnh6LmFzYw==]]></property><property name="PHP_SHA256" fullname="$_SERVER[&quot;PHP_SHA256&quot;]" type="string" size="64" encoding="base64"><![CDATA[MzMwYjU0ODc2ZWExZDA1YWRlMTJlZTk3MjYxNjczMzIwNThiY2NkNThkZmZhMWQ0ZTEyMTE3ZjZiNGY2MTZiOQ==]]></property><property name="HOME" fullname="$_SERVER[&quot;HOME&quot;]" type="string" size="5" encoding="base64"><![CDATA[L3Jvb3Q=]]></property><property name="PHP_SELF" fullname="$_SERVER[&quot;PHP_SELF&quot;]" type="string" size="43" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9waHB1bml0L3BocHVuaXQvcGhwdW5pdA==]]></property><property name="SCRIPT_NAME" fullname="$_SERVER[&quot;SCRIPT_NAME&quot;]" type="string" size="43" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9waHB1bml0L3BocHVuaXQvcGhwdW5pdA==]]></property><property name="SCRIPT_FILENAME" fullname="$_SERVER[&quot;SCRIPT_FILENAME&quot;]" type="string" size="43" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9waHB1bml0L3BocHVuaXQvcGhwdW5pdA==]]></property><property name="PATH_TRANSLATED" fullname="$_SERVER[&quot;PATH_TRANSLATED&quot;]" type="string" size="43" encoding="base64"><![CDATA[L29wdC9wcm9qZWN0L3ZlbmRvci9waHB1bml0L3BocHVuaXQvcGhwdW5pdA==]]></property><property name="DOCUMENT_ROOT" fullname="$_SERVER[&quot;DOCUMENT_ROOT&quot;]" type="string" size="0" encoding="base64"><![CDATA[]]></property><property name="REQUEST_TIME_FLOAT" fullname="$_SERVER[&quot;REQUEST_TIME_FLOAT&quot;]" type="float"><![CDATA[1730574707.649]]></property><property name="REQUEST_TIME" fullname="$_SERVER[&quot;REQUEST_TIME&quot;]" type="int"><![CDATA[1730574707]]></property><property name="argv" fullname="$_SERVER[&quot;argv&quot;]" type="array" children="1" numchildren="9"></property><property name="argc" fullname="$_SERVER[&quot;argc&quot;]" type="int"><![CDATA[9]]></property></property><property name="$requiredExtensions" fullname="$requiredExtensions" type="uninitialized"></property><property name="$unavailableExtensions" fullname="$unavailableExtensions" type="uninitialized"></property><property name="$file" fullname="$file" type="uninitialized"></property></response>

[1] [Step Debug] <- context_get -i 48 -d 0 -c 2
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="context_get" transaction_id="48" context="2"></response>

[1] [Step Debug] <- run -i 49
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="run" transaction_id="49" status="stopping" reason="ok"></response>

[1] [Step Debug] <- detach -i 50
[1] [Step Debug] -> <response xmlns="urn:debugger_protocol_v1" xmlns:xdebug="https://xdebug.org/dbgp/xdebug" command="detach" transaction_id="50" status="stopping" reason="ok"></response>

[1] Log closed at 2024-11-02 19:11:50.082717

