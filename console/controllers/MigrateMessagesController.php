<?php

namespace console\controllers;

use common\chip\event\repositories\EventRepository;
use common\models\EventLog;
use common\models\ProjectMessages;
use Yii;
use yii\console\Controller;
use yii\console\ExitCode;
use yii\db\Expression;
use yii\helpers\Console;

/**
 * Контроллер для миграции сообщений в события
 */
class MigrateMessagesController extends Controller
{
    /**
     * @var int Лимит сообщений для миграции за один запуск
     */
    public $limit = 100;
    
    /**
     * @var bool Пометить исходные сообщения как обработанные
     */
    public $markProcessed = false;
    
    /**
     * @var string Начальная дата для миграции (Y-m-d)
     */
    public $startDate;
    
    /**
     * @var string Конечная дата для миграции (Y-m-d)
     */
    public $endDate;
    
    /**
     * @var bool Режим анализа (без фактического создания событий)
     */
    public $analyzeOnly = false;
    
    /**
     * {@inheritdoc}
     */
    public function options($actionID)
    {
        $options = parent::options($actionID);
        
        if ($actionID === 'messages-to-events') {
            $options[] = 'limit';
            $options[] = 'markProcessed';
            $options[] = 'startDate';
            $options[] = 'endDate';
            $options[] = 'analyzeOnly';
        }
        
        return $options;
    }
    
    /**
     * Мигрирует сообщения проектов в события
     * 
     * @return int Код выхода
     */
    public function actionMessagesToEvents()
    {
        $this->stdout("Миграция сообщений проектов в события\n", Console::FG_GREEN);
        
        // Подготавливаем запрос
        $query = ProjectMessages::find()
            ->orderBy(['id' => SORT_ASC])
            ->limit($this->limit);
        
        // Добавляем фильтр по датам, если они указаны
        if ($this->startDate) {
            $query->andWhere(['>=', 'created_at', $this->startDate . ' 00:00:00']);
        }
        
        if ($this->endDate) {
            $query->andWhere(['<=', 'created_at', $this->endDate . ' 23:59:59']);
        }
        
        // Находим сообщения
        $messages = $query->all();
        
        if (empty($messages)) {
            $this->stdout("Нет сообщений для миграции.\n", Console::FG_YELLOW);
            return ExitCode::OK;
        }
        
        $this->stdout("Найдено " . count($messages) . " сообщений для миграции.\n", Console::FG_GREEN);
        
        if ($this->analyzeOnly) {
            $this->stdout("Режим анализа включен. События не будут созданы.\n", Console::FG_YELLOW);
            
            // Анализируем типы сообщений
            $types = [];
            
            foreach ($messages as $message) {
                $type = $this->getMessageType($message);
                
                if (!isset($types[$type])) {
                    $types[$type] = 0;
                }
                
                $types[$type]++;
            }
            
            $this->stdout("\nАнализ типов сообщений:\n", Console::FG_GREEN);
            
            foreach ($types as $type => $count) {
                $this->stdout("- {$type}: {$count}\n");
            }
            
            return ExitCode::OK;
        }
        
        // Инициализируем репозиторий
        $repository = Yii::$container->get(EventRepository::class);
        
        $migrated = 0;
        $skipped = 0;
        $error = 0;
        
        foreach ($messages as $message) {
            try {
                // Проверяем, есть ли уже событие для этого сообщения
                $existingEvent = EventLog::find()
                    ->where([
                        'entity_type' => 'project_message',
                        'entity_id' => $message->id
                    ])
                    ->exists();
                
                if ($existingEvent) {
                    $this->stdout("Событие для сообщения #{$message->id} уже существует. Пропускаем.\n", Console::FG_YELLOW);
                    $skipped++;
                    continue;
                }
                
                // Создаем событие
                $event = new EventLog();
                $event->event_type = $this->getMessageType($message);
                $event->entity_type = 'project_message';
                $event->entity_id = $message->id;
                $event->payload = json_encode([
                    'project_id' => $message->project_id,
                    'title' => $message->title,
                    'content' => $message->content,
                    'comment' => $message->comment,
                    'type' => $message->type,
                    'sys' => $message->sys,
                    'send_to' => $message->send_to,
                    'path' => $message->path,
                    'filename' => $message->filename,
                ]);
                $event->created_by = $message->created_by;
                
                // Устанавливаем такую же дату создания, как у сообщения
                $event->created_at = $message->created_at;
                
                // Помечаем как обработанное, чтобы не пытаться обработать старые сообщения
                $event->processed = 1;
                $event->processed_at = new Expression('NOW()');
                
                if ($event->save()) {
                    $this->stdout("Создано событие для сообщения #{$message->id}.\n", Console::FG_GREEN);
                    
                    // Если требуется, помечаем исходное сообщение как обработанное
                    if ($this->markProcessed) {
                        $message->is_shown = 1;
                        $message->is_notified = 1;
                        $message->save(false);
                    }
                    
                    $migrated++;
                } else {
                    $this->stderr("Ошибка сохранения события для сообщения #{$message->id}: " . json_encode($event->getErrors()) . "\n", Console::FG_RED);
                    $error++;
                }
            } catch (\Throwable $e) {
                $this->stderr("Ошибка при миграции сообщения #{$message->id}: {$e->getMessage()}\n", Console::FG_RED);
                $error++;
            }
        }
        
        $this->stdout("\nМиграция завершена:\n", Console::FG_GREEN);
        $this->stdout("- Успешно мигрировано: {$migrated}\n");
        $this->stdout("- Пропущено: {$skipped}\n");
        $this->stdout("- Ошибок: {$error}\n");
        
        return ExitCode::OK;
    }
    
    /**
     * Определяет тип события на основе типа сообщения
     * 
     * @param ProjectMessages $message Сообщение проекта
     * @return string Тип события
     */
    private function getMessageType(ProjectMessages $message): string
    {
        // Определяем тип события на основе типа сообщения и других атрибутов
        $prefix = 'project.message';
        
        switch ($message->type) {
            case 1: // TYPE_USUAL
                $type = 'usual';
                break;
            case 2: // TYPE_SYS
                $type = 'system';
                break;
            case 3: // TYPE_NOTE
                $type = 'note';
                break;
            case 4: // TYPE_RELOAD
                $type = 'reload';
                break;
            default:
                $type = 'unknown';
        }
        
        // Добавляем информацию о системности
        if ($message->sys == 1) {
            $type .= '.system';
        }
        
        return $prefix . '.' . $type;
    }
}
