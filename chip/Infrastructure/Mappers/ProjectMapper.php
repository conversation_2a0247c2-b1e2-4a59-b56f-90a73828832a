<?php

declare(strict_types=1);

namespace chip\Infrastructure\Mappers;

use chip\Domain\Entities\Project;

final class ProjectMapper
{
    public function toDomain(array $data): Project
    {
        
    }
    
    public function toArray(Project $project): array
    {
        
    }
    
    public function toModel(Project $project): object
    {
        
    }
    
    public function fromModel(object $model): Project
    {
        
    }
}
