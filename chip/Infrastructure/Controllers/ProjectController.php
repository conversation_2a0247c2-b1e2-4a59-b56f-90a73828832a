<?php

declare(strict_types=1);

namespace chip\Infrastructure\Controllers;

use chip\Application\Handlers\CreateProjectHandler;
use chip\Application\Handlers\GetProjectHandler;
use chip\Application\Commands\CreateProjectCommand;
use chip\Application\Queries\GetProjectQuery;

final class ProjectController
{
    public function __construct(
        private CreateProjectHandler $createProjectHandler,
        private GetProjectHandler $getProjectHandler
    ) {}
    
    public function create(): array
    {
        
    }
    
    public function view(string $id): array
    {
        
    }
    
    public function list(): array
    {
        
    }
    
    public function update(string $id): array
    {
        
    }
    
    public function delete(string $id): array
    {
        
    }
}
