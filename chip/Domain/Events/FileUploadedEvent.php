<?php

declare(strict_types=1);

namespace chip\Domain\Events;

use chip\Domain\ValueObjects\ProjectId;
use chip\Domain\ValueObjects\FileId;
use chip\Domain\ValueObjects\FileName;

final class FileUploadedEvent extends DomainEvent
{
    public function __construct(
        private ProjectId $projectId,
        private FileId $fileId,
        private FileName $fileName
    ) {
        parent::__construct();
    }
    
    public function getEventName(): string
    {
        return 'file.uploaded';
    }
    
    public function getPayload(): array
    {
        return [
            'projectId' => $this->projectId->value(),
            'fileId' => $this->fileId->value(),
            'fileName' => $this->fileName->value(),
            'occurredAt' => $this->getOccurredAt()->format('Y-m-d H:i:s'),
        ];
    }
    
    public function getProjectId(): ProjectId
    {
        return $this->projectId;
    }
    
    public function getFileId(): FileId
    {
        return $this->fileId;
    }
    
    public function getFileName(): FileName
    {
        return $this->fileName;
    }
}
