<?php

declare(strict_types=1);

namespace chip\Domain\Events;

use chip\Domain\ValueObjects\ProjectId;
use chip\Domain\ValueObjects\ClientId;
use chip\Domain\ValueObjects\VehicleInfo;

final class ProjectCreatedEvent extends DomainEvent
{
    public function __construct(
        private ProjectId $projectId,
        private ClientId $clientId,
        private VehicleInfo $vehicleInfo
    ) {
        parent::__construct();
    }
    
    public function getEventName(): string
    {
        return 'project.created';
    }
    
    public function getPayload(): array
    {
        return [
            'projectId' => $this->projectId->value(),
            'clientId' => $this->clientId->value(),
            'vehicleInfo' => $this->vehicleInfo->toArray(),
            'occurredAt' => $this->getOccurredAt()->format('Y-m-d H:i:s'),
        ];
    }
    
    public function getProjectId(): ProjectId
    {
        return $this->projectId;
    }
    
    public function getClientId(): ClientId
    {
        return $this->clientId;
    }
    
    public function getVehicleInfo(): VehicleInfo
    {
        return $this->vehicleInfo;
    }
}
