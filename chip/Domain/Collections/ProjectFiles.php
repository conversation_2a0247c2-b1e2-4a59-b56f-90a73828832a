<?php

declare(strict_types=1);

namespace chip\Domain\Collections;

use chip\Domain\Entities\ProjectFile;
use chip\Domain\ValueObjects\FileId;

final class ProjectFiles implements \IteratorAggregate, \Countable
{
    /** @var ProjectFile[] */
    private array $files = [];
    
    public function add(ProjectFile $file): void
    {
        $this->files[$file->getId()->value()] = $file;
    }
    
    public function remove(FileId $fileId): void
    {
        unset($this->files[$fileId->value()]);
    }
    
    public function findById(FileId $fileId): ?ProjectFile
    {
        return $this->files[$fileId->value()] ?? null;
    }
    
    public function getIterator(): \ArrayIterator
    {
        return new \ArrayIterator($this->files);
    }
    
    public function count(): int
    {
        return count($this->files);
    }
    
    public function isEmpty(): bool
    {
        return empty($this->files);
    }
    
    public function toArray(): array
    {
        return array_values($this->files);
    }
}
