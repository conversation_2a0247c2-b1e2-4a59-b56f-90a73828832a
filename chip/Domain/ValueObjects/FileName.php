<?php

declare(strict_types=1);

namespace chip\Domain\ValueObjects;

final class FileName
{
    private string $value;
    
    public function __construct(string $value)
    {
        if (empty(trim($value))) {
            throw new \InvalidArgumentException('File name cannot be empty');
        }
        
        $this->value = $value;
    }
    
    public function value(): string
    {
        return $this->value;
    }
    
    public function getExtension(): string
    {
        return pathinfo($this->value, PATHINFO_EXTENSION);
    }
    
    public function getBaseName(): string
    {
        return pathinfo($this->value, PATHINFO_FILENAME);
    }
    
    public function equals(self $other): bool
    {
        return $this->value === $other->value;
    }
    
    public function __toString(): string
    {
        return $this->value;
    }
}
