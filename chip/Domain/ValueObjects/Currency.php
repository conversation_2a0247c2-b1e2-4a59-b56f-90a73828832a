<?php

declare(strict_types=1);

namespace chip\Domain\ValueObjects;

final class Currency
{
    public const USD = 'USD';
    public const EUR = 'EUR';
    public const RUB = 'RUB';
    
    private string $value;
    
    private function __construct(string $value)
    {
        $this->value = $value;
    }
    
    public static function usd(): self { return new self(self::USD); }
    public static function eur(): self { return new self(self::EUR); }
    public static function rub(): self { return new self(self::RUB); }
    
    public function equals(self $other): bool
    {
        return $this->value === $other->value;
    }
    
    public function value(): string
    {
        return $this->value;
    }
    
    public function __toString(): string
    {
        return $this->value;
    }
}
