<?php

declare(strict_types=1);

namespace chip\Domain\ValueObjects;

final class FileStatus
{
    public const UPLOADED = 'uploaded';
    public const PROCESSING = 'processing';
    public const COMPLETED = 'completed';
    public const FAILED = 'failed';
    
    private string $value;
    private ?string $reason = null;
    
    private function __construct(string $value, ?string $reason = null)
    {
        $this->value = $value;
        $this->reason = $reason;
    }
    
    public static function uploaded(): self { return new self(self::UPLOADED); }
    public static function processing(): self { return new self(self::PROCESSING); }
    public static function completed(): self { return new self(self::COMPLETED); }
    public static function failed(string $reason): self { return new self(self::FAILED, $reason); }
    
    public function canTransitionTo(self $newStatus): bool
    {
        $transitions = [
            self::UPLOADED => [self::PROCESSING],
            self::PROCESSING => [self::COMPLETED, self::FAILED],
            self::COMPLETED => [],
            self::FAILED => [self::PROCESSING],
        ];
        
        return in_array($newStatus->value, $transitions[$this->value] ?? [], true);
    }
    
    public function equals(self $other): bool
    {
        return $this->value === $other->value;
    }
    
    public function value(): string
    {
        return $this->value;
    }
    
    public function getReason(): ?string
    {
        return $this->reason;
    }
    
    public function __toString(): string
    {
        return $this->value;
    }
}
