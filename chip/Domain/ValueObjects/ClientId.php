<?php

declare(strict_types=1);

namespace chip\Domain\ValueObjects;

final class ClientId
{
    private string $value;
    
    public function __construct(string $value)
    {
        if (empty($value)) {
            throw new \InvalidArgumentException('Client ID cannot be empty');
        }
        
        $this->value = $value;
    }
    
    public function value(): string
    {
        return $this->value;
    }
    
    public function equals(self $other): bool
    {
        return $this->value === $other->value;
    }
    
    public function __toString(): string
    {
        return $this->value;
    }
}
