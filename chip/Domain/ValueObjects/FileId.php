<?php

declare(strict_types=1);

namespace chip\Domain\ValueObjects;

final class FileId
{
    private string $value;
    
    public function __construct(string $value)
    {
        if (empty($value)) {
            throw new \InvalidArgumentException('File ID cannot be empty');
        }
        
        $this->value = $value;
    }
    
    public static function generate(): self
    {
        return new self(\Ramsey\Uuid\Uuid::uuid4()->toString());
    }
    
    public function value(): string
    {
        return $this->value;
    }
    
    public function equals(self $other): bool
    {
        return $this->value === $other->value;
    }
    
    public function __toString(): string
    {
        return $this->value;
    }
}
