<?php

declare(strict_types=1);

namespace chip\Domain\ValueObjects;

final class FileType
{
    public const BIN = 'bin';
    public const HEX = 'hex';
    public const KESS = 'kess';
    public const KTAG = 'ktag';
    public const AUTOPACK = 'autopack';
    public const ORI = 'ori';
    public const MOD = 'mod';
    
    private string $value;
    
    private function __construct(string $value)
    {
        $this->value = $value;
    }
    
    public static function bin(): self { return new self(self::BIN); }
    public static function hex(): self { return new self(self::HEX); }
    public static function kess(): self { return new self(self::KESS); }
    public static function ktag(): self { return new self(self::KTAG); }
    public static function autopack(): self { return new self(self::AUTOPACK); }
    public static function ori(): self { return new self(self::ORI); }
    public static function mod(): self { return new self(self::MOD); }
    
    public static function fromExtension(string $extension): self
    {
        return match(strtolower($extension)) {
            'bin' => self::bin(),
            'hex' => self::hex(),
            'kess' => self::kess(),
            'ktag' => self::ktag(),
            'ori' => self::ori(),
            'mod' => self::mod(),
            default => throw new \InvalidArgumentException("Unsupported file extension: {$extension}")
        };
    }
    
    public function isProcessable(): bool
    {
        return in_array($this->value, [self::BIN, self::HEX, self::KESS, self::KTAG], true);
    }
    
    public function isOneOf(array $types): bool
    {
        return in_array($this->value, array_map(fn($type) => $type->value, $types), true);
    }
    
    public function equals(self $other): bool
    {
        return $this->value === $other->value;
    }
    
    public function value(): string
    {
        return $this->value;
    }
    
    public function __toString(): string
    {
        return $this->value;
    }
}
