<?php

declare(strict_types=1);

namespace chip\Domain\ValueObjects;

final class ProjectStatus
{
    public const CREATED = 'created';
    public const FILE_UPLOADED = 'file_uploaded';
    public const PROCESSING = 'processing';
    public const COMPLETED = 'completed';
    public const FAILED = 'failed';
    public const CANCELLED = 'cancelled';
    
    private string $value;
    
    private function __construct(string $value)
    {
        $this->value = $value;
    }
    
    public static function created(): self { return new self(self::CREATED); }
    public static function fileUploaded(): self { return new self(self::FILE_UPLOADED); }
    public static function processing(): self { return new self(self::PROCESSING); }
    public static function completed(): self { return new self(self::COMPLETED); }
    public static function failed(): self { return new self(self::FAILED); }
    public static function cancelled(): self { return new self(self::CANCELLED); }
    
    public function canTransitionTo(self $newStatus): bool
    {
        $transitions = [
            self::CREATED => [self::FILE_UPLOADED, self::CANCELLED],
            self::FILE_UPLOADED => [self::PROCESSING, self::CANCELLED],
            self::PROCESSING => [self::COMPLETED, self::FAILED],
            self::COMPLETED => [],
            self::FAILED => [self::PROCESSING],
            self::CANCELLED => [],
        ];
        
        return in_array($newStatus->value, $transitions[$this->value] ?? [], true);
    }
    
    public function isOneOf(array $statuses): bool
    {
        return in_array($this->value, array_map(fn($status) => $status->value, $statuses), true);
    }
    
    public function equals(self $other): bool
    {
        return $this->value === $other->value;
    }
    
    public function value(): string
    {
        return $this->value;
    }
    
    public function __toString(): string
    {
        return $this->value;
    }
}
