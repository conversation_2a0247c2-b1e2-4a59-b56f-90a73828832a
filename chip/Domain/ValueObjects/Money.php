<?php

declare(strict_types=1);

namespace chip\Domain\ValueObjects;

final class Money
{
    private int $amount; // Amount in cents
    private Currency $currency;
    
    public function __construct(int $amount, Currency $currency)
    {
        if ($amount < 0) {
            throw new \InvalidArgumentException('Amount cannot be negative');
        }
        
        $this->amount = $amount;
        $this->currency = $currency;
    }
    
    public static function fromFloat(float $amount, Currency $currency): self
    {
        return new self((int) round($amount * 100), $currency);
    }
    
    public function add(self $other): self
    {
        $this->ensureSameCurrency($other);
        return new self($this->amount + $other->amount, $this->currency);
    }
    
    public function subtract(self $other): self
    {
        $this->ensureSameCurrency($other);
        if ($this->amount < $other->amount) {
            throw new \InvalidArgumentException('Cannot subtract larger amount');
        }
        return new self($this->amount - $other->amount, $this->currency);
    }
    
    public function multiply(float $multiplier): self
    {
        return new self((int) round($this->amount * $multiplier), $this->currency);
    }
    
    public function equals(self $other): bool
    {
        return $this->amount === $other->amount && 
               $this->currency->equals($other->currency);
    }
    
    public function getAmount(): int { return $this->amount; }
    public function getCurrency(): Currency { return $this->currency; }
    
    public function toFloat(): float
    {
        return $this->amount / 100;
    }
    
    private function ensureSameCurrency(self $other): void
    {
        if (!$this->currency->equals($other->currency)) {
            throw new \InvalidArgumentException('Cannot operate on different currencies');
        }
    }
}
