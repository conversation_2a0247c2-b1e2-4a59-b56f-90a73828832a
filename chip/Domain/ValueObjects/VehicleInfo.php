<?php

declare(strict_types=1);

namespace chip\Domain\ValueObjects;

final class VehicleInfo
{
    private Brand $brand;
    private Model $model;
    private Year $year;
    private EngineType $engineType;
    
    public function __construct(
        Brand $brand,
        Model $model,
        Year $year,
        EngineType $engineType
    ) {
        $this->brand = $brand;
        $this->model = $model;
        $this->year = $year;
        $this->engineType = $engineType;
    }
    
    public static function fromArray(array $data): self
    {
        return new self(
            new Brand($data['brand']),
            new Model($data['model']),
            new Year($data['year']),
            new EngineType($data['engineType'])
        );
    }
    
    public function getBrand(): Brand { return $this->brand; }
    public function getModel(): Model { return $this->model; }
    public function getYear(): Year { return $this->year; }
    public function getEngineType(): EngineType { return $this->engineType; }
    
    public function isValid(): bool
    {
        
    }
    
    public function equals(self $other): bool
    {
        return $this->brand->equals($other->brand) &&
               $this->model->equals($other->model) &&
               $this->year->equals($other->year) &&
               $this->engineType->equals($other->engineType);
    }
    
    public function toArray(): array
    {
        return [
            'brand' => $this->brand->value(),
            'model' => $this->model->value(),
            'year' => $this->year->value(),
            'engineType' => $this->engineType->value(),
        ];
    }
}
