<?php

declare(strict_types=1);

namespace chip\Domain\Repositories;

use chip\Domain\Entities\Project;
use chip\Domain\ValueObjects\ProjectId;
use chip\Domain\ValueObjects\ClientId;
use chip\Domain\ValueObjects\ProjectStatus;

interface ProjectRepositoryInterface
{
    public function save(Project $project): void;
    
    public function findById(ProjectId $id): ?Project;
    
    public function findByClientId(ClientId $clientId): array;
    
    public function findByStatus(ProjectStatus $status): array;
    
    public function findActiveProjects(): array;
    
    public function delete(ProjectId $id): void;
    
    public function exists(ProjectId $id): bool;
    
    public function count(): int;
    
    public function findProjectsCreatedAfter(\DateTimeImmutable $date): array;
    
    public function findProjectsWithStatus(array $statuses): array;
}
