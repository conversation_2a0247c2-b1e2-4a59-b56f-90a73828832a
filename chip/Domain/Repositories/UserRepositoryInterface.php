<?php

declare(strict_types=1);

namespace chip\Domain\Repositories;

use chip\Domain\Entities\User;
use chip\Domain\ValueObjects\UserId;
use chip\Domain\ValueObjects\UserEmail;

interface UserRepositoryInterface
{
    public function save(User $user): void;
    
    public function findById(UserId $id): ?User;
    
    public function findByEmail(UserEmail $email): ?User;
    
    public function findAll(): array;
    
    public function delete(UserId $id): void;
    
    public function exists(UserId $id): bool;
    
    public function existsByEmail(UserEmail $email): bool;
}
