<?php

declare(strict_types=1);

namespace chip\Domain\Services;

use chip\Domain\Entities\ProjectFile;
use chip\Domain\ValueObjects\FileType;
use chip\Domain\ValueObjects\FileChecksum;
use chip\Domain\ValueObjects\ProcessingOptions;
use chip\Domain\Exceptions\FileException;

final class FileProcessingService
{
    public function validateFileForProcessing(ProjectFile $file): void
    {
        
    }
    
    public function canFileBeProcessed(ProjectFile $file): bool
    {
        
    }
    
    public function determineProcessingStrategy(ProjectFile $file): string
    {
        
    }
    
    public function validateFileIntegrity(ProjectFile $file, FileChecksum $expectedChecksum): bool
    {
        
    }
    
    public function getRequiredProcessingOptions(FileType $fileType): ProcessingOptions
    {
        
    }
    
    public function estimateProcessingTime(ProjectFile $file): int
    {
        
    }
    
    public function isFileCorrupted(ProjectFile $file): bool
    {
        
    }
}
