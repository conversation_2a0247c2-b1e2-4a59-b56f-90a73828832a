<?php

declare(strict_types=1);

namespace chip\Domain\Services;

use chip\Domain\Entities\Project;
use chip\Domain\Entities\User;
use chip\Domain\ValueObjects\ProjectId;
use chip\Domain\ValueObjects\ClientId;
use chip\Domain\ValueObjects\VehicleInfo;
use chip\Domain\ValueObjects\EcuInfo;
use chip\Domain\ValueObjects\Money;
use chip\Domain\Exceptions\ProjectException;

final class ProjectDomainService
{
    public function __construct(
        private PricingService $pricingService,
        private ProjectValidationService $validationService
    ) {}
    
    public function createProject(
        ClientId $clientId,
        VehicleInfo $vehicleInfo,
        EcuInfo $ecuInfo,
        User $user
    ): Project {
        $this->validationService->validateProjectCreation($clientId, $vehicleInfo, $ecuInfo, $user);
        
        $projectId = ProjectId::generate();
        $cost = $this->pricingService->calculateProjectCost($vehicleInfo, $ecuInfo);
        
        return Project::create($projectId, $clientId, $vehicleInfo, $ecuInfo, $cost);
    }
    
    public function canUserAccessProject(User $user, Project $project): bool
    {
        
    }
    
    public function canProjectBeProcessed(Project $project): bool
    {
        
    }
    
    public function calculateProjectPriority(Project $project): int
    {
        
    }
    
    public function isProjectEligibleForDiscount(Project $project): bool
    {
        
    }
    
    public function validateProjectCompletion(Project $project): void
    {
        
    }
}
