<?php

declare(strict_types=1);

namespace chip\Domain\Entities;

use chip\Domain\ValueObjects\UserId;
use chip\Domain\ValueObjects\UserEmail;
use chip\Domain\ValueObjects\UserRole;
use chip\Domain\ValueObjects\UserProfile;
use chip\Domain\ValueObjects\NotificationPreferences;
use chip\Domain\ValueObjects\ProjectId;
use chip\Domain\ValueObjects\Permission;

final class User
{
    private UserId $id;
    private UserEmail $email;
    private UserRole $role;
    private UserProfile $profile;
    private NotificationPreferences $notificationPreferences;
    private \DateTimeImmutable $createdAt;
    private ?\DateTimeImmutable $lastLoginAt = null;
    
    public function __construct(
        UserId $id,
        UserEmail $email,
        UserRole $role,
        UserProfile $profile
    ) {
        $this->id = $id;
        $this->email = $email;
        $this->role = $role;
        $this->profile = $profile;
        $this->notificationPreferences = NotificationPreferences::default();
        $this->createdAt = new \DateTimeImmutable();
    }
    
    public function canAccessProject(ProjectId $projectId): bool
    {
        
    }
    
    public function hasPermission(Permission $permission): bool
    {
        
    }
    
    public function changeRole(UserRole $newRole): void
    {
        $this->role = $newRole;
    }
    
    public function updateProfile(UserProfile $profile): void
    {
        $this->profile = $profile;
    }
    
    public function updateNotificationPreferences(NotificationPreferences $preferences): void
    {
        $this->notificationPreferences = $preferences;
    }
    
    public function recordLogin(): void
    {
        $this->lastLoginAt = new \DateTimeImmutable();
    }
    
    private function isProjectOwner(ProjectId $projectId): bool
    {
        
    }
    
    // Getters
    public function getId(): UserId { return $this->id; }
    public function getEmail(): UserEmail { return $this->email; }
    public function getRole(): UserRole { return $this->role; }
    public function getProfile(): UserProfile { return $this->profile; }
    public function getNotificationPreferences(): NotificationPreferences { return $this->notificationPreferences; }
    public function getCreatedAt(): \DateTimeImmutable { return $this->createdAt; }
    public function getLastLoginAt(): ?\DateTimeImmutable { return $this->lastLoginAt; }
}
