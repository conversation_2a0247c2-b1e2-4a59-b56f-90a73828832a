<?php

declare(strict_types=1);

namespace chip\Domain\Entities;

use chip\Domain\ValueObjects\ProjectId;
use chip\Domain\ValueObjects\ClientId;
use chip\Domain\ValueObjects\ProjectStatus;
use chip\Domain\ValueObjects\VehicleInfo;
use chip\Domain\ValueObjects\EcuInfo;
use chip\Domain\ValueObjects\Money;
use chip\Domain\Events\ProjectCreatedEvent;
use chip\Domain\Events\ProjectStatusChangedEvent;
use chip\Domain\Events\FileUploadedEvent;
use chip\Domain\Exceptions\ProjectException;

final class Project
{
    private ProjectId $id;
    private ClientId $clientId;
    private ProjectStatus $status;
    private VehicleInfo $vehicleInfo;
    private EcuInfo $ecuInfo;
    private ProjectFiles $files;
    private ProjectTimeline $timeline;
    private Money $cost;
    private \DateTimeImmutable $createdAt;
    private ?\DateTimeImmutable $completedAt = null;
    
    /** @var DomainEvent[] */
    private array $domainEvents = [];
    
    private function __construct(
        ProjectId $id,
        ClientId $clientId,
        VehicleInfo $vehicleInfo,
        EcuInfo $ecuInfo,
        Money $cost
    ) {
        $this->id = $id;
        $this->clientId = $clientId;
        $this->vehicleInfo = $vehicleInfo;
        $this->ecuInfo = $ecuInfo;
        $this->cost = $cost;
        $this->status = ProjectStatus::created();
        $this->files = new ProjectFiles();
        $this->timeline = new ProjectTimeline();
        $this->createdAt = new \DateTimeImmutable();
        
        $this->recordEvent(new ProjectCreatedEvent($this->id, $this->clientId, $this->vehicleInfo));
    }
    
    public static function create(
        ProjectId $id,
        ClientId $clientId,
        VehicleInfo $vehicleInfo,
        EcuInfo $ecuInfo,
        Money $cost
    ): self {
        return new self($id, $clientId, $vehicleInfo, $ecuInfo, $cost);
    }
    
    public function uploadFile(ProjectFile $file): void
    {
        if (!$this->canUploadFiles()) {
            throw new ProjectException('Cannot upload files in current status');
        }
        
        $this->files->add($file);
        $this->changeStatus(ProjectStatus::fileUploaded());
        
        $this->recordEvent(new FileUploadedEvent($this->id, $file->getId(), $file->getName()));
    }
    
    public function startProcessing(): void
    {
        if (!$this->canStartProcessing()) {
            throw new ProjectException('Cannot start processing in current status');
        }
        
        $this->changeStatus(ProjectStatus::processing());
    }
    
    public function complete(): void
    {
        if (!$this->canComplete()) {
            throw new ProjectException('Cannot complete project in current status');
        }
        
        $this->changeStatus(ProjectStatus::completed());
        $this->completedAt = new \DateTimeImmutable();
    }
    
    public function addNote(Note $note): void
    {
        
    }
    
    private function changeStatus(ProjectStatus $newStatus): void
    {
        if (!$this->status->canTransitionTo($newStatus)) {
            throw new ProjectException("Cannot transition from {$this->status} to {$newStatus}");
        }
        
        $oldStatus = $this->status;
        $this->status = $newStatus;
        
        $this->recordEvent(new ProjectStatusChangedEvent($this->id, $oldStatus, $newStatus));
    }
    
    private function canUploadFiles(): bool
    {
        
    }
    
    private function canStartProcessing(): bool
    {
        
    }
    
    private function canComplete(): bool
    {
        
    }
    
    private function recordEvent(DomainEvent $event): void
    {
        $this->domainEvents[] = $event;
    }
    
    public function releaseEvents(): array
    {
        $events = $this->domainEvents;
        $this->domainEvents = [];
        return $events;
    }
    
    // Getters
    public function getId(): ProjectId { return $this->id; }
    public function getClientId(): ClientId { return $this->clientId; }
    public function getStatus(): ProjectStatus { return $this->status; }
    public function getVehicleInfo(): VehicleInfo { return $this->vehicleInfo; }
    public function getFiles(): ProjectFiles { return $this->files; }
    public function getCost(): Money { return $this->cost; }
    public function getCreatedAt(): \DateTimeImmutable { return $this->createdAt; }
    public function getCompletedAt(): ?\DateTimeImmutable { return $this->completedAt; }
}
