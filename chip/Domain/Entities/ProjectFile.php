<?php

declare(strict_types=1);

namespace chip\Domain\Entities;

use chip\Domain\ValueObjects\FileId;
use chip\Domain\ValueObjects\ProjectId;
use chip\Domain\ValueObjects\FileName;
use chip\Domain\ValueObjects\FileType;
use chip\Domain\ValueObjects\FileStatus;
use chip\Domain\ValueObjects\FilePath;
use chip\Domain\ValueObjects\FileSize;
use chip\Domain\ValueObjects\FileChecksum;
use chip\Domain\Exceptions\FileException;

final class ProjectFile
{
    private FileId $id;
    private ProjectId $projectId;
    private FileName $name;
    private FileType $type;
    private FileStatus $status;
    private FilePath $path;
    private FileSize $size;
    private FileChecksum $checksum;
    private \DateTimeImmutable $uploadedAt;
    private ?\DateTimeImmutable $processedAt = null;
    
    public function __construct(
        FileId $id,
        ProjectId $projectId,
        FileName $name,
        FileType $type,
        FilePath $path,
        FileSize $size,
        FileChecksum $checksum
    ) {
        $this->id = $id;
        $this->projectId = $projectId;
        $this->name = $name;
        $this->type = $type;
        $this->path = $path;
        $this->size = $size;
        $this->checksum = $checksum;
        $this->status = FileStatus::uploaded();
        $this->uploadedAt = new \DateTimeImmutable();
    }
    
    public function markAsProcessing(): void
    {
        if (!$this->status->canTransitionTo(FileStatus::processing())) {
            throw new FileException('Cannot mark file as processing');
        }
        
        $this->status = FileStatus::processing();
    }
    
    public function markAsCompleted(): void
    {
        if (!$this->status->canTransitionTo(FileStatus::completed())) {
            throw new FileException('Cannot mark file as completed');
        }
        
        $this->status = FileStatus::completed();
        $this->processedAt = new \DateTimeImmutable();
    }
    
    public function markAsFailed(string $reason): void
    {
        $this->status = FileStatus::failed($reason);
    }
    
    public function isProcessable(): bool
    {
        
    }
    
    public function updatePath(FilePath $path): void
    {
        $this->path = $path;
    }
    
    // Getters
    public function getId(): FileId { return $this->id; }
    public function getProjectId(): ProjectId { return $this->projectId; }
    public function getName(): FileName { return $this->name; }
    public function getType(): FileType { return $this->type; }
    public function getStatus(): FileStatus { return $this->status; }
    public function getPath(): FilePath { return $this->path; }
    public function getSize(): FileSize { return $this->size; }
    public function getChecksum(): FileChecksum { return $this->checksum; }
    public function getUploadedAt(): \DateTimeImmutable { return $this->uploadedAt; }
    public function getProcessedAt(): ?\DateTimeImmutable { return $this->processedAt; }
}
