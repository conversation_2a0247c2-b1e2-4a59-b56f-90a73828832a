<?php

declare(strict_types=1);

namespace chip\Domain\Exceptions;

final class ProjectException extends \DomainException
{
    public static function invalidStatus(string $message): self
    {
        return new self("Invalid project status: {$message}");
    }
    
    public static function cannotUploadFiles(string $reason): self
    {
        return new self("Cannot upload files: {$reason}");
    }
    
    public static function cannotStartProcessing(string $reason): self
    {
        return new self("Cannot start processing: {$reason}");
    }
}
