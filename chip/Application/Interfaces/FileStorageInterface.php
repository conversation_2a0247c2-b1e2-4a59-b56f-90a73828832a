<?php

declare(strict_types=1);

namespace chip\Application\Interfaces;

use chip\Domain\Entities\ProjectFile;
use chip\Domain\ValueObjects\FilePath;

interface FileStorageInterface
{
    public function store(ProjectFile $file): void;
    
    public function retrieve(FilePath $path): string;
    
    public function delete(FilePath $path): void;
    
    public function exists(FilePath $path): bool;
    
    public function move(FilePath $from, FilePath $to): void;
    
    public function getSize(FilePath $path): int;
}
