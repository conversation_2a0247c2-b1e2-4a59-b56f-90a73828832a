<?php

declare(strict_types=1);

namespace chip\Application\Commands;

use chip\Domain\ValueObjects\UserId;
use chip\Domain\ValueObjects\ClientId;
use chip\Domain\ValueObjects\VehicleInfo;
use chip\Domain\ValueObjects\EcuInfo;

final class CreateProjectCommand
{
    public function __construct(
        private UserId $userId,
        private ClientId $clientId,
        private VehicleInfo $vehicleInfo,
        private EcuInfo $ecuInfo,
        private array $options = []
    ) {}
    
    public function getUserId(): UserId
    {
        return $this->userId;
    }
    
    public function getClientId(): ClientId
    {
        return $this->clientId;
    }
    
    public function getVehicleInfo(): VehicleInfo
    {
        return $this->vehicleInfo;
    }
    
    public function getEcuInfo(): EcuInfo
    {
        return $this->ecuInfo;
    }
    
    public function getOptions(): array
    {
        return $this->options;
    }
}
