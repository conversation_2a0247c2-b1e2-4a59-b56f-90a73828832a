<?php

declare(strict_types=1);

namespace chip\Application\Commands;

use chip\Domain\ValueObjects\ProjectId;
use chip\Domain\ValueObjects\FileId;
use chip\Domain\ValueObjects\FileName;
use chip\Domain\ValueObjects\FileType;
use chip\Domain\ValueObjects\FilePath;
use chip\Domain\ValueObjects\FileSize;
use chip\Domain\ValueObjects\FileChecksum;

final class UploadFileCommand
{
    public function __construct(
        private ProjectId $projectId,
        private FileId $fileId,
        private FileName $fileName,
        private FileType $fileType,
        private FilePath $filePath,
        private FileSize $fileSize,
        private FileChecksum $fileChecksum
    ) {}
    
    public function getProjectId(): ProjectId
    {
        return $this->projectId;
    }
    
    public function getFileId(): FileId
    {
        return $this->fileId;
    }
    
    public function getFileName(): FileName
    {
        return $this->fileName;
    }
    
    public function getFileType(): FileType
    {
        return $this->fileType;
    }
    
    public function getFilePath(): FilePath
    {
        return $this->filePath;
    }
    
    public function getFileSize(): FileSize
    {
        return $this->fileSize;
    }
    
    public function getFileChecksum(): FileChecksum
    {
        return $this->fileChecksum;
    }
}
