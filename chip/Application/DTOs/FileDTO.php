<?php

declare(strict_types=1);

namespace chip\Application\DTOs;

use chip\Domain\Entities\ProjectFile;

final class FileDTO
{
    public function __construct(
        public readonly string $id,
        public readonly string $projectId,
        public readonly string $name,
        public readonly string $type,
        public readonly string $status,
        public readonly string $path,
        public readonly int $size,
        public readonly string $checksum,
        public readonly string $uploadedAt,
        public readonly ?string $processedAt = null
    ) {}
    
    public static function fromEntity(ProjectFile $file): self
    {
        return new self(
            id: $file->getId()->value(),
            projectId: $file->getProjectId()->value(),
            name: $file->getName()->value(),
            type: $file->getType()->value(),
            status: $file->getStatus()->value(),
            path: $file->getPath()->value(),
            size: $file->getSize()->value(),
            checksum: $file->getChecksum()->value(),
            uploadedAt: $file->getUploadedAt()->format('Y-m-d H:i:s'),
            processedAt: $file->getProcessedAt()?->format('Y-m-d H:i:s')
        );
    }
}
