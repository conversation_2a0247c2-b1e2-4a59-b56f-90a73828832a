<?php

declare(strict_types=1);

namespace chip\Application\DTOs;

use chip\Domain\Entities\Project;

final class ProjectDTO
{
    public function __construct(
        public readonly string $id,
        public readonly string $clientId,
        public readonly string $status,
        public readonly array $vehicleInfo,
        public readonly array $ecuInfo,
        public readonly array $cost,
        public readonly string $createdAt,
        public readonly ?string $completedAt = null,
        public readonly array $files = []
    ) {}
    
    public static function fromEntity(Project $project): self
    {
        return new self(
            id: $project->getId()->value(),
            clientId: $project->getClientId()->value(),
            status: $project->getStatus()->value(),
            vehicleInfo: $project->getVehicleInfo()->toArray(),
            ecuInfo: [], // TODO: implement when EcuInfo is created
            cost: [
                'amount' => $project->getCost()->getAmount(),
                'currency' => $project->getCost()->getCurrency()->value()
            ],
            createdAt: $project->getCreatedAt()->format('Y-m-d H:i:s'),
            completedAt: $project->getCompletedAt()?->format('Y-m-d H:i:s'),
            files: [] // TODO: implement when files collection is ready
        );
    }
}
