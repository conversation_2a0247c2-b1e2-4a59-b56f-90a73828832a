<?php

declare(strict_types=1);

namespace chip\Application\Queries;

use chip\Domain\ValueObjects\UserId;
use chip\Domain\ValueObjects\ProjectStatus;

final class GetProjectListQuery
{
    public function __construct(
        private ?UserId $userId = null,
        private ?ProjectStatus $status = null,
        private int $limit = 20,
        private int $offset = 0
    ) {}
    
    public function getUserId(): ?UserId
    {
        return $this->userId;
    }
    
    public function getStatus(): ?ProjectStatus
    {
        return $this->status;
    }
    
    public function getLimit(): int
    {
        return $this->limit;
    }
    
    public function getOffset(): int
    {
        return $this->offset;
    }
}
