<?php

declare(strict_types=1);

namespace chip\Application\Queries;

use chip\Domain\ValueObjects\ProjectId;
use chip\Domain\ValueObjects\UserId;

final class GetProjectQuery
{
    public function __construct(
        private ProjectId $projectId,
        private ?UserId $requestingUserId = null
    ) {}
    
    public function getProjectId(): ProjectId
    {
        return $this->projectId;
    }
    
    public function getRequestingUserId(): ?UserId
    {
        return $this->requestingUserId;
    }
}
