<?php

use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $form yii\widgets\ActiveForm */

$this->title = Yii::t('frontend', 'Signup');
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="container">
    <div class="row">
        <div class="col-sm-12">
            <?php if(Yii::$app->session->hasFlash('alert')):?>
                <?php echo \yii\bootstrap\Alert::widget([
                    'body'=>ArrayHelper::getValue(Yii::$app->session->getFlash('alert'), 'body'),
                    'options'=>ArrayHelper::getValue(Yii::$app->session->getFlash('alert'), 'options'),
                ])?>
            <?php endif; ?>
            <div class="login-card card-block auth-body mr-auto ml-auto">
                <div class="text-center">
                    <img  src="<?=Yii::getAlias('@chipassets')?>/images/MS_logo_basic_extended_horizontal.png" alt="MS_logo_basic_extended_horizontal.png">
                </div>
                <div class="auth-box">
                    <div class="row m-b-20">
                        <div class="col-md-12 col-lg-12 col-xl-12 m-b-20">
                            <ul class="nav nav-tabs md-tabs sign-tabs" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link active" data-toggle="tab" href="#personal" role="tab">Personal</a>
                                    <div class="slide"></div>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="<?=Url::to(['/user/sign-in/signup-business'])?>" >Business</a>
                                    <div class="slide"></div>
                                </li>
                            </ul>
                        </div>
                        <!-- Tab panes -->
                        <div class="tab-content card-block col-md-12 col-lg-12 col-xl-12">
                            <div class="tab-pane active" id="personal" role="tabpanel">
                                <?php $form = ActiveForm::begin(['id' => 'form-signup-personal', 'enableAjaxValidation' => true, 'enableClientValidation' => false]) ?>
                                <?php echo $form->field($model, 'type')->hiddenInput(['value'=> 1])->label(false)  ?>
                                <?php echo $form->field($model, 'username')->textInput(['placeholder' => 'Username'])->label(false) ?>
                                <?php echo $form->field($model, 'password')->passwordInput(['placeholder' => 'Password'])->label(false) ?>
                                <?php echo $form->field($model, 'email')->textInput(['placeholder' => 'Email'])->label(false) ?>
                                <?php echo $form->field($model, 'phone')->widget(\yii\widgets\MaskedInput::className(), [
                                    'mask' => '(999) 999-99-99',
                                ])->label(false) ?>
                                <?php echo $form->field($model, 'skype')->textInput(['placeholder' => 'Telegram login'])->label(false) ?>
                                <div class="form-group">
                                    <?php echo Html::submitButton(Yii::t('frontend', 'Signup'), ['class' => 'btn btn-primary', 'name' => 'signup-button']) ?>
                                </div>
                                <hr/>
                                <div class="form-group">
                                    <a href="/">Home</a>
                                </div>
                                <?php ActiveForm::end(); ?>
                            </div>
                            <div class="tab-pane" id="business" role="tabpanel">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
