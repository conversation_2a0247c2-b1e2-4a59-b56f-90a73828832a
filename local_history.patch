Index: common/commands/AlientechProcessProjectsCommand.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
--- common/commands/AlientechProcessProjectsCommand.php	(date 1623060247688)
+++ common/commands/AlientechProcessProjectsCommand.php	(date 1623060247688)
@@ -3,72 +3,14 @@
 namespace common\commands;
 
 use common\helpers\AlientechApiHelper;
-use common\helpers\AlientechApiKessHelper;
-use common\helpers\AlientechApiKtagHelper;
-use common\helpers\KessHelper;
-use common\helpers\MessageHelper;
-use common\helpers\ProjectHelper;
-use common\models\AlientechLog;
-use common\models\AlientechOperation;
-use common\models\ProjectFiles;
-use common\models\Projects;
 use trntv\bus\interfaces\SelfHandlingCommand;
 use Yii;
 use yii\base\BaseObject;
-use yii\httpclient\Client;
 
 class AlientechProcessProjectsCommand extends BaseObject implements SelfHandlingCommand
 {
 
-    const FileTypeOrigDec = 'orig_dec';
-    const ALIENTECH_USER_ID = 99;
-    const WEBMASTER_TEST_USER_ID = 2066;
-
-    const MAXIMUM_KESS_FILE_SLOTS = 3;
-    const MAXIMUM_KTAG_FILE_SLOTS = 3;
-
-    public static $clientApplicationGUID = 'c7f43304-624b-42b9-99f8-9d36c9f234d3';
-    public static $secretKey = '?,;NTnD8XD>5X9-b25YnTg';
-    public static $apiUrl = 'https://encodingapi.alientech.to';
-
-    public static $userPerformed = 'CL365';
-
-    public $accessToken = '';
-    public $isAuthorised = false;
-
-    const OperationTypes_KESSv2_Decoding = 1;
-    const OperationTypes_KESSv2_Encoding = 2;
-    const OperationTypes_K_TAG_Decoding = 3;
-    const OperationTypes_K_TAG_Encoding = 4;
-
-    const OperationStatus_InProgress = 0;
-    const OperationStatus_Completed = 1;
-
-    const OperationStep_decoding = 0;
-    const OperationStep_decoded = 1;
-    const OperationStep_file_downloaded = 2;
-    const OperationStep_uploaded_mod = 3;
-    const OperationStep_uploaded_mod_alien = 9;
-    const OperationStep_encoding_mod_ready = 4;
-    const OperationStep_encoding_mod_started = 8;
-    const OperationStep_encoded = 5;
-    const OperationStep_encoded_file_downloaded = 6;
-    const OperationStep_closed = 7;
-
-
-
-    const operationTypeKess = 36;
-    const operationTypeKtag = 37;
-
-    const NOT_PROCESSED = 0;
-    const ERROR_KTAG_Z_LOAD = 'KTAG_Z_LOAD';
-    const ERROR_KV2_INVALID_FILE = 'KV2_INVALID_FILE';
-
     public $files = [];
-    public $operation;
-
-    public $helper;
-    public $client;
 
     /**
      * Command init
@@ -76,22 +18,7 @@
     public function init()
     {
         $this->files = AlientechApiHelper::getOrigEncFiles();
-        $this->getClient();
 
-    }
-    //-------------------------------------ref-3--------------------------------------------------------------
-    /**
-     * REV-3 Получение клиента запроса
-     * @return Client
-     */
-    private function getClient():Client{
-        $this->client = new Client([
-            'baseUrl' => self::$apiUrl,
-            'contentLoggingMaxSize' => 1000000,
-            'responseConfig' => [
-                'format' => Client::FORMAT_JSON
-            ],
-        ]);
     }
 
     /**
@@ -100,1203 +27,14 @@
      */
     public function handle($command)
     {
-        $encFiles = AlientechApiHelper::getOrigEncFiles();
-
-        if (count($encFiles) > 0) {
-            $startRes = $this->startAlientechProjects($encFiles);
-            print_r('\r\n $startRes \r\n');
-            print_r($startRes);
-        }
-        $checkRes = $this->checkAlientechOperations();
-        print_r('\r\n $checkRes \r\n');
-        print_r($checkRes);
-    }
-
-    /**
-     * REV-3 Проверка на авторизацию
-     * @return bool
-     * @throws \yii\base\InvalidConfigException
-     */
-    private function authorise()
-    {
-        $this->accessToken = Yii::$app->session->get('accessToken', '');
-
-        if (!empty($this->accessToken)) {
-            return true;
-        } else {
-            return $this->autenticate();
-        }
-    }
-
-    /**
-     * REV-3 Метод авторизации
-     * @return bool
-     * @throws \yii\base\InvalidConfigException
-     */
-    public function autenticate():bool
-    {
-        $this->client->requestConfig = [
-            'format' => Client::FORMAT_JSON
-        ];
-
-        $response = $this->client->createRequest()
-            ->setMethod('POST')
-            ->setUrl('/api/access-tokens/request')
-            ->setData(['clientApplicationGUID' => self::$clientApplicationGUID, 'secretKey' => self::$secretKey])
-            ->send();
-
-        self::checkErrors($response);
-
-        $resp = $response->getContent();
-
-        self::logAlientechApi('autenticate', '/api/access-tokens/request', $resp);
-
-        $respData = json_decode($resp);
-
-        if (isset($respData->accessToken) && !empty($respData->accessToken)) {
-            Yii::$app->session->set('accessToken', $respData->accessToken);
-            $this->accessToken = $respData->accessToken;
-            return true;
-        }
-
-        return false;
-
-    }
-
-    //-------------------------------------ref-3--------------------------------------------------------------
-
-    private function startAlientechProjects(array $encFiles)
-    {
-        $this->logToFile(' --- startDecodeFiles -- ---');
-
-        $result = [];
-        foreach($encFiles as $file) {
-//       todo     проверить есть ли уже слот по этому проекту чтобы открыть его (в случае если клиент подгрузил еще один оригинальный файл)
-//            if (($operation = AlientechOperation::find()->where(['project_id' => $file->project_id])->one()) !== null) {
-//
-//            }
-//            $result[] = $file->project->readmethod_id == $this->>operationTypeKess;
-            $this->logToFile(' --- startDecodeFiles -- $file ---'.$file->id);
-
-            if ($file->project->readmethod_id == self::operationTypeKess) {
-                $this->helper = new KessHelper();
-                $this->logToFile(' --- startDecodeFiles -- $file -operationTypeKess--'.$file->id);
-                $result[] = $this->helper->decodeKessFile($file);
-            }
-
-            if ($file->project->readmethod_id == self::operationTypeKtag) {
-                $this->helper = new KtagHelper();
-                $this->logToFile(' --- startDecodeFiles -- $file -operationTypeKtag--'.$file->id);
-                $result[] = AlientechApiKtagHelper::decodeKtagFile($file);
-            }
-        }
-
-        return $result;
-    }
-
-    private function checkAlientechOperations()
-    {
-        $result = [];
-        $this->operation = AlientechOperation::find()
-            ->where(['processed' => 0])
-            ->andWhere(['NOT IN', 'step',  [self::OperationStep_encoded_file_downloaded, self::OperationStep_file_downloaded, self::OperationStep_closed]])
-            ->one(); // todo change
-
-        if (is_null($this->operation)) {
-
-            $slotsKess = AlientechApiKessHelper::getKessFileSlots();
-
-            if (count($slotsKess) > 0) {
-                foreach ($slotsKess as $slot) {
-                    if (!$slot->isClosed) {
-                        $result[] = AlientechApiKessHelper::closeKessFileSlot($slot->guid);
-                    }
-                }
-            }
-
-            $slotsKtag = AlientechApiKtagHelper::getFileSlots();
-
-            if (count($slotsKtag) > 0) {
-                foreach ($slotsKtag as $slot) {
-                    if (!$slot->isClosed) {
-                        $result[] = AlientechApiKtagHelper::closeKtagFileSlot($slot->guid);
-                    }
-                }
-            }
-
-            $this->logToFile(' --- closeAllSlots -- $operations---');
-            return $result;
-        }
-
-
-            $this->logToFile(' --- checkAlientechOperation -- $operation---'.$this->operation->id);
-
-        $this->updateOperation($this->operation, ['processed' => 1]);
-        $operationResult = $this->checkOperation();
-        $result[$this->operation->id] = $operationResult;
-
-        return $result;
-
-    }
-
-    /**
-     * REV-3 Метод проверки одной операции по работе с файлами
-     * Если операция завершена - обновляем запись в бд
-     * @return bool|mixed
-     * @throws \Exception
-     */
-    private function checkOperation()
-    {
-
-        $result = ['success' => false];
-
-        $this->logToFile(' --- checkOperation --  step---'.$this->operation->step, $this->operation);
-
-        if (empty($this->operation->result_data)) {
-
-            $this->logToFile(' --- checkOperation --  OperationStatus_InProgress--result_data-empty-'.$this->operation->id, $this->operation);
-
-            if (!empty($this->operation->operation_data)) {
-                $operationData = json_decode($this->operation->operation_data);
-
-                if(!$operationData->isCompleted) {
-
-                    $this->logToFile(' --- checkOperation --  OperationStatus_InProgress---'.$this->operation->id, $this->operation);
-
-                    if ($this->tryCheckAlientechOperation($this->operation, $operationData)){
-                        $result = ['tryCheckAlientechOperation' => true];
-                    }
-                } else {
-                    if (isset($operationData->error)) {
-                        if ($operationData->error->errorName) {
-                            $errorName = $operationData->error->errorName;
-                            if (!empty($errorName) && ($errorName == self::ERROR_KTAG_Z_LOAD || $errorName == self::ERROR_KV2_INVALID_FILE)) {
-                                $project = $this->operation->project;
-                                $shouldSaveProject = false;
-                                if (($project->readmethod_id == $this->operation->type) && ($project->readmethod_id == self::operationTypeKess)) {
-                                    $project->setAttribute('readmethod_id', self::operationTypeKtag);
-                                    $shouldSaveProject = true;
-                                } elseif(($project->readmethod_id == $this->operation->type) && ($project->readmethod_id == self::operationTypeKtag)) {
-                                    $project->setAttribute('readmethod_id', self::operationTypeKess);
-                                    $shouldSaveProject = true;
-                                }
-                                if ($shouldSaveProject){
-                                    if($project->save(false)){
-                                        ProjectFiles::updateAll(['alientech_operation_id' => null], 'alientech_operation_id = '.$this->operation->id);
-                                        AlientechOperation::deleteAll(['id' => $this->operation->id]);
-                                    }
-                                }
-                            }
-                        }
-                    }
-                }
-            }
-
-        } else {
-
-            $resultData = json_decode($this->operation->result_data);
-
-            if ($this->operation->step == self::OperationStep_decoded){
-
-                $this->logToFile(' --- checkOperation --  OperationStep_decoded---'.$this->operation->id, $this->operation);
-
-                if (!$resultData->isSuccessful && $resultData->hasFailed) {
-                    $this->logToFile(' --- checkOperation --  !$resultData->isSuccessful && $resultData->hasFailed---'.$this->operation->id, $this->operation);
-                    return $result;
-                }
-//                if ($operation->type == $this->>operationTypeKess) {
-//                    if (!AlientechApiKessHelper::checkCountOpenKessFileSlots()) {
-//                        $this->>setMessageToOperationFile($operation, 'TOO_MANY_OPEN_KESSV2_FILE_SLOTS', true);
-//                        $result['message'][] = 'TOO_MANY_OPEN_KESSV2_FILE_SLOTS';
-//                        $result['closeKessFileSlot'] = AlientechApiKessHelper::closeKessFileSlot($resultData->slotGUID, $operation);
-//                        $this->>updateOperation($operation, ['processed' => 0]);
-//                        $this->>logToFile(' --- checkOperation --  TOO_MANY_OPEN_KESSV2_FILE_SLOTS---'.$operation->id, $operation);
-//                        return $result;
-//                    }
-//                }
-//                if ($operation->type == $this->>operationTypeKtag) {
-//                    if (!AlientechApiKtagHelper::checkCountOpenKtagFileSlots()) {
-//                        $this->>setMessageToOperationFile($operation, 'TOO_MANY_OPEN_K_TAG_FILE_SLOTS');
-//                        $result['message'][] = 'TOO_MANY_OPEN_K_TAG_FILE_SLOTS';
-//                        $result['closeKtagFileSlot'] = AlientechApiKtagHelper::closeKtagFileSlot($resultData->slotGUID);
-//                        $this->>updateOperation($operation, ['processed' => 0]);
-//                        $this->>logToFile(' --- checkOperation --  TOO_MANY_OPEN_K_TAG_FILE_SLOTS---'.$operation->id, $operation);
-//                        return $result;
-//                    }
-//                }
-
-
-                if (!$this->reOpenFileSlot($this->operation->type, $resultData->slotGUID, $this->operation)) {
-                    $this->setMessageToOperationFile($this->operation, 'slot reopen false');
-                    $result['message'][] = 'reopen false';
-                    $this->updateOperation($this->operation, ['processed' => 0]);
-                    $this->logToFile(' --- checkOperation --  slot reopen false---'.$this->operation->id, $this->operation);
-                    return $result;
-                }
-
-                $result['fileData'] = $this->downloadDecodedFiles($this->operation, $resultData);
-
-                $this->tryCloseSlot($this->operation);
-            }
-
-            //        модифицированный файл загружен и надо запустить его отправку на сервер
-//            if ($operation->step == $this->>OperationStep_uploaded_mod) {
-//                $result = $this->>uploadModFileToAlientech($operation);
-//            }
-
-            //        модифицированный файл загружен и надо запустить его кодировку на сервере
-            if ($this->operation->step == self::OperationStep_encoding_mod_ready) {
-                $this->logToFile(' --- checkOperation --  OperationStep_encoding_mod_ready---'.$this->operation->id, $this->operation);
-                if ($this->operation->completed == self::OperationStep_uploaded_mod) {
-                    $this->logToFile(' --- checkOperation --  OperationStep_encoding_mod_ready-completed == $this->>OperationStep_uploaded_mod--', $this->operation);
-                    $result['uploadModFilesToAlientech'] = $this->uploadModFilesToAlientech($this->operation);
-                    $result['encodeModFiles'] = $this->encodeModFiles($this->operation);
-                }
-                $this->tryCloseSlot($this->operation);
-            }
-
-            //        модифицированный файл кодируется на сервере и проверяется операция
-            if ($this->operation->step == self::OperationStep_encoding_mod_started){
-                $this->logToFile(' --- checkOperation --  OperationStep_encoding_mod_started---'.$this->operation->id, $this->operation);
-
-                if ($this->timeoutOperationCheck($resultData)) {
-                    $respData = $this->checkAlientechOperation($resultData->guid);
-                    if (!$respData->isSuccessful || $respData->hasFailed) {
-                        $this->setMessageToOperationFile($this->operation, $respData);
-                    } else { //&& $respData->asyncOperationType !== $this->>OperationTypes_KESSv2_Decoding todo может быть ошибка, надо проверять
-                        if ($respData->status == self::OperationStatus_Completed && $respData->isCompleted && $respData->asyncOperationType !== self::OperationTypes_KESSv2_Decoding && $respData->asyncOperationType !== self::OperationTypes_K_TAG_Decoding) {
-                            $this->completeAlientechOperation($this->operation, $respData, self::OperationStep_encoded);
-                        } else {
-                            $result[] = $respData;
-                        }
-                    }
-                }
-                $this->tryCloseSlot($this->operation);
-            }
-
-//        модифицированный файл закодирован сервером
-            if ($this->operation->step == self::OperationStep_encoded){
-
-                $this->logToFile(' --- checkOperation --  OperationStep_encoded---'.$this->operation->id, $this->operation);
-
-                if ($resultData->isSuccessful) {
-                    $result = $this->downloadEncodedFiles($this->operation);
-                }
-                $this->tryCloseSlot($this->operation);
-            }
-
-        }
-
-
-        $this->updateOperation($this->operation, ['processed' => 0]);
-
-        $this->logToFile(' --- checkOperation-end-operation-'.$this->operation->id, $this->operation);
-
-        return $result;
-
-    }
-
-    /**
-     * REV-3 Обновляет данные об операции в бд
-     * @param null $operation
-     * @param null $respData
-     * @return bool
-     */
-    private function updateOperation(AlientechOperation $operation = null, $data = []):array
-    {
-        $this->logToFile(' --- updateOperation --- ', $operation);
-
-        $result = [];
-
-        if (is_null($operation)) {
-            return $result;
-        }
-
-        $operation->setAttributes($data);
-
-        if (!$operation->save()) {
-            $this->logToFile(' --- updateOperation -save-error- '.json_encode($operation->errors), $operation);
-            return $operation->errors;
-        }
-
-        return ['status' => 'success'];
-    }
-
-    /**
-     * REV-3 логирование
-     */
-    private function logToFile($logString = '')
-    {
-        if (!is_null($this->operation)) {
-            error_log(date('Y-m-d H:i:s').' -- '.$this->operation->id.' -- '.$logString."\r\n", 3, Yii::getAlias('@storage') . '/startalientech.log');
-            echo date('Y-m-d H:i:s').' -- '.$this->operation->id.' -- '.$logString."\r\n";
-        } else {
-            error_log(date('Y-m-d H:i:s').' -- '.$logString."\r\n", 3, Yii::getAlias('@storage') . '/startalientech.log');
-            echo date('Y-m-d H:i:s').' --  -- '.$logString."\r\n";
-        }
-    }
-
-    /**
-     * REV-2 Дергаем операцию
-     * @param AlientechOperation|null $operation
-     * @param null $operationData
-     * @return bool
-     * @throws \yii\base\InvalidConfigException
-     * @throws \yii\httpclient\Exception
-     */
-    private function tryCheckAlientechOperation(AlientechOperation $operation = null, $operationData = null)
-    {
-        if ($this->timeoutOperationCheck($operationData)) {
-
-            $this->logToFile(' --- checkOperation -timeoutOperationCheck-  OperationStatus_InProgress---'.$operation->id, $operation);
-
-            $respData = self::checkAlientechOperation($operationData->guid);
-
-            if (!$respData->isSuccessful || $respData->hasFailed) {
-                self::updateOperation($operation, ['operation_data' => json_encode($respData), 'processed' => 0]);
-                self::setMessageToOperationFile($operation, $respData, true);
-                return false;
-            } elseif ($respData->status == self::OperationStatus_Completed && $respData->isCompleted) {
-                self::updateOperation($operation, ['operation_data' => json_encode($respData), 'processed' => 0]);
-                self::setMessageToOperationFile($operation, 'Decoding...', true);
-                self::completeAlientechOperation($operation, $respData);
-                return true;
-            }
-            return false;
-        }
-        return false;
-    }
-
-//-------------------------------------ref-2--------------------------------------------------------------
-
-
-    /**
-     * REV-2 метод логирования всех обращений к апи алиентеч
-     * @param null $guid
-     * @param null $url
-     * @param null $data
-     */
-    public function logAlientechApi($guid = null, $url = null, $data = null):void
-    {
-        $this->logToFile(' --- logAlientechApi --  ---'.$guid.$url);
-
-        if (!is_string($data)) {
-            $data = json_encode($data);
-        }
-
-        $operationLog = new AlientechLog();
-        $operationLog->setAttributes([
-            'guid' => $guid ?? '',
-            'url' => $url ?? '',
-            'resp_data' => $data ?? '',
-        ]);
-
-        $operationLog->save();
-
-        $this->logToFile(' --- $operationLog->save() --  ---'.$operationLog->id);
-
-    }
-
-
-
-
-
-    /**
-     * REV-2  проверка, можно ли дергать операцию по таймауту
-     * @param $operationData
-     * @return bool
-     * @throws \Exception
-     */
-    public function tryCloseSlot(AlientechOperation $operation):bool
-    {
-        $resultData = json_decode($operation->result_data);
-        if ($operation->type == self::operationTypeKess) {
-            return AlientechApiKessHelper::closeKessFileSlot($resultData->slotGUID, $operation);
-        }
-        if ($operation->type == self::operationTypeKtag) {
-            return AlientechApiKtagHelper::closeKtagFileSlot($resultData->slotGUID, $operation);
-        }
-    }
-
-    /**
-     * REV-2  проверка, можно ли дергать операцию по таймауту
-     * @param $operationData
-     * @return bool
-     * @throws \Exception
-     */
-    public function timeoutOperationCheck($operationData):bool
-    {
-        $startedOn = new \DateTime($operationData->startedOn);
-        $dateNow = new \DateTime('now');
-        $dateDiff = date_diff($dateNow, $startedOn);
-        if ($dateDiff->s >= $operationData->recommendedPollingInterval) {
-            return true;
-        }
-        return false;
-    }
-
-    /**
-     * REV-2 Метод проверяет операцию с запросом информации на сервере Alientech
-     * @param string $guid
-     * @return bool|mixed
-     * @throws \yii\base\InvalidConfigException
-     * @throws \yii\httpclient\Exception
-     */
-    public function checkAlientechOperation($guid = '')
-    {
-        $result = null;
-
-        if (empty($guid)) {
-            return $result;
-        }
-
-        if (self::authorise()) {
-            $url = '/api/async-operations/'.$guid;
-            $client = self::getClient();
-
-            $response = $client->createRequest()
-                ->setMethod('GET')
-                ->setHeaders(['X-Alientech-ReCodAPI-LLC' => self::$accessToken])
-                ->setUrl($url)
-                ->send();
-
-            $resp = $response->getContent();
-
-            $respData = json_decode($resp);
-
-            self::logAlientechApi($guid, $url, $resp);
-
-            $result = $respData;
-        }
-
-        return $result;
-    }
-
-    /**
-     * REV-2 Отправка уведомления в проект
-     * @param AlientechOperation $operation
-     * @param null $respData
-     */
-    public function setMessageToOperationFile(AlientechOperation $operation = null, $respData = null, $onlySys = true)
-    {
-        if (!is_null($operation)) {
-            $this->logToFile(' --- setMessageToOperationFile--operation-'.$operation->id, $operation);
-
-            $fileMessage = '';
-
-            if (!empty($respData->error)) {
-                $fileMessage = $respData->error->errorName;
-            } else {
-                $fileMessage = $respData;
-            }
-
-            $projectFile = ProjectFiles::findOne($operation->project_file_id);
-
-            if (!empty($fileMessage)) {
-
-                ProjectHelper::createProjectMessage($operation->project_id,
-                    'Alientech Message',
-                    $fileMessage,
-                    $fileMessage,
-                    1, 0, 0, 0, MessageHelper::TYPE_SYS, ['send_to' => 'admin']);
-                $this->logToFile(' --- createProjectMessage-TYPE_SYS-operation-'.$operation->id, $operation);
-            }
-
-            if (!$onlySys) {
-
-                if (!empty($respData->error)) {
-                    $projectFile->setAttribute('params', $fileMessage);
-                    //                $options['send_to'] = MessageHelper::getAdministrators();
-                } else {
-                    $projectFile->setAttribute('params', $fileMessage);
-                }
-
-                if ($projectFile->save(false)) {
-                    $this->logToFile(' --- $projectFile->save(false)-operation-'.$operation->id, $operation);
-                } else {
-                    $this->logToFile(' --- $projectFile->save(false)-error-operation-');
-                }
-
-                $project = Projects::findOne($operation->project_id);
-                $project->setAttribute('status_admin', ProjectHelper::STATUS_CHANGED);
-
-                if ($project->save(false)) {
-                    $this->logToFile(' --- $project->save(false)-STATUS_CHANGED-operation-'.$operation->id, $operation);
-                } else {
-                    $this->logToFile(' --- $project->save(false)-STATUS_CHANGED-error-operation-'.json_encode($project->errors), $operation);
-                }
-
-                //            ProjectHelper::createProjectMessage($operation->project_id, 'Reload', 'Reload', 'Reload', 0, 0, 0 , 0, MessageHelper::TYPE_RELOAD, ['send_to' => 'admin']);
-                $this->logToFile(' --- createProjectMessage-TYPE_RELOAD-operation-'.$operation->id, $operation);
-
-            }
 
-        } else {
-            $this->logToFile(' --- setMessageToOperationFile--operation-isNull');
+//        error_log(date('Y-m-d H:i:s').' --- actionStartAlientechProcess -- count($encFiles) ---'.count($this->files)."\r\n", 3, Yii::getAlias('@storage') . '/startalientech.log');
 
-        }
-
-    }
-
-    /**
-     * REV-2 Обновляет данные об операции в бд
-     * @param null $operation
-     * @param null $respData
-     * @return bool
-     */
-    public function completeAlientechOperation(AlientechOperation $operation = null, $respData = null, $step = self::OperationStep_decoded)
-    {
-        $this->logToFile(' --- completeAlientechOperation--step-'.$step, $operation);
-
-        $operation->setAttributes([
-            'result_data' => json_encode($respData),
-            'completed' => 1,
-            'step' => $step,
-        ]);
-
-        if ($operation->save(false)) {
-            $this->logToFile(' --- $operation->save(false)-completed-operation-'.$operation->id, $operation);
+        if (count($this->files) > 0) {
+            return AlientechApiHelper::startDecodeFiles($this->files);
         } else {
-            $this->logToFile(' --- $operation->save(false)-completed-error-operation-'.json_encode($operation->errors), $operation);
+            return ['no files'];
         }
-    }
-
-
-    /**
-     * REV-2 переоткрываем закрытый слот
-     * @param $type
-     * @param $slotGuid
-     * @return bool
-     * @throws \yii\base\InvalidConfigException
-     * @throws \yii\httpclient\Exception
-     */
-    public function reOpenFileSlot($type, $slotGuid, $operation):bool
-    {
-        $this->logToFile(' --- reOpenFileSlot--$type-'.$type, $operation);
-
-        if ($type == self::operationTypeKess) {
-            return AlientechApiKessHelper::tryReOpenKessFileSlot($slotGuid, $operation);
-        }
-
-        if ($type == self::operationTypeKtag) {
-            return AlientechApiKtagHelper::tryReOpenKtagFileSlot($slotGuid, $operation);
-        }
-    }
-
-    /**
-     * REV-2 Скачиваем декодированный файл
-     * @param AlientechOperation $operation
-     * @param $resultData
-     * @return array
-     * @throws \yii\base\Exception
-     * @throws \yii\base\InvalidConfigException
-     * @throws \yii\httpclient\Exception
-     */
-    public function downloadDecodedFiles(AlientechOperation $operation, $resultData):array
-    {
-
-        $this->logToFile(' --- downloadDecodedFiles--$operation-'.$operation->id, $operation);
-
-        $result = [];
-
-        if ($resultData->isSuccessful) {
-
-            $step = self::OperationStep_file_downloaded;
-
-            $fileData = $resultData->result;
-
-            $downloadedFileData = [];
-
-            if ($operation->type == self::operationTypeKess) {
-                $downloadedFileData = AlientechApiKessHelper::downloadKessFile($operation, $fileData, null, $fileData->decodedFileURL, $step);
-            }
-
-            if ($operation->type == self::operationTypeKtag) {
-                $downloadedFileData = AlientechApiKtagHelper::downloadKtagFile($operation, $fileData, null, null, $step);
-            }
-
-            $result = $downloadedFileData;
-
-            if($downloadedFileData['success']) {
-
-                $fileInfo = (array)$fileData->information;
-
-                $content = '';
-
-                foreach ($fileInfo as $key => $value) {
-                    $content .= $key . ' : ' . $value . '<br>';
-                }
-
-                $messageStart = 'Decoded file downloaded ';
-
-                $result['downloadData'] = $downloadedFileData;
-
-                $systemNotes = 0;
-
-                foreach ($downloadedFileData['filesData'] as $downloadedFile) {
-                    $projectFile = new ProjectFiles();
-                    $projectFile->setAttributes([
-                        'type' => 'external',
-                        'title' => $downloadedFile['fileName'],
-                        'component_name' => $downloadedFile['component'] ?? null,
-                        'project_id' => $operation->project_id,
-                        'file_id' => $operation->project_file_id,
-                        'alientech_operation_id' => $operation->id,
-                        'file_type' => ProjectHelper::FILE_TYPE_ORIGINAL_DECODED,
-                        'path' => $downloadedFile['filePath'],
-                        'filename' => $downloadedFile['fileName'],
-                        'hash' => yii::$app->security->generateRandomString(12),
-                    ]);
-
-                    if ($projectFile->save(false)) {
-
-                        $this->logToFile(' ---downloadDecodedFiles $projectFile->save--$projectFile-'.$projectFile->id, $operation);
-
-                        $result['projectFiles'][] = $projectFile->id;
-
-                        self::setMessageToOperationFile($operation, 'Decoding... Success!', true);
-
-                        $this->logToFile(' ---downloadDecodedFiles $parentFile->save--$parentFile-'.$operation->project_file_id, $operation);
-
-                        if (empty($systemNotes)) {
-                            ProjectHelper::createProjectMessage($operation->project_id,
-                                $messageStart,
-                                $messageStart . $downloadedFile['fileName'],
-                                $content,
-                                1, 0, 0, 0, MessageHelper::TYPE_SYS, ['send_to' => 'admin']);
-
-                            $this->logToFile(' ---downloadDecodedFiles ProjectHelper::createProjectMessage-TYPE_SYS-project_id-'.$operation->project_id);
-                        }
-
-                        $project = Projects::findOne($operation->project_id);
-
-                        ProjectHelper::createProjectMessage(
-                            $project->id,
-                            $messageStart,
-                            $messageStart
-//                            . $project->registration_num. ' '
-                            . $project->brand->title. ' '
-                            . $project->model->title. ' '
-                            . $project->year. ' '
-                            . $project->engine->title,
-                            $projectFile->title . (!empty($projectFile->extension)? '.' . $projectFile->extension : ''),
-//                            todo change for send sms and year instead generation
-                            0, 0, 0 , 0, MessageHelper::TYPE_NOTE, ['send_to' => 'admin']);
-
-                        $this->logToFile(' ---downloadDecodedFiles ProjectHelper::createProjectMessage-TYPE_NOTE-project_id-'.$operation->project_id, $operation);
-
-                        $result['ProjectMessages'][] = $messageStart . $downloadedFile['fileName'] .' downloaded';
-
-                        $systemNotes++;
-
-                    } else {
-                        $result['errors'] = $projectFile->errors;
-                        return $result;
-                    }
 
-                }
-
-
-                self::updateOperation($operation, ['step' => self::OperationStep_file_downloaded]);
-
-                $this->logToFile(' ---downloadDecodedFiles $operation->save-OperationStep_file_downloaded-$operation-'.$operation->id, $operation);
-
-                $project->setAttributes([
-                    'status_admin' => ProjectHelper::STATUS_CHANGED,
-                    'updated_by' => $project->created_by,
-                ]);
-                $project->save(false);
-
-                $this->logToFile(' ---downloadDecodedFiles $project->save--project_id-'.$project->id, $operation);
-
-                ProjectHelper::createProjectMessage($operation->project_id, 'Reload', 'Reload', 'Reload', 0, 0, 0 , 0, MessageHelper::TYPE_RELOAD, ['alientech' => true, 'send_to' => 'admin']);
-
-                $this->logToFile(' ---downloadDecodedFiles ProjectHelper::createProjectMessage-TYPE_RELOAD-project_id-'.$operation->project_id, $operation);
-
-            }
-        } else {
-            if ($resultData->hasFailed) {
-
-                $result['message'] = $resultData->error->errorName;
-
-                ProjectHelper::createProjectMessage(
-                    $operation->project_id,
-                    $operation->projectFile->title . ' DECODE ERROR',
-                    $resultData->error->errorName,
-                    $resultData->error->errorName,
-                    1, 0, 0, 0, MessageHelper::TYPE_SYS, ['alientech' =>true, 'send_to' => 'admin']);
-
-                $this->logToFile(' ---downloadDecodedFiles $resultData->hasFailed ProjectHelper::createProjectMessage-project_id-'.$operation->project_id, $operation);
-
-                $project = Projects::findOne($operation->project_id);
-
-                $project->setAttributes([
-                    'status_admin' => ProjectHelper::STATUS_CHANGED,
-                ]);
-                $project->save();
-
-                $this->logToFile(' ---downloadDecodedFiles $project->save--project_id-'.$project->id, $operation);
-
-                self::setMessageToOperationFile($operation, 'Decoding... Error!', true);
-
-                $this->logToFile(' ---downloadDecodedFiles hasFailed-operation-'.$operation->id, $operation);
-
-                $this->logToFile(' ---downloadDecodedFiles  $resultData->hasFailed $operation->save--$operation-'.$operation->id, $operation);
-
-                ProjectHelper::createProjectMessage($operation->project_id, 'Reload', 'Reload', 'Reload', 0, 0, 0 , 0, MessageHelper::TYPE_RELOAD, ['alientech' => true, 'send_to' => 'admin']);
-
-                $this->logToFile(' ---downloadDecodedFiles $resultData->hasFailed ProjectHelper::createProjectMessage-Reload-project_id-'.$operation->project_id, $operation);
-
-            }
-        }
-
-        return $result;
-
-    }
-
-    /**
-     * REV-2 Проверка на возможность кодирования загруженного ранее файла
-     * @param AlientechOperation|null $operation
-     * @return array
-     * @throws \yii\base\InvalidConfigException
-     * @throws \yii\httpclient\Exception
-     */
-    public function encodeModFiles(?AlientechOperation $operation)
-    {
-        $this->logToFile(' --- encodeModFiles -- $operation ---'.$operation->id, $operation);
-
-        $result = ['status' => 'error'];
-
-        $resultData = json_decode($operation->result_data);
-        if (!$resultData->isCompleted) {
-            $result['data'] = self::checkAlientechOperation($resultData->guid);
-            self::updateOperation($operation, ['result_data' => json_encode($result['data']), 'processed' => 0]);
-            return $result;
-        }
-
-        if (!$resultData->isSuccessful && $resultData->hasFailed) {
-            $result['data'] = $resultData;
-            return $result;
-        }
-
-//        if ($operation->type == self::operationTypeKess) {
-//            if (!AlientechApiKessHelper::checkCountOpenKessFileSlots()) {
-//                self::setMessageToOperationFile($operation, 'TOO_MANY_OPEN_KESSV2_FILE_SLOTS', true);
-//                $result['message'][] = 'TOO_MANY_OPEN_KESSV2_FILE_SLOTS';
-//                $this->logToFile(' --- checkOperation --  TOO_MANY_OPEN_KESSV2_FILE_SLOTS---'.$operation->id, $operation);
-//                AlientechApiKessHelper::closeKessFileSlot($resultData->slotGUID, $operation);
-//                self::updateOperation($operation, ['processed' => 0]);
-//                return $result;
-//            }
-//        }
-//        if ($operation->type == self::operationTypeKtag) {
-//            if (!AlientechApiKtagHelper::checkCountOpenKtagFileSlots()) {
-//                self::setMessageToOperationFile($operation, 'TOO_MANY_OPEN_K_TAG_FILE_SLOTS');
-//                $result['message'][] = 'TOO_MANY_OPEN_K_TAG_FILE_SLOTS';
-//                $this->logToFile(' --- checkOperation --  TOO_MANY_OPEN_K_TAG_FILE_SLOTS---'.$operation->id, $operation);
-//                AlientechApiKtagHelper::closeKtagFileSlot($resultData->slotGUID);
-//                self::updateOperation($operation, ['processed' => 0]);
-//                return $result;
-//            }
-//        }
-
-        if (!self::reOpenFileSlot($operation->type, $resultData->slotGUID, $operation)) {
-            $result['message'][] = 'reopen false';
-            self::setMessageToOperationFile($operation, 'reopen false');
-            return $result;
-        }
-
-        $result['fileData'] = self::encodeModFile($operation, $resultData);
-
-        $result['status'] = 'success';
-
-        return $result;
-
-    }
-
-    /**
-     * REV-2 Кодирование загруженного ранее файла
-     * @param AlientechOperation $operation
-     * @param $resultData
-     * @return array
-     * @throws \yii\base\InvalidConfigException
-     */
-    private static function encodeModFile(AlientechOperation $operation, $resultData)
-    {
-        $this->logToFile(' --- encodeModFile -- $operation ---'.$operation->id, $operation);
-
-        $result = ['status' => 'error'];
-
-        if ($operation->type == self::operationTypeKess) {
-
-            $fileData = $resultData->result;
-
-            $encodeResult = AlientechApiKessHelper::encodeModKessFile($fileData, $operation);
-
-            $result['encodeResult'] = $encodeResult;
-
-            $result['closedSlot'] = AlientechApiKessHelper::closeKessFileSlot($encodeResult->slotGUID, $operation);
-
-        }
-
-        if ($operation->type == self::operationTypeKtag) {
-
-            $fileResult = $resultData->result;
-
-            $encodeResult = AlientechApiKtagHelper::encodeModKtagFile($fileResult, $operation);
-
-            $result['encodeResult'] = $encodeResult;
-
-            $result['closedSlot'] = AlientechApiKtagHelper::closeKtagFileSlot($fileResult->ktagFileSlotGUID, $operation);
-
-        }
-
-        $operation->result_data = json_encode($encodeResult);
-        $operation->step = self::OperationStep_encoding_mod_started;
-        $operation->save(false);
-        return $result;
-    }
-
-    /**
-     * REV-2 Запуск скачивания кодированного ранее файла
-     * @param AlientechOperation $operation
-     * @return mixed
-     */
-    private static function downloadEncodedFiles(AlientechOperation $operation)
-    {
-        if ($operation->type == self::operationTypeKess) {
-            return AlientechApiKessHelper::downloadEncodedFile($operation);
-        }
-        if ($operation->type == self::operationTypeKtag) {
-            return AlientechApiKtagHelper::downloadEncodedFile($operation);
-        }
-    }
-
-    /**
-     * REV-2 Создание кодированного файла и добавление в проект
-     * @param AlientechOperation $operation
-     * @param array $downloadedFileData
-     * @return ProjectFiles
-     * @throws \yii\base\Exception
-     */
-    public function createModEncodedFile(AlientechOperation $operation, array $downloadedFileData)
-    {
-        $projectFile = new ProjectFiles();
-        $projectFile->setAttributes([
-            'type' => 'external',
-            'title' => $downloadedFileData['fileName'],
-            'project_id' => $operation->project_id,
-            'file_id' => $operation->project_file_id,
-            'can_download' => 1,
-            'alientech_operation_id' => $operation->id,
-            'file_type' => ProjectHelper::FILE_TYPE_MODIFIED_ENCODED,
-            'path' => $downloadedFileData['filePath'],
-            'filename' => $downloadedFileData['fileName'],
-            'hash' => yii::$app->security->generateRandomString(12),
-        ]);
-        $projectFile->save(false);
-
-        $this->logToFile(' --- createModEncodedFile--operation-'.$operation->id, $operation);
-
-    }
-
-
-    /**
-     * REV-2 Загрузка мод файла в проект
-     * @param ProjectFiles $projectFile
-     * @return array|bool|mixed
-     * @throws \yii\base\InvalidConfigException
-     */
-    public function uploadModProjectFile(ProjectFiles $projectFile)
-    {
-
-        $this->logToFile(' --- uploadModFile -- $projectFile---'.$projectFile->id, $projectFile->alientechOperation);
-//        $result = [];
-//        $result['status'] = 'error';
-        self::setMessageToOperationFile($projectFile->alientechOperation, '');
-
-        return self::updateOperation($projectFile->alientechOperation, ['step' => self::OperationStep_uploaded_mod, 'completed' => self::OperationStep_uploaded_mod, 'processed' => self::NOT_PROCESSED]);
-
-//        if ($projectFile->alientechOperation->type == self::operationTypeKess) {
-//            $result = AlientechApiKessHelper::uploadModKessFile($projectFile);
-//        }
-//
-//        if ($projectFile->alientechOperation->type == self::operationTypeKtag) {
-//            $result = AlientechApiKtagHelper::uploadModKtagComponentFile($projectFile);
-//        }
-//
-//        if (!empty($result)) {
-//
-//            self::updateOperation($projectFile->alientechOperation, [
-//                'step' => self::OperationStep_uploaded_mod,
-//                'processed' => 0,
-//            ]);
-//
-//            $this->logToFile(' --- uploadModFile -- $projectFile->alientechOperation->save(false)---'.$projectFile->alientechOperation->id);
-//
-//        }
-
-//        return $result;
-
-    }
-
-    public function uploadModFile(ProjectFiles $projectFile)
-    {
-
-        if ($projectFile->alientechOperation->type == self::operationTypeKess) {
-            $result = AlientechApiKessHelper::uploadModKessFile($projectFile, $projectFile->alientechOperation);
-        }
-
-        if ($projectFile->alientechOperation->type == self::operationTypeKtag) {
-            $result = AlientechApiKtagHelper::uploadModKtagComponentFile($projectFile, $projectFile->alientechOperation);
-        }
-
-        if (!empty($result)) {
-
-            self::updateOperation($projectFile->alientechOperation, [
-                'step' => self::OperationStep_encoding_mod_ready,
-                'processed' => 0,
-                'completed' => self::OperationStep_encoding_mod_ready,
-            ]);
-
-            $this->logToFile(' --- uploadModFile -- $projectFile->alientechOperation->save(false)---'.$projectFile->alientechOperation->id, $projectFile->alientechOperation);
-
-        }
-
-        return $result;
-
-    }
-
-    public function uploadModFilesToAlientech(AlientechOperation $operation)
-    {
-        $result = [];
-
-        $files = ProjectFiles::find()
-            ->where([
-                'alientech_operation_id' =>$operation->id,
-                'file_type' => ProjectHelper::FILE_TYPE_MODIFIED_DECODED,
-                'isDeleted' => 0
-            ])
-            ->all();
-        if (count($files) > 0) {
-            foreach ($files as $file) {
-                $result[] = self::uploadModFile($file);
-            }
-        }
-        return $result;
-    }
-    /**
-     * REV-2 Загрузка мод файла на сервер
-     * @param ProjectFiles $projectFile
-     * @return array|bool|mixed
-     * @throws \yii\base\InvalidConfigException
-     */
-//    public function uploadModFileToAlientech(AlientechOperation $operation)
-//    {
-//        $this->logToFile(' --- uploadModFileToAlientech -- $operation---'.$operation->id, $operation);
-//
-//        $result = [];
-//        $result['status'] = 'error';
-//
-//        if ($operation->type == self::operationTypeKess) {
-//            $result = AlientechApiKessHelper::uploadModKessFile($operation);
-//        }
-//
-//        if ($operation->type == self::operationTypeKtag) {
-//            $result = AlientechApiKtagHelper::uploadModKtagComponentFile($operation);
-//        }
-//
-//        if (!empty($result)) {
-//
-//            self::updateOperation($operation, [
-//                'step' => self::OperationStep_encoding_mod_ready,
-//                'processed' => 0,
-//                'completed' => self::OperationStep_encoding_mod_ready,
-//            ]);
-//
-//            $this->logToFile(' --- uploadModFileToAlientech -- $projectFile->alientechOperation->save(false)---'.$operation->id, $operation);
-//
-//        }
-//
-//        return $result;
-//
-//    }
-
-    /**
-     * REV-2 Обработка ответа от сервера
-     * @param Client $client
-     * @param \yii\httpclient\Response $response
-     * @param AlientechOperation $operation
-     * @param array $data
-     * @return mixed|null
-     */
-    public function checkAlientechResponse(Client $client, \yii\httpclient\Response $response, AlientechOperation $operation = null, $data = [], $needResponse = true, $onliSys = true)
-    {
-        $this->logToFile(' --- checkAlientechResponse --', $operation);
-
-        $resp = $response->getContent();
-
-        self::logAlientechApi($data[0], $data[1], $resp);
-
-        $respData = json_decode($resp);
-
-        if($needResponse && !isset($respData->guid)) {
-            self::setMessageToOperationFile($operation, $resp, $onliSys);
-            return null;
-        }
-
-        return $respData;
-
-    }
-
-    /**
-     * REV-2 метод получения всех оригинальных кодированных файлов в незакрытых проектах которые созданы с типом софта слейв
-     * @return array
-     */
-    public function getOrigEncFiles():array
-    {
-        $files =
-            ProjectFiles::find()
-                ->joinWith(['project' => function ($q) {
-                    $q->where('projects.status !='.ProjectHelper::STATUS_CLOSED);
-                }])
-                ->joinWith(['originalDecodedFiles decoded' => function ($q) {
-                    $q->where('decoded.id is null');
-                }])
-                ->with('project')
-                ->notDeleted()
-                ->where(['project_files.file_type' => ProjectHelper::FILE_TYPE_ORIGINAL_ENCODED])
-                ->andWhere('project_files.alientech_operation_id is null')
-                ->andWhere(['!=','projects.status', ProjectHelper::STATUS_CLOSED])
-                ->andWhere(['!=','projects.isDeleted', 1])
-                ->limit(5)
-//                ->createCommand()->getRawSql();
-                ->all();
-        return $files ?? [];
-    }
-
-
-    /**
-     * REV-2 Метод стартует декодирование новых оригинальных кодированных файлов
-     * берем файл из новосозданного проекта если он оригинальный кодированный
-     * это мы узнаем из проекта при создании по типу слейв устройства
-     * @param array $files
-     * @return array
-     */
-    public function startDecodeFiles(array $files):array
-    {
-        $this->logToFile(' --- startDecodeFiles -- ---');
-
-        $result = [];
-        foreach($files as $file) {
-//       todo     проверить есть ли уже слот по этому проекту чтобы открыть его (в случае если клиент подгрузил еще один оригинальный файл)
-//            if (($operation = AlientechOperation::find()->where(['project_id' => $file->project_id])->one()) !== null) {
-//
-//            }
-//            $result[] = $file->project->readmethod_id == self::operationTypeKess;
-            $this->logToFile(' --- startDecodeFiles -- $file ---'.$file->id);
-
-            if ($file->project->readmethod_id == self::operationTypeKess) {
-                $this->logToFile(' --- startDecodeFiles -- $file -operationTypeKess--'.$file->id);
-
-                $result[] = AlientechApiKessHelper::decodeKessFile($file);
-//                if (AlientechApiKessHelper::checkCountOpenKessFileSlots()) {
-////                    self::setMessageToOperationFile();
-////                    $file->setAttribute('params', 'TOO_MANY_OPEN_KESSV2_FILE_SLOTS');
-////                    $file->save(false);
-//
-////                    $project = Projects::findOne($file->project_id);
-////                    $project->setAttributes([
-////                        'status_admin' => ProjectHelper::STATUS_CHANGED,
-////                        'updated_by' => $project->created_by,
-////                    ]);
-////                    $project->save(false);
-////                    ProjectHelper::createProjectMessage($project->id,
-////                        'Alientech Message',
-////                        $file->params,
-////                        $file->params,
-////                        1, 0, 0, 0, MessageHelper::TYPE_SYS, ['send_to' => 'admin']);
-////
-////                    ProjectHelper::createProjectMessage($project->id, 'Reload', 'Reload', 'Reload', 0, 0, 0 , 0, MessageHelper::TYPE_RELOAD, ['send_to' => 'admin']);
-//
-////                    return ['TOO_MANY_OPEN_KESSV2_FILE_SLOTS'];
-//                } else {
-//                    $this->logToFile(' --- startDecodeFiles -- $file -TOO_MANY_OPEN_KESSV2_FILE_SLOTS--'.$file->id);
-//                    return ['TOO_MANY_OPEN_KESSV2_FILE_SLOTS'];
-//                }
-
-            }
-
-            if ($file->project->readmethod_id == self::operationTypeKtag) {
-                $this->logToFile(' --- startDecodeFiles -- $file -operationTypeKtag--'.$file->id);
-                $result[] = AlientechApiKtagHelper::decodeKtagFile($file);
-//                if (AlientechApiKtagHelper::checkCountOpenKtagFileSlots()) {
-////                    $file->setAttribute('params', 'TOO_MANY_OPEN_Ktag_FILE_SLOTS');
-////                    $file->save(false);
-////                    $project = Projects::findOne($file->project_id);
-////                    $project->setAttributes([
-////                        'status_admin' => ProjectHelper::STATUS_CHANGED,
-////                        'updated_by' => $project->created_by,
-////                    ]);
-////                    $project->save(false);
-////                    $project->save(false);
-////                    ProjectHelper::createProjectMessage($project->id,
-////                        'Alientech Message',
-////                        $file->params,
-////                        $file->params,
-////                        1, 0, 0, 0, MessageHelper::TYPE_SYS, ['send_to' => 'admin']);
-////
-////                    ProjectHelper::createProjectMessage($project->id, 'Reload', 'Reload', 'Reload', 0, 0, 0 , 0, MessageHelper::TYPE_RELOAD, ['send_to' => 'admin']);
-////
-////                    return ['TOO_MANY_OPEN_Ktag_FILE_SLOTS'];
-//                } else {
-//                    $this->logToFile(' --- startDecodeFiles -- $file -operationTypeKtag-TOO_MANY_OPEN_Ktag_FILE_SLOTS-'.$file->id);
-//                    return ['TOO_MANY_OPEN_Ktag_FILE_SLOTS'];
-//                }
-
-            }
-        }
-
-        return $result;
-    }
-
-    /**
-     * REV-2 Метод проверки результатов запроса на сервер
-     * @param $response
-     * @return array
-     */
-    public function checkErrors($response)
-    {
-        $this->logToFile(' --- checkErrors -- ---');
-        $resp = $response->getContent();
-
-        if (empty($resp)) {
-            return [
-                'status' => 'error',
-                'message' => 'empty response'
-            ];
-        }
-
-        if ($resp == 'TOO_MANY_OPEN_KESSV2_FILE_SLOTS') {
-            return [
-                'status' => 'error',
-                'message' => $resp
-            ];
-
-        }
-        if ($resp == 'KESSV2_FILE_SLOT_IS_CLOSED') {
-            return [
-                'status' => 'error',
-                'message' => $resp
-            ];
-
-        }
-        return [
-            'status' => 'success',
-            'message' => ''
-        ];
-
-    }
-
-    //------------------------------------/-ref-2--------------------------------------------------------------
+    }
 }
